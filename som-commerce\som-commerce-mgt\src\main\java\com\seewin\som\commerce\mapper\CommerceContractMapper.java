package com.seewin.som.commerce.mapper;

import com.seewin.som.commerce.entity.CommerceContract;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seewin.som.commerce.req.CommerceChargeContractListDto;
import com.seewin.som.commerce.req.CommerceContractListDto;
import com.seewin.som.commerce.req.CommerceContractRentFeeExportDto;
import com.seewin.som.commerce.resp.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 招商合同表-商铺 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
public interface CommerceContractMapper extends BaseMapper<CommerceContract> {

    CommerceContract getLastContract(@Param("roomId") Long roomId);

    List<CommerceContract> historyList(@Param("listDto") CommerceContractListDto listDto);

    String getMaxShopId(@Param("roomId") Long roomId);

    Long getShopIdCount(@Param("roomId") Long roomId);

    CommerceContract selectByRoomIdAndApproveStatus(@Param("roomId") Long roomId, @Param("approveStatus") Integer approveStatus);

    /**
     * TODO
     * <AUTHOR>
     * @date 2024/5/16 17:49
     * @param billMonth
     * @return java.util.List<com.seewin.som.commerce.resp.CommerceContractBillMonthVo>
     */
    List<CommerceContractBillMonthVo> selectContractByBillMonth(@Param("billMonth") String billMonth);

    List<CommerceContractArchiveVo> selectRentStartContract(@Param("date") LocalDate date);

    List<CommerceContract> selectContractApproveOvertime(@Param("date") LocalDate date);

    /**
     * 获取每月账单的需要的合同
     * <AUTHOR>
     * @date 2024/6/26 15:16
     * @param type
     * @return java.util.List<com.seewin.som.commerce.resp.CommerceContractMonthFeeVo>
     */
    List<CommerceContractMonthFeeVo> selectContractMonthFee(@Param("type") String type);

    /**
     * 本次合同的前一份合同信息
     * @param id
     * @return
     */
    CommerceContract getLastOneContract(Long id);

    List<CommerceEffectiveContractVo> selectEffectiveContract(@Param("currentDate")LocalDate currentDate);
    List<CommerceEffectiveContractVo> selectEffectiveContractByContractCode(@Param("contractCodeList")List<String> contractCodeList);

    CommerceEffectiveContractVo getRentFeeCalculateInfo(@Param("contractCode") String contractCode);

    List<CommerceContractRentFeeExportVo> getContractFeeList(@Param("dto") CommerceContractRentFeeExportDto dto);

    CommerceContractArchiveVo findSignContract(@Param("roomId") Long roomId);

    List<CommerceChargeContractListVo> listByReceivable(@Param("dto")CommerceChargeContractListDto dto);

    List<CommerceInvertContractListVo> getAllInvertContract();
}

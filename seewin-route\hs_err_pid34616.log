#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 202736 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=34616, tid=51732
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.7******.130-jcef (21.0.7+9) (build 21.0.7+9-b895.130)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.7******.130-jcef (21.0.7+9-b895.130, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitlab.seewintech.com': 

Host: Intel(R) Core(TM) i5-9400 CPU @ 2.90GHz, 6 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Mon Jul  7 10:09:58 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 1.514100 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x00000285fe9053d0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=51732, stack(0x0000008e9be00000,0x0000008e9bf00000) (1024K)]


Current CompileTask:
C2:1514 1083       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)

Stack: [0x0000008e9be00000,0x0000008e9bf00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e6049]
V  [jvm.dll+0x8c4343]
V  [jvm.dll+0x8c689e]
V  [jvm.dll+0x8c6f83]
V  [jvm.dll+0x289266]
V  [jvm.dll+0xc675d]
V  [jvm.dll+0xc6c93]
V  [jvm.dll+0x2fee70]
V  [jvm.dll+0x60cb49]
V  [jvm.dll+0x259a22]
V  [jvm.dll+0x259ddf]
V  [jvm.dll+0x252585]
V  [jvm.dll+0x24fdde]
V  [jvm.dll+0x1cd734]
V  [jvm.dll+0x25f76c]
V  [jvm.dll+0x25dcb6]
V  [jvm.dll+0x3ffa86]
V  [jvm.dll+0x86bea8]
V  [jvm.dll+0x6e480d]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000285ff32ba80, length=15, elements={
0x00000285f2d42b90, 0x00000285fe8f9650, 0x00000285fe8fafc0, 0x00000285fe8fda20,
0x00000285fe9011a0, 0x00000285fe901bf0, 0x00000285fe902640, 0x00000285fe9053d0,
0x00000285fe909a80, 0x00000285feb4f600, 0x00000285feb80530, 0x00000285fed4a850,
0x00000285ff1b60a0, 0x00000285ff3a7cc0, 0x00000285ff3924f0
}

Java Threads: ( => current thread )
  0x00000285f2d42b90 JavaThread "main"                              [_thread_blocked, id=17536, stack(0x0000008e9b000000,0x0000008e9b100000) (1024K)]
  0x00000285fe8f9650 JavaThread "Reference Handler"          daemon [_thread_blocked, id=4508, stack(0x0000008e9b800000,0x0000008e9b900000) (1024K)]
  0x00000285fe8fafc0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=51292, stack(0x0000008e9b900000,0x0000008e9ba00000) (1024K)]
  0x00000285fe8fda20 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=1904, stack(0x0000008e9ba00000,0x0000008e9bb00000) (1024K)]
  0x00000285fe9011a0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=35260, stack(0x0000008e9bb00000,0x0000008e9bc00000) (1024K)]
  0x00000285fe901bf0 JavaThread "Service Thread"             daemon [_thread_blocked, id=51056, stack(0x0000008e9bc00000,0x0000008e9bd00000) (1024K)]
  0x00000285fe902640 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=45844, stack(0x0000008e9bd00000,0x0000008e9be00000) (1024K)]
=>0x00000285fe9053d0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=51732, stack(0x0000008e9be00000,0x0000008e9bf00000) (1024K)]
  0x00000285fe909a80 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=52176, stack(0x0000008e9bf00000,0x0000008e9c000000) (1024K)]
  0x00000285feb4f600 JavaThread "Notification Thread"        daemon [_thread_blocked, id=45332, stack(0x0000008e9c000000,0x0000008e9c100000) (1024K)]
  0x00000285feb80530 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=34332, stack(0x0000008e9c100000,0x0000008e9c200000) (1024K)]
  0x00000285fed4a850 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=48836, stack(0x0000008e9c200000,0x0000008e9c300000) (1024K)]
  0x00000285ff1b60a0 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=51572, stack(0x0000008e9c300000,0x0000008e9c400000) (1024K)]
  0x00000285ff3a7cc0 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_vm, id=49708, stack(0x0000008e9c500000,0x0000008e9c600000) (1024K)]
  0x00000285ff3924f0 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=16804, stack(0x0000008e9c600000,0x0000008e9c700000) (1024K)]
Total: 15

Other Threads:
  0x00000285fe8d8540 VMThread "VM Thread"                           [id=21324, stack(0x0000008e9b700000,0x0000008e9b800000) (1024K)]
  0x00000285fe8c6900 WatcherThread "VM Periodic Task Thread"        [id=44416, stack(0x0000008e9b600000,0x0000008e9b700000) (1024K)]
  0x00000285f50c0b70 WorkerThread "GC Thread#0"                     [id=50792, stack(0x0000008e9b100000,0x0000008e9b200000) (1024K)]
  0x00000285f50d18d0 ConcurrentGCThread "G1 Main Marker"            [id=6884, stack(0x0000008e9b200000,0x0000008e9b300000) (1024K)]
  0x00000285f50d23d0 WorkerThread "G1 Conc#0"                       [id=47924, stack(0x0000008e9b300000,0x0000008e9b400000) (1024K)]
  0x00000285fe794180 ConcurrentGCThread "G1 Refine#0"               [id=24180, stack(0x0000008e9b400000,0x0000008e9b500000) (1024K)]
  0x00000285fe794c00 ConcurrentGCThread "G1 Service"                [id=39800, stack(0x0000008e9b500000,0x0000008e9b600000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0  2014 1083       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)
C2 CompilerThread1  2014 1094       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000702800000, size: 4056 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000028591000000-0x0000028591d10000-0x0000028591d10000), size 13697024, SharedBaseAddress: 0x0000028591000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000028592000000-0x00000285d2000000, reserved size: 1073741824
Narrow klass base: 0x0000028591000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 6 total, 6 available
 Memory: 16220M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4056M
 Pre-touch: Disabled
 Parallel Workers: 6
 Concurrent Workers: 2
 Concurrent Refinement Workers: 6
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 260096K, used 14336K [0x0000000702800000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 0 survivors (0K)
 Metaspace       used 8024K, committed 8320K, reserved 1114112K
  class space    used 915K, committed 1024K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|   1|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|   2|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Untracked 
|   3|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Untracked 
|   4|0x0000000703000000, 0x0000000703000000, 0x0000000703200000|  0%| F|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Untracked 
|   5|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Untracked 
|   6|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|   7|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|   8|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000| PB 0x0000000703800000| Untracked 
|   9|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  10|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  11|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  12|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  13|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  14|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  15|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  16|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  17|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  18|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  19|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  20|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  21|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  22|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|  23|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|  24|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  25|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  26|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  27|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  28|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  29|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  30|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  31|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  32|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  33|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  34|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked 
|  35|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked 
|  36|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  37|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  38|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  39|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  40|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  41|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  42|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  43|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  44|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  45|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  46|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  47|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  48|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  49|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  50|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  51|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  52|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  53|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  54|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  55|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  56|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  57|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  58|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  59|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  60|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  61|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  62|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked 
|  63|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  64|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  65|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked 
|  66|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  67|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  68|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  69|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  70|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  71|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  72|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  73|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  74|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  75|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  76|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  77|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  78|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  79|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
|  80|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
|  81|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
|  82|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
|  83|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
|  84|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
|  85|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
|  86|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
|  87|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
|  88|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
|  89|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
|  90|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
|  91|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
|  92|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
|  93|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
|  94|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
|  95|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
|  96|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked 
|  97|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
|  98|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
|  99|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
| 100|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
| 101|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
| 102|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
| 103|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
| 104|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked 
| 105|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked 
| 106|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Untracked 
| 107|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000| PB 0x000000070fe00000| Untracked 
| 108|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000| PB 0x0000000710000000| Untracked 
| 109|0x0000000710200000, 0x0000000710200000, 0x0000000710400000|  0%| F|  |TAMS 0x0000000710200000| PB 0x0000000710200000| Untracked 
| 110|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000| PB 0x0000000710400000| Untracked 
| 111|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000| PB 0x0000000710600000| Untracked 
| 112|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000| PB 0x0000000710800000| Untracked 
| 113|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000| PB 0x0000000710a00000| Untracked 
| 114|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000| PB 0x0000000710c00000| Untracked 
| 115|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000| PB 0x0000000710e00000| Untracked 
| 116|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000| PB 0x0000000711000000| Untracked 
| 117|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000| PB 0x0000000711200000| Untracked 
| 118|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000| PB 0x0000000711400000| Untracked 
| 119|0x0000000711600000, 0x0000000711733998, 0x0000000711800000| 60%| E|  |TAMS 0x0000000711600000| PB 0x0000000711600000| Complete 
| 120|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%| E|CS|TAMS 0x0000000711800000| PB 0x0000000711800000| Complete 
| 121|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| E|CS|TAMS 0x0000000711a00000| PB 0x0000000711a00000| Complete 
| 122|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| E|CS|TAMS 0x0000000711c00000| PB 0x0000000711c00000| Complete 
| 123|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| E|CS|TAMS 0x0000000711e00000| PB 0x0000000711e00000| Complete 
| 124|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| E|CS|TAMS 0x0000000712000000| PB 0x0000000712000000| Complete 
| 125|0x0000000712200000, 0x0000000712400000, 0x0000000712400000|100%| E|CS|TAMS 0x0000000712200000| PB 0x0000000712200000| Complete 
| 126|0x0000000712400000, 0x0000000712600000, 0x0000000712600000|100%| E|CS|TAMS 0x0000000712400000| PB 0x0000000712400000| Complete 

Card table byte_map: [0x00000285f99a0000,0x00000285fa190000] _byte_map_base: 0x00000285f618c000

Marking Bits: (CMBitMap*) 0x00000285f50c1270
 Bits: [0x00000285fa190000, 0x00000285fe0f0000)

Polling page: 0x00000285f4730000

Metaspace:

Usage:
  Non-class:      6.94 MB used.
      Class:    915.88 KB used.
       Both:      7.84 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.12 MB ( 11%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.00 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.12 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.42 MB
       Class:  15.00 MB
        Both:  23.42 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 224.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 130.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 379.
num_chunk_merges: 0.
num_chunk_splits: 218.
num_chunks_enlarged: 89.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=388Kb max_used=388Kb free=119611Kb
 bounds [0x0000028587ad0000, 0x0000028587d40000, 0x000002858f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=1707Kb max_used=1707Kb free=118293Kb
 bounds [0x0000028580000000, 0x0000028580270000, 0x0000028587530000]
CodeHeap 'non-nmethods': size=5760Kb used=1429Kb max_used=1458Kb free=4330Kb
 bounds [0x0000028587530000, 0x00000285877a0000, 0x0000028587ad0000]
 total_blobs=1613 nmethods=1098 adapters=420
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.244 Thread 0x00000285fe909a80 1091       3       sun.security.util.ArrayUtil::swap (15 bytes)
Event: 1.244 Thread 0x00000285fe909a80 nmethod 1091 0x00000285801a9510 code [0x00000285801a96c0, 0x00000285801a9808]
Event: 1.244 Thread 0x00000285fe909a80 1087       1       sun.security.util.math.intpoly.IntegerPolynomial$Element::getField (5 bytes)
Event: 1.244 Thread 0x00000285fe909a80 nmethod 1087 0x0000028587b2ca90 code [0x0000028587b2cc20, 0x0000028587b2ccf0]
Event: 1.245 Thread 0x00000285fed4a850 nmethod 1074 0x0000028587b2cd90 code [0x0000028587b2cf60, 0x0000028587b2d3f8]
Event: 1.245 Thread 0x00000285fed4a850 1078       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setSquare (52 bytes)
Event: 1.246 Thread 0x00000285fed4a850 nmethod 1078 0x0000028587b2d690 code [0x0000028587b2d840, 0x0000028587b2d990]
Event: 1.246 Thread 0x00000285fed4a850 1093       4       sun.security.ec.ECOperations$PointMultiplier::lookup (84 bytes)
Event: 1.246 Thread 0x00000285fe9053d0 nmethod 1089 0x0000028587b2da90 code [0x0000028587b2dc60, 0x0000028587b2e0f8]
Event: 1.246 Thread 0x00000285fe9053d0 1084       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setSquare (5 bytes)
Event: 1.247 Thread 0x00000285fe9053d0 nmethod 1084 0x0000028587b2e410 code [0x0000028587b2e5c0, 0x0000028587b2e710]
Event: 1.247 Thread 0x00000285fe9053d0 1080       4       sun.security.util.math.intpoly.IntegerPolynomialP256::square (613 bytes)
Event: 1.253 Thread 0x00000285fe909a80 1095       3       sun.security.util.math.intpoly.IntegerPolynomial::get0 (10 bytes)
Event: 1.505 Thread 0x00000285fe909a80 nmethod 1095 0x00000285801a9910 code [0x00000285801a9ac0, 0x00000285801a9cc8]
Event: 1.505 Thread 0x00000285fe909a80 1096       3       sun.security.util.math.intpoly.IntegerPolynomial::get0 (5 bytes)
Event: 1.505 Thread 0x00000285fe909a80 nmethod 1096 0x00000285801a9e10 code [0x00000285801a9fc0, 0x00000285801aa2b0]
Event: 1.505 Thread 0x00000285fe9053d0 nmethod 1080 0x0000028587b2e890 code [0x0000028587b2ea40, 0x0000028587b2ef98]
Event: 1.505 Thread 0x00000285fe9053d0 1097       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setAdditiveInverse (32 bytes)
Event: 1.508 Thread 0x00000285fe9053d0 nmethod 1097 0x0000028587b2f110 code [0x0000028587b2f2a0, 0x0000028587b2f490]
Event: 1.508 Thread 0x00000285fe9053d0 1083       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.020 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.dll
Event: 0.088 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\zip.dll

Deoptimization events (18 events):
Event: 0.759 Thread 0x00000285f2d42b90 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000028587adc9f0 relative=0x00000000000000d0
Event: 0.759 Thread 0x00000285f2d42b90 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000028587adc9f0 method=java.lang.String.charAt(I)C @ 4 c2
Event: 0.759 Thread 0x00000285f2d42b90 DEOPT PACKING pc=0x0000028587adc9f0 sp=0x0000008e9b0fd9c0
Event: 0.759 Thread 0x00000285f2d42b90 DEOPT UNPACKING pc=0x00000285875846a2 sp=0x0000008e9b0fd960 mode 2
Event: 0.767 Thread 0x00000285f2d42b90 DEOPT PACKING pc=0x0000028580039087 sp=0x0000008e9b0fdbe0
Event: 0.767 Thread 0x00000285f2d42b90 DEOPT UNPACKING pc=0x0000028587584e42 sp=0x0000008e9b0fd078 mode 0
Event: 0.779 Thread 0x00000285f2d42b90 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000028587aeecb0 relative=0x0000000000000090
Event: 0.779 Thread 0x00000285f2d42b90 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000028587aeecb0 method=jdk.internal.misc.Unsafe.convEndian(ZI)I @ 4 c2
Event: 0.779 Thread 0x00000285f2d42b90 DEOPT PACKING pc=0x0000028587aeecb0 sp=0x0000008e9b0fdbe0
Event: 0.779 Thread 0x00000285f2d42b90 DEOPT UNPACKING pc=0x00000285875846a2 sp=0x0000008e9b0fdb10 mode 2
Event: 0.837 Thread 0x00000285f2d42b90 DEOPT PACKING pc=0x00000285800119cc sp=0x0000008e9b0fd890
Event: 0.837 Thread 0x00000285f2d42b90 DEOPT UNPACKING pc=0x0000028587584e42 sp=0x0000008e9b0fccb0 mode 0
Event: 0.886 Thread 0x00000285f2d42b90 DEOPT PACKING pc=0x000002858000ed4c sp=0x0000008e9b0fb1d0
Event: 0.886 Thread 0x00000285f2d42b90 DEOPT UNPACKING pc=0x0000028587584e42 sp=0x0000008e9b0fa638 mode 0
Event: 0.998 Thread 0x00000285ff1b60a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000028587aef128 relative=0x00000000000001c8
Event: 0.999 Thread 0x00000285ff1b60a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000028587aef128 method=java.lang.invoke.VarHandle.checkAccessModeThenIsDirect(Ljava/lang/invoke/VarHandle$AccessDescriptor;)Z @ 4 c2
Event: 0.999 Thread 0x00000285ff1b60a0 DEOPT PACKING pc=0x0000028587aef128 sp=0x0000008e9c3ff440
Event: 0.999 Thread 0x00000285ff1b60a0 DEOPT UNPACKING pc=0x00000285875846a2 sp=0x0000008e9c3ff378 mode 2

Classes loaded (20 events):
Event: 1.194 Loading class sun/security/ssl/XDHKeyExchange$1
Event: 1.194 Loading class sun/security/ssl/XDHKeyExchange$1 done
Event: 1.194 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry
Event: 1.194 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry done
Event: 1.194 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession
Event: 1.194 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession done
Event: 1.195 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384
Event: 1.195 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384 done
Event: 1.195 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521
Event: 1.195 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521 done
Event: 1.195 Loading class sun/security/util/math/intpoly/P384OrderField
Event: 1.195 Loading class sun/security/util/math/intpoly/P384OrderField done
Event: 1.202 Loading class sun/security/util/math/intpoly/P521OrderField
Event: 1.202 Loading class sun/security/util/math/intpoly/P521OrderField done
Event: 1.212 Loading class java/security/interfaces/ECPrivateKey
Event: 1.212 Loading class java/security/interfaces/ECPrivateKey done
Event: 1.213 Loading class sun/security/util/ArrayUtil
Event: 1.213 Loading class sun/security/util/ArrayUtil done
Event: 1.215 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1
Event: 1.215 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.820 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f0e538}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x0000000711f0e538) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.821 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f15b48}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000711f15b48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.821 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f1c5e0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x0000000711f1c5e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.822 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f23070}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000711f23070) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.823 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f276f8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000711f276f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.827 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f514d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000711f514d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.828 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f54e60}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000711f54e60) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.829 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f64180}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711f64180) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.832 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f95e08}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x0000000711f95e08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.833 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f9c6d8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711f9c6d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.834 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f9ff28}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711f9ff28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.935 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711db4f50}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711db4f50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.968 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711b4a1c8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000711b4a1c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.968 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711b4db30}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000711b4db30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.978 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711b7bf58}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711b7bf58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.000 Thread 0x00000285f2d42b90 Exception <a 'java/lang/NoSuchMethodError'{0x00000007118727a0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007118727a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.001 Thread 0x00000285ff1b60a0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711814b30}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000711814b30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.008 Thread 0x00000285ff1b60a0 Exception <a 'java/lang/NoSuchMethodError'{0x000000071184e828}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000071184e828) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.055 Thread 0x00000285ff3a7cc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007118f1260}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007118f1260) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.061 Thread 0x00000285ff3a7cc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007119415b8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000007119415b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 0.138 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.161 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.168 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.168 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.339 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.339 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.004 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.004 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.113 Thread 0x00000285f2d42b90 Thread added: 0x00000285fe8fda20
Event: 0.113 Thread 0x00000285f2d42b90 Thread added: 0x00000285fe9011a0
Event: 0.114 Thread 0x00000285f2d42b90 Thread added: 0x00000285fe901bf0
Event: 0.114 Thread 0x00000285f2d42b90 Thread added: 0x00000285fe902640
Event: 0.114 Thread 0x00000285f2d42b90 Thread added: 0x00000285fe9053d0
Event: 0.115 Thread 0x00000285f2d42b90 Thread added: 0x00000285fe909a80
Event: 0.128 Thread 0x00000285f2d42b90 Thread added: 0x00000285feb4f600
Event: 0.129 Thread 0x00000285f2d42b90 Thread added: 0x00000285feb80530
Event: 0.132 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\net.dll
Event: 0.134 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\nio.dll
Event: 0.136 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\zip.dll
Event: 0.216 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\jimage.dll
Event: 0.238 Thread 0x00000285fe909a80 Thread added: 0x00000285fec2c1a0
Event: 0.759 Thread 0x00000285fec2c1a0 Thread exited: 0x00000285fec2c1a0
Event: 0.772 Thread 0x00000285fe909a80 Thread added: 0x00000285fed4a850
Event: 0.863 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\sunmscapi.dll
Event: 0.918 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\extnet.dll
Event: 0.924 Thread 0x00000285f2d42b90 Thread added: 0x00000285ff1b60a0
Event: 1.008 Thread 0x00000285ff1b60a0 Thread added: 0x00000285ff3a7cc0
Event: 1.070 Thread 0x00000285ff1b60a0 Thread added: 0x00000285ff3924f0


Dynamic libraries:
0x00007ff7ae320000 - 0x00007ff7ae32a000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.exe
0x00007ffa153e0000 - 0x00007ffa15645000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa143f0000 - 0x00007ffa144b9000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa12910000 - 0x00007ffa12cf8000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa13030000 - 0x00007ffa1317b000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa093e0000 - 0x00007ffa093f8000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jli.dll
0x00007ffa13d70000 - 0x00007ffa13f3a000 	C:\Windows\System32\USER32.dll
0x00007ffa12e80000 - 0x00007ffa12ea7000 	C:\Windows\System32\win32u.dll
0x00007ffa144c0000 - 0x00007ffa144eb000 	C:\Windows\System32\GDI32.dll
0x00007ffa12530000 - 0x00007ffa12667000 	C:\Windows\System32\gdi32full.dll
0x00007ffa12860000 - 0x00007ffa12903000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa0d8a0000 - 0x00007ffa0d8bb000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\VCRUNTIME140.dll
0x00007ff9e2090000 - 0x00007ff9e232a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffa146b0000 - 0x00007ffa14759000 	C:\Windows\System32\msvcrt.dll
0x00007ffa148f0000 - 0x00007ffa14920000 	C:\Windows\System32\IMM32.DLL
0x00007ffa0dfd0000 - 0x00007ffa0dfdc000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\vcruntime140_1.dll
0x00007ff9f0a80000 - 0x00007ff9f0b0d000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\msvcp140.dll
0x00007ff9642a0000 - 0x00007ff965061000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\server\jvm.dll
0x00007ffa14760000 - 0x00007ffa14813000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa13330000 - 0x00007ffa133d6000 	C:\Windows\System32\sechost.dll
0x00007ffa14ab0000 - 0x00007ffa14bc5000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa13560000 - 0x00007ffa135d4000 	C:\Windows\System32\WS2_32.dll
0x00007ffa122d0000 - 0x00007ffa1232e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa04c10000 - 0x00007ffa04c45000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa0e190000 - 0x00007ffa0e19b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa122b0000 - 0x00007ffa122c4000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa112f0000 - 0x00007ffa1130b000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa0d970000 - 0x00007ffa0d97a000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jimage.dll
0x00007ffa06230000 - 0x00007ffa06471000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa13980000 - 0x00007ffa13d05000 	C:\Windows\System32\combase.dll
0x00007ffa14930000 - 0x00007ffa14a11000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa03bf0000 - 0x00007ffa03c29000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa12670000 - 0x00007ffa12709000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa09e20000 - 0x00007ffa09e40000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.dll
0x00007ffa14c40000 - 0x00007ffa15382000 	C:\Windows\System32\SHELL32.dll
0x00007ffa12d00000 - 0x00007ffa12e74000 	C:\Windows\System32\wintypes.dll
0x00007ffa101d0000 - 0x00007ffa10a28000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa136a0000 - 0x00007ffa13791000 	C:\Windows\System32\SHCORE.dll
0x00007ffa14bd0000 - 0x00007ffa14c3a000 	C:\Windows\System32\shlwapi.dll
0x00007ffa12440000 - 0x00007ffa1246f000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa093c0000 - 0x00007ffa093d8000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\zip.dll
0x00007ffa09180000 - 0x00007ffa09190000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\net.dll
0x00007ffa0db10000 - 0x00007ffa0dc2e000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffa11860000 - 0x00007ffa118ca000 	C:\Windows\system32\mswsock.dll
0x00007ffa045d0000 - 0x00007ffa045e6000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\nio.dll
0x00007ffa11b10000 - 0x00007ffa11b2b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffa11250000 - 0x00007ffa1128a000 	C:\Windows\system32\rsaenh.dll
0x00007ffa11900000 - 0x00007ffa1192b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffa12410000 - 0x00007ffa12436000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffa11b30000 - 0x00007ffa11b3c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffa10d20000 - 0x00007ffa10d53000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffa143d0000 - 0x00007ffa143da000 	C:\Windows\System32\NSI.dll
0x00007ffa02d80000 - 0x00007ffa02d8e000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\sunmscapi.dll
0x00007ffa12eb0000 - 0x00007ffa13027000 	C:\Windows\System32\CRYPT32.dll
0x00007ffa11d30000 - 0x00007ffa11d60000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffa11ce0000 - 0x00007ffa11d1f000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ff9e0070000 - 0x00007ff9e0078000 	C:\Windows\system32\wshunix.dll
0x00007ff9fdae0000 - 0x00007ff9fdae9000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\IntelliJ IDEA 2025.1.3\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;D:\IntelliJ IDEA 2025.1.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitlab.seewintech.com': 
java_class_path (initial): D:/IntelliJ IDEA 2025.1.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/IntelliJ IDEA 2025.1.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 6                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4253024256                                {product} {ergonomic}
   size_t MaxNewSize                               = 2550136832                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4253024256                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\jdk-17.0.10
CLASSPATH=.;D:\jdk-17.0.10\lib;D:\jdk-17.0.10\lib\tools.jar
PATH=D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;D:\ShadowBot;D:\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\jdk-17.0.10\bin;E:\JavaSpace\peizhi\apache-maven-3.5.4\apache-maven-3.5.4\bin;D:\Git\cmd;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;D:\cursor\resources\app\bin;E:\node;C:\Program Files\dotnet;D:\ShadowBot;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Microsoft VS Code\bin;D:\IntelliJ IDEA 2025.1.3\bin
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 81552K (0% of 16609336K total physical memory with 2805704K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 6424K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1583K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16400B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 25 days 14:38 hours

CPU: total 6 (initial active 6) (6 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xde, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 6 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 16220M (2739M free)
TotalPageFile size 32693M (AvailPageFile size 60M)
current process WorkingSet (physical memory assigned to process): 79M, peak: 79M
current process commit charge ("private bytes"): 352M, peak: 354M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130) for windows-amd64 JRE (21.0.7+9-b895.130), built on 2025-05-13 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.

package com.seewin.som.report.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 项目运营报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Getter
@Setter
public class ReportOperationReportAddDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 分析指标
     */
    private String indicator;

    /**
     * 分析指标类型（0-门前客流（人）1-进店客流（人） 2-销售额（元）3-租管费（元）4-租售比（%））
     */
    private Integer indicatorType;

    /**
     * 本期数据
     */
    private BigDecimal currentPeriodData;

    /**
     * 同比数据
     */
    private BigDecimal yearOnYearData;

    /**
     * 同比率
     */
    private BigDecimal yearOnYearRate;

    /**
     * 环比数据
     */
    private BigDecimal monthOnMonthData;

    /**
     * 环比率
     */
    private BigDecimal monthOnMonthRate;

    /**
     * 均值
     */
    private BigDecimal meanValue;

    /**
     * 切尾均值
     */
    private BigDecimal trimmedMean;

    /**
     * 中位数
     */
    private BigDecimal median;

    /**
     * 众数
     */
    private BigDecimal mostFrequentValue;

    /**
     * 标准差
     */
    private BigDecimal standardDeviation;

    /**
     * 方差
     */
    private BigDecimal variance;

    /**
     * 中位数绝对偏差
     */
    private BigDecimal medianAbsoluteDeviation;

    /**
     * 极差
     */
    private BigDecimal dataRange;

    /**
     * 四分位差
     */
    private BigDecimal interquartileRange;

    /**
     * 第一四分位
     */
    private BigDecimal firstQuartile;

    /**
     * 第三四分位
     */
    private BigDecimal thirdQuartile;

    /**
     * 排名top5
     */
    private String topFiveRank;

    /**
     * 排名bottom5
     */
    private String bottomFiveRank;

    /**
     * 结果解读
     */
    private String resultInterpretation;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 可视化图表地址
     */
    private String chartData;

    private LocalDate startDate;

    private LocalDate endDate;
}

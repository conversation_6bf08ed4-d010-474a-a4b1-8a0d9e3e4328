package com.seewin.som.report.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 盈亏报告-成本分析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Getter
@Setter
public class ReportProfitCostAnalysisListDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 房间id
     */
    private String storeId;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 月度总成本（元/月）-本期数据
     */
    private BigDecimal monthTotal;

    /**
     * 月度总成本（元/月）-同比率
     */
    private BigDecimal monthTotalSameRatio;

    /**
     * 月度总成本（元/月）-环比率
     */
    private BigDecimal monthTotalRingRatio;

    /**
     * 人工成本（元/月）-本期数据
     */
    private BigDecimal laborCost;

    /**
     * 人工成本（元/月）-同比率
     */
    private BigDecimal laborCostSameRatio;

    /**
     * 人工成本（元/月）-环比率
     */
    private BigDecimal laborCostRingRatio;

    /**
     * 租管费（元/月）-本期数据
     */
    private BigDecimal rentPipe;

    /**
     * 租管费（元/月）-同比率
     */
    private BigDecimal rentPipeSameRatio;

    /**
     * 租管费（元/月）-环比率
     */
    private BigDecimal rentPipeRingRatio;

    /**
     * 水电费（元/月）-本期数据
     */
    private BigDecimal hydropowerFee;

    /**
     * 水电费（元/月）-同比率
     */
    private BigDecimal hydropowerFeeSameRatio;

    /**
     * 水电费（元/月）-环比率
     */
    private BigDecimal hydropowerFeeRingRatio;

    /**
     * 原材料费用（元/月）-本期数据
     */
    private BigDecimal materialCost;

    /**
     * 原材料费用（元/月）-同比率
     */
    private BigDecimal materialCostSameRatio;

    /**
     * 原材料费用（元/月）-环比率
     */
    private BigDecimal materialCostRingRatio;

    /**
     * 营销费用（元/月）-本期数据
     */
    private BigDecimal marketingExpenses;

    /**
     * 营销费用（元/月）-同比率
     */
    private BigDecimal marketingExpensesSameRatio;

    /**
     * 营销费用（元/月）-环比率
     */
    private BigDecimal marketingExpensesRingRatio;

    /**
     * 外卖平台费用（元/月）-本期数据
     */
    private BigDecimal takeoutFee;

    /**
     * 外卖平台费用（元/月）-同比率
     */
    private BigDecimal takeoutFeeSameRatio;

    /**
     * 外卖平台费用（元/月）-环比率
     */
    private BigDecimal takeoutFeeRingRatio;

    /**
     * 加盟管理费（元/月）-本期数据
     */
    private BigDecimal franchiseFee;

    /**
     * 加盟管理费（元/月）-同比率
     */
    private BigDecimal franchiseFeeSameRatio;

    /**
     * 加盟管理费（元/月）-环比率
     */
    private BigDecimal franchiseFeeRingRatio;

    /**
     * 税费（元/月）-本期数据
     */
    private BigDecimal tax;

    /**
     * 税费（元/月）-同比率
     */
    private BigDecimal taxSameRatio;

    /**
     * 税费（元/月）-环比率
     */
    private BigDecimal taxRingRatio;

    /**
     * 装修摊销费用（元/月）-本期数据
     */
    private BigDecimal decorationFee;

    /**
     * 装修摊销费用（元/月）-同比率
     */
    private BigDecimal decorationFeeSameRatio;

    /**
     * 装修摊销费用（元/月）-环比率
     */
    private BigDecimal decorationFeeRingRatio;

    /**
     * 固定资产折旧（元/月）-本期数据
     */
    private BigDecimal fixedDepreciation;

    /**
     * 固定资产折旧（元/月）-同比率
     */
    private BigDecimal fixedDepreciationSameRatio;

    /**
     * 固定资产折旧（元/月）-环比率
     */
    private BigDecimal fixedDepreciationRingRatio;

    /**
     * 品牌加盟费（元/月）-本期数据
     */
    private BigDecimal brandFee;

    /**
     * 品牌加盟费（元/月）-同比率
     */
    private BigDecimal brandFeeSameRatio;

    /**
     * 品牌加盟费（元/月）-环比率
     */
    private BigDecimal brandFeeRingRatio;

    /**
     * 其他成本-本期数据
     */
    private BigDecimal otherCosts;

    /**
     * 其他成本-同比率
     */
    private BigDecimal otherCostsSameRatio;

    /**
     * 其他成本-环比率
     */
    private BigDecimal otherCostsRingRatio;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 开始时间
     */
    private LocalDate startDate;
    /**
     * 结束时间
     */
    private LocalDate endDate;
    private List<String> storeIds;
}

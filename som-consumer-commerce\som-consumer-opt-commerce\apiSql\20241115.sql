drop table if exists som_commerce.som_commerce_contract_template;

/*==============================================================*/
/* Table: som_commerce.som_commerce_contract_template                        */
/*==============================================================*/
create table som_commerce.som_commerce_contract_template
(
    id                   bigint not null comment '主键',
    tenant_id            bigint comment '租户id(项目id)',
    tenant_name          varchar(255) comment '租户名称(项目名称)',
    ent_id               bigint comment '企业ID',
    org_fid              varchar(255) comment '所属组织ID路径',
    org_fname            varchar(255) comment '所属组织名称路径',
    template_name        varchar(255) comment '模板名称',
    template_file_id     bigint comment '模板附件ID',
    template_type        int comment '模板类型（0-word，1-excel）',
    contract_attr        int comment '合同属性（0 合同，1附件协议）',
    contract_type        varchar(255) comment '合同类型（0 租赁合同）',
    contract_type_name   varchar(255) comment '合同类型名称',
    contract_attr_name   varchar(255) comment '合同属性',
    status               int comment '模板状态0禁用 1 启用 ',
    remark               varchar(255) comment '备注',
    create_by            bigint comment '创建人id',
    create_user          varchar(32) comment '创建人账号/手机号',
    create_user_name     varchar(32) comment '创建人姓名/昵称',
    create_time          datetime comment '创建时间',
    update_by            bigint comment '修改人id',
    update_user          varchar(32) comment '修改人账号/手机号',
    update_user_name     varchar(32) comment '修改人姓名/昵称',
    update_time          datetime comment '修改时间',
    del_status           int comment '是否已删除: 0-否，1-是',
    version              int comment '乐观锁',
    primary key (id)
);

alter table som_commerce.som_commerce_contract_template comment '合同模板表';


drop table if exists som_commerce.som_commerce_contract_template_attr;

/*==============================================================*/
/* Table: som_commerce.som_commerce_contract_template_attr                   */
/*==============================================================*/
create table som_commerce.som_commerce_contract_template_attr
(
    id                   bigint not null comment '主键',
    tenant_id            bigint comment '租户id(项目id)',
    tenant_name          varchar(255) comment '租户名称(项目名称)',
    ent_id               bigint comment '企业ID',
    org_fid              varchar(255) comment '所属组织ID路径',
    org_fname            varchar(255) comment '所属组织名称路径',
    contract_template_id bigint comment '合同模板ID',
    title_name           varchar(1000) comment '标题名称',
    field_id             bigint comment '字段名称ID',
    field_code           varchar(1000) comment '字段名称code',
    field_name           varchar(1000) comment '字段名称',
    edit_flag            int comment '是否可编辑（0-是，1-否）',
    create_by            bigint comment '创建人id',
    create_user          varchar(32) comment '创建人账号/手机号',
    create_user_name     varchar(32) comment '创建人姓名/昵称',
    create_time          datetime comment '创建时间',
    update_by            bigint comment '修改人id',
    update_user          varchar(32) comment '修改人账号/手机号',
    update_user_name     varchar(32) comment '修改人姓名/昵称',
    update_time          datetime comment '修改时间',
    del_status           int comment '是否已删除: 0-否，1-是',
    version              int comment '乐观锁',
    primary key (id)
);

alter table som_commerce.som_commerce_contract_template_attr comment '合同模板表字段属性表';

drop table if exists som_commerce.som_commerce_contract_template_field;

/*==============================================================*/
/* Table: som_commerce.som_commerce_contract_template_field                  */
/*==============================================================*/
create table som_commerce.som_commerce_contract_template_field
(
    id                   bigint not null comment '主键',
    parent_id            bigint comment '父ID',
    field_code           varchar(1000) comment '字段code',
    field_name           varchar(1000) comment '字段名称',
    field_type           varchar(32) comment '字段类型',
    suffix               varchar(32) comment '后缀名',
    value_format         varchar(32) comment '数据转换（yyyy-MM-dd）',
    precision_number     int comment '保留小数数量',
    sort_number          int comment '排序',
    level_number         int comment '层级',
    create_by            bigint comment '创建人id',
    create_user          varchar(32) comment '创建人账号/手机号',
    create_user_name     varchar(32) comment '创建人姓名/昵称',
    create_time          datetime comment '创建时间',
    update_by            bigint comment '修改人id',
    update_user          varchar(32) comment '修改人账号/手机号',
    update_user_name     varchar(32) comment '修改人姓名/昵称',
    update_time          datetime comment '修改时间',
    del_status           int comment '是否已删除: 0-否，1-是',
    version              int comment '乐观锁',
    primary key (id)
);

alter table som_commerce.som_commerce_contract_template_field comment '合同模板字段表';

drop table if exists  som_commerce.som_commerce_contract_print;
CREATE TABLE som_commerce.som_commerce_contract_print (
    `id` bigint NOT NULL COMMENT '主键',

    `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户id(项目id)',
    `tenant_name` varchar(255) DEFAULT NULL COMMENT '租户名称(项目名称)',
    `ent_id` bigint DEFAULT NULL COMMENT '企业ID',
    `org_fid` varchar(255) DEFAULT '0' COMMENT '所属组织ID路径',
    `org_fname` varchar(255) DEFAULT NULL COMMENT '所属组织名称路径',

    `contract_code` varchar(255) DEFAULT NULL COMMENT '合同编号',
    `contract_template_id` bigint DEFAULT NULL COMMENT '合同模板ID',

    `template_name` varchar(255) DEFAULT NULL COMMENT '模板名称',
    `template_type` int DEFAULT NULL COMMENT '模板类型（0-word，1-excel）',
    `contract_attr` int DEFAULT NULL COMMENT '合同属性（0 合同，1附件协议）',
    `contract_type` varchar(255) DEFAULT NULL COMMENT '合同类型（0 租赁合同）',
    `contract_type_name` varchar(255) DEFAULT NULL COMMENT '合同类型名称',
    `contract_attr_name` varchar(255) DEFAULT NULL COMMENT '合同属性',

    `contract_no` varchar(255) DEFAULT NULL COMMENT '合同号',
    `invoice_no` varchar(255) DEFAULT NULL COMMENT '单据号',
    `register_no` varchar(255) DEFAULT NULL COMMENT '合同登记(备案)号',

    `lessor` varchar(64) DEFAULT NULL COMMENT '甲方(出租方)',
    `lessor_representative` varchar(64) DEFAULT NULL COMMENT '甲方(出租方)法定代表人',
    `lessor_address` varchar(255) DEFAULT NULL COMMENT '甲方(出租方)住所',

    `renter` varchar(64) DEFAULT NULL COMMENT '乙方(承租方)',
    `renter_representative` varchar(64) DEFAULT NULL COMMENT '乙方(承租方)法定代表人',
    `renter_address` varchar(255) DEFAULT NULL COMMENT '乙方(承租方)住所',

    `room_city` varchar(64) DEFAULT NULL COMMENT '商铺所在城市',
    `room_address` varchar(255) DEFAULT NULL COMMENT '商铺所在地址',
    `room_name` varchar(64) DEFAULT NULL COMMENT '铺位号',
    `room_build_area` double(10,0) DEFAULT NULL COMMENT '商铺建筑面积',

    `rent_period` int DEFAULT NULL COMMENT '租期(月份)',
    `rent_start_date` date DEFAULT NULL COMMENT '租赁起始时间',
    `rent_end_date` date DEFAULT NULL COMMENT '租赁截止时间',

    `brand_id` bigint DEFAULT NULL COMMENT '品牌ID',
    `brand_name` varchar(255) DEFAULT NULL COMMENT '品牌名称',
    `brand_commercial_type_code` bigint DEFAULT NULL COMMENT '经营品牌业态字典code',
    `brand_commercial_type_name` varchar(255) DEFAULT NULL COMMENT '经营品牌业态名称',
    `business_scope` varchar(255) DEFAULT NULL COMMENT '经营范围',

    `month_price_contract` decimal(20,2) DEFAULT NULL COMMENT '月租金单价',
    `month_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '月租金总额（元）',
    `incremental_start_contract` decimal(20,2) DEFAULT NULL COMMENT '租金递增起始年',
    `incremental_rate_contract` decimal(20,2) DEFAULT NULL COMMENT '租金递增率(%)',

    `rent_bail_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '租赁保证金',
    `common_bail_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '公共事业保证金',

    `accounting_org_name` varchar(255) DEFAULT NULL COMMENT '甲方账户名称(核算组织名称)',
    `bank_account_name` varchar(255) DEFAULT NULL COMMENT '甲方银行名称',
    `bank_account_code` varchar(255) DEFAULT NULL COMMENT '甲方银行账号编码',

    `delivery_date` date DEFAULT NULL COMMENT '商户交付日期',
    `fee_free_contract` int DEFAULT NULL COMMENT '装修免租期(天数)',
    `open_date` date DEFAULT NULL COMMENT '商户开业日期',
    `decoration_bail_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '装修押金(元)',
    `pos_fee` decimal(20,2) DEFAULT NULL COMMENT 'POS机设备总价款(元)',

    `lessor_notify` varchar(64) DEFAULT NULL COMMENT '通知信息-甲方(出租方)',
    `lessor_notify_address` varchar(255) DEFAULT NULL COMMENT '通知信息-甲方(出租方)地址',

    `renter_notify` varchar(64) DEFAULT NULL COMMENT '通知信息-乙方(承租方)',
    `renter_notify_phone` varchar(64) DEFAULT NULL COMMENT '通知信息-乙方(承租方)电话',
    `renter_notify_address` varchar(255) DEFAULT NULL COMMENT '通知信息-乙方(承租方)地址',
    `renter_notify_email` varchar(64) DEFAULT NULL COMMENT '通知信息-乙方(承租方)邮箱',
    `renter_notify_contact_name` varchar(64) DEFAULT NULL COMMENT '通知信息-乙方(承租方)联系人',
    `renter_notify_contact_phone` varchar(64) DEFAULT NULL COMMENT '通知信息-乙方(承租方)联系方式',

    `standard_detail` text DEFAULT NULL COMMENT '标准明细',

    `bank_account` varchar(255) DEFAULT NULL COMMENT '甲方银行账号',
    `thirdparty` varchar(64) DEFAULT NULL COMMENT '第三方',
    `interparty` varchar(64) DEFAULT NULL COMMENT '丙方',
    `prep_rent_fee` decimal(20,2) DEFAULT NULL COMMENT '首期预付租管费',
    `floor_name` varchar(255) DEFAULT NULL COMMENT '楼层名称',
    `function_name` varchar(255) DEFAULT NULL COMMENT '功能',
    `lessor_email` varchar(64) DEFAULT NULL COMMENT '甲方(出租方)邮箱',
    `lessor_contact_name` varchar(64) DEFAULT NULL COMMENT '甲方(出租方)联系人',
    `lessor_contact_phone` varchar(64) DEFAULT NULL COMMENT '甲方(出租方)联系方式',
    `renter_card_number` varchar(64) DEFAULT NULL COMMENT '乙方(承租方)身份证号',

    `month_manage_price_contract` decimal(20,2) DEFAULT NULL COMMENT '月物业管理费单价',
    `month_manage_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '月物业管理费总额（元）',
    `month_operate_price_contract` decimal(20,2) DEFAULT NULL COMMENT '月运营管理费单价',
    `month_operate_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '月运营管理费总额（元）',
    `composite_manage_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '综合管理费',
    `prep_pay_days` int DEFAULT NULL COMMENT '运营运维使用费预付天数',
    `prep_operate_fee` decimal(20,2) DEFAULT NULL COMMENT '预付运营平台软硬件使用费',
    `prep_maintain_fee` decimal(20,2) DEFAULT NULL COMMENT '预付运维平台软硬件使用费',
    `property_manage_bail_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '物业管理费保证金',
    `operate_manage_bail_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '运营管理费保证金',
    `other_price_contract` decimal(20,2) DEFAULT NULL COMMENT '其他费用单价',
    `other_fee_contract` decimal(20,2) DEFAULT NULL COMMENT '其他服务费用（元）',

    `create_by` bigint DEFAULT NULL COMMENT '创建人id',
    `create_user` varchar(32) DEFAULT NULL COMMENT '创建人账号/手机号',
    `create_user_name` varchar(32) DEFAULT NULL COMMENT '创建人姓名/昵称',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` bigint DEFAULT NULL COMMENT '修改人id',
    `update_user` varchar(32) DEFAULT NULL COMMENT '修改人账号/手机号',
    `update_user_name` varchar(32) DEFAULT NULL COMMENT '修改人姓名/昵称',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `del_status` int NOT NULL DEFAULT '0' COMMENT '是否已删除: 0-否，1-是',
    `version` int DEFAULT NULL COMMENT '乐观锁',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='招商合同打印表';

ALTER TABLE som_rent.som_rent_fee_actual_detail_log ADD `arrears_confirm_status` int DEFAULT '0' COMMENT '滞纳金确认状态: 0-没有确认 1-已确认';
ALTER TABLE som_rent.som_rent_fee_actual_detail_log ADD `arrears_invoice_status` int DEFAULT NULL COMMENT '滞纳金发票状态: 0-成功 1-生成中 2-失败';
ALTER TABLE som_rent.som_rent_fee_actual_detail_log ADD `arrears_invoice_result` varchar(512) DEFAULT NULL COMMENT '滞纳金发票处理结果';
ALTER TABLE som_rent.som_rent_fee_actual_detail_log ADD `arrears_invoice_pic` varchar(512) DEFAULT NULL COMMENT '滞纳金发票处理图片';
ALTER TABLE som_rent.som_rent_fee_actual_detail_log ADD `arrears_invoice_biz_id` varchar(64) DEFAULT NULL COMMENT '滞纳金发票业务id';

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num, `version`, `platform`)
VALUES (1854478504612290561, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplate', '合同模板表', '', 2, 0, 11, 2, 0, '-', 0, '', 0,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num, `version`, `platform`)
VALUES (1854478504629067777, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplate:page', '合同模板表分页', '/commerceContractTemplate/page', 2, 1, 11, 2, 1854478504612290561, '合同模板表', 0, '', 1,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num, `version`, `platform`)
VALUES (1854478504633262081, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplate:add', '合同模板表新增', '/commerceContractTemplate/add', 2, 1,  11, 2, 1854478504612290561, '合同模板表', 0, '', 2,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num, `version`, `platform`)
VALUES (1854478504633262082, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplate:edit', '合同模板表编辑', '/commerceContractTemplate/edit', 2, 1,  11, 2, 1854478504612290561, '合同模板表', 0, '', 3,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num, `version`, `platform`)
VALUES (1854478504633262083, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplate:get', '合同模板表查看', '/commerceContractTemplate/get', 2, 1,  11, 2, 1854478504612290561, '合同模板表', 0, '', 4,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num, `version`, `platform`)
VALUES (1854478504633262084, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplate:del', '合同模板表删除', '/commerceContractTemplate/del', 2, 1,  11, 2, 1854478504612290561, '合同模板表', 0, '', 5,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num, `version`, `platform`)
VALUES (1854478504633262085, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplate:list', '合同模板表列表', '/commerceContractTemplate/list', 2, 1,  11, 2, 1854478504612290561, '合同模板表', 0, '', 6,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num, `version`, `platform`)
VALUES (1854478504633262086, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplate:getFieldById', '合同模板表字段编辑详情', '/commerceContractTemplate/getFieldById', 2, 1,  11, 2, 1854478504612290561, '合同模板表', 0, '', 7,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num, `version`, `platform`)
VALUES (1854478504633262087, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplate:updateStatus', '合同模板表-批量修改合同模板的状态', '/commerceContractTemplate/updateStatus', 2, 1,  11, 2, 1854478504612290561, '合同模板表', 0, '', 8,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504662622209, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplateattr', '合同模板表字段属性表', '', 2, 0, 11, 2, 0, '-', 0, '', 0,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504662622210, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplateattr:page', '合同模板表字段属性表分页', '/commerceContractTemplateAttr/page', 2, 1, 11, 2, 1854478504662622209, '合同模板表字段属性表', 0, '', 1,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504662622211, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplateattr:add', '合同模板表字段属性表新增', '/commerceContractTemplateAttr/add', 2, 1,  11, 2, 1854478504662622209, '合同模板表字段属性表', 0, '', 2,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504666816514, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplateattr:edit', '合同模板表字段属性表编辑', '/commerceContractTemplateAttr/edit', 2, 1,  11, 2, 1854478504662622209, '合同模板表字段属性表', 0, '', 3,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504666816515, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplateattr:get', '合同模板表字段属性表查看', '/commerceContractTemplateAttr/get', 2, 1,  11, 2, 1854478504662622209, '合同模板表字段属性表', 0, '', 4,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504666816516, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplateattr:del', '合同模板表字段属性表删除', '/commerceContractTemplateAttr/del', 2, 1,  11, 2, 1854478504662622209, '合同模板表字段属性表', 0, '', 5,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504666816517, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplateattr:list', '合同模板表字段属性表列表', '/commerceContractTemplateAttr/list', 2, 1,  11, 2, 1854478504662622209, '合同模板表字段属性表', 0, '', 6,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504683593729, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplatefield', '合同模板字段表', '', 2, 0, 11, 2, 0, '-', 0, '', 0,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504683593730, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplatefield:page', '合同模板字段表分页', '/commerceContractTemplateField/page', 2, 1, 11, 2, 1854478504683593729, '合同模板字段表', 0, '', 1,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504683593731, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplatefield:add', '合同模板字段表新增', '/commerceContractTemplateField/add', 2, 1,  11, 2, 1854478504683593729, '合同模板字段表', 0, '', 2,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504683593732, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplatefield:edit', '合同模板字段表编辑', '/commerceContractTemplateField/edit', 2, 1,  11, 2, 1854478504683593729, '合同模板字段表', 0, '', 3,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504683593733, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplatefield:get', '合同模板字段表查看', '/commerceContractTemplateField/get', 2, 1,  11, 2, 1854478504683593729, '合同模板字段表', 0, '', 4,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504683593734, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplatefield:del', '合同模板字段表删除', '/commerceContractTemplateField/del', 2, 1,  11, 2, 1854478504683593729, '合同模板字段表', 0, '', 5,null,'som');

INSERT INTO `som_system`.`som_sys_api`(id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504683593735, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplatefield:list', '合同模板字段表列表', '/commerceContractTemplateField/list', 2, 1,  11, 2, 1854478504683593729, '合同模板字段表', 0, '', 6,null,'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1854478504683593736, 'commerce', '商业管理', 'opt:commerce:commercecontracttemplatefield:tree', '合同模板字段表树形下拉框查询', '/commerceContractTemplateField/tree', 2, 1,  11, 2, 1854478504683593729, '合同模板字段表', 0, '', 7,null,'som');

INSERT INTO `som_system`.`som_sys_api`(`id`, `service_code`, `service_name`, `api_code`, `api_name`, `api_path`, `api_type`, `api_level`, `api_source`, `auth_type`, `parent_id`, `parent_name`, `log_enable`, `remark`, `sort_num`, `version`, `platform`) VALUES (1806584150161903618, 'commerce', '运营管理', 'opt:commerce:commercecontract:contract', '合同传签-生成合同管理页', '/commerceContract/manage', 2, 1, 11, 2, 1770985183372029953, '', 1, '', 0, NULL, 'som');

INSERT INTO `som_system`.`som_sys_api`(`id`, `service_code`, `service_name`, `api_code`, `api_name`, `api_path`, `api_type`, `api_level`, `api_source`, `auth_type`, `parent_id`, `parent_name`, `log_enable`, `remark`, `sort_num`, `version`, `platform`) VALUES (1806584150161903619, 'commerce', '运营管理', 'opt:commerce:commercecontract:contractTemplate', '合同传签-合同模板页', '/commerceContract/manage', 2, 1, 11, 2, 1770985183372029953, '', 1, '', 0, NULL, 'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark,  sort_num, `version`, `platform`)
VALUES (1855784991007596545, 'commerce', '商业管理', 'opt:commerce:commercecontractprint', '招商合同打印表', '', 2, 0, 11, 2, 0, '-', 0, '', 0,null,'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark,  sort_num, `version`, `platform`)
VALUES (1855784991150202881, 'commerce', '商业管理', 'opt:commerce:commercecontractprint:page', '招商合同打印表分页', '/commerceContractPrint/page', 2, 1, 11, 2, 1855784991007596545, '招商合同打印表', 0, '', 1,null,'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark,  sort_num, `version`, `platform`)
VALUES (1855784991150202882, 'commerce', '商业管理', 'opt:commerce:commercecontractprint:add', '招商合同打印表新增', '/commerceContractPrint/add', 2, 1,  11, 2, 1855784991007596545, '招商合同打印表', 0, '', 2,null,'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark,  sort_num, `version`, `platform`)
VALUES (1855784991150202883, 'commerce', '商业管理', 'opt:commerce:commercecontractprint:edit', '招商合同打印表编辑', '/commerceContractPrint/edit', 2, 1,  11, 2, 1855784991007596545, '招商合同打印表', 0, '', 3,null,'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark,  sort_num, `version`, `platform`)
VALUES (1855784991150202884, 'commerce', '商业管理', 'opt:commerce:commercecontractprint:get', '招商合同打印表查看', '/commerceContractPrint/get', 2, 1,  11, 2, 1855784991007596545, '招商合同打印表', 0, '', 4,null,'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark,  sort_num, `version`, `platform`)
VALUES (1855784991150202885, 'commerce', '商业管理', 'opt:commerce:commercecontractprint:del', '招商合同打印表删除', '/commerceContractPrint/del', 2, 1,  11, 2, 1855784991007596545, '招商合同打印表', 0, '', 5,null,'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark,  sort_num, `version`, `platform`)
VALUES (1855784991150202886, 'commerce', '商业管理', 'opt:commerce:commercecontractprint:list', '招商合同打印表列表', '/commerceContractPrint/list', 2, 1,  11, 2, 1855784991007596545, '招商合同打印表', 0, '', 6,null,'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, sort_num,`version`, `platform`)
VALUES (1855784991150202887, 'commerce', '商业管理', 'opt:commerce:commercecontractprint:exp', '招商合同打印表-导出合同PDF', '/commerceContractPrint/exp', 2, 1,  11, 2, 1855784991007596545, '招商合同打印表', 0, '', 6,null,'som');

INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (1, 0, NULL, '基本信息', 'title', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (2, 0, NULL, '当事人信息', 'title', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (3, 0, NULL, '商铺信息', 'title', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (4, 0, NULL, '甲方账户信息', 'title', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (5, 0, NULL, '通知信息', 'title', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (6, 0, NULL, '装修信息', 'title', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (7, 0, NULL, '其他信息', 'title', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (8, 0, NULL, '租赁信息', 'title', NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (9, 1, 'contractNo', '合同号', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (10, 1, 'registerNo', '合同登记（备案）号', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (11, 1, 'brandName', '品牌名称', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (12, 1, 'floorName', '楼层', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (13, 1, 'roomName', '铺位编号', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (14, 1, 'roomBuildArea', '建筑面积', 'number', '㎡', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (15, 1, 'functionName', '功能', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (16, 1, 'lessor', '甲方（杭州）', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (17, 1, 'renter', '乙方', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (18, 1, 'renterCardNumber', '身份证号', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (19, 1, 'lessor', '甲方（深圳益田）', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (20, 1, 'lessor', '甲方', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (21, 1, 'lessor', '甲方（陕西）', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (22, 1, 'lessor', '甲方（厦门）', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (23, 1, 'lessor', '甲方（长沙）', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (24, 1, 'lessor', '甲方（深圳实正）', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (25, 2, 'lessor', '甲方（出租方）', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (26, 2, 'lessorRepresentative', '甲方法定代表人', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (27, 2, 'lessorAddress', '甲方住所', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (28, 2, 'renter', '乙方（出租方）', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (29, 2, 'renterRepresentative', '乙方法定代表人', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (30, 2, 'renterAddress', '乙方住所', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (31, 2, 'thirdparty', '第三方', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (32, 2, 'lessor', '甲方', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (33, 2, 'renter', '乙方', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (34, 2, 'renterCardNumber', '乙方身份证号', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (35, 2, 'interparty', '丙方', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (36, 2, 'lessorAddress', '甲方通讯地址', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (37, 2, 'lessorContactName', '甲方联系人', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (38, 2, 'lessorContactPhone', '甲方联系电话', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (39, 2, 'lessorEmail', '甲方邮箱', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (40, 2, 'renterAddress', '乙方通讯地址', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (41, 2, 'renterNotifyContactName', '乙方联系人', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (42, 2, 'renterNotifyContactPhone', '乙方联系电话', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (43, 2, 'renterNotifyEmail', '乙方邮箱', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (44, 3, 'roomCity', '所在区域', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (45, 3, 'roomBuildArea', '建筑面积', 'number', '㎡', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (46, 3, 'brandName', '品牌名称', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (47, 3, 'brandCommercialTypeName', '经营业态', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (48, 3, 'businessScope', '经营范围', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (49, 4, 'bankAccountName', '银行名称', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (50, 4, 'bankAccount', '银行账号', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (51, 4, 'accountingOrgName', '账户名称', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (52, 5, 'lessor', '甲方', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (53, 5, 'lessorAddress', '甲方地址', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (54, 5, 'renter', '乙方', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (55, 5, 'renterNotifyPhone', '乙方电话', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (56, 5, 'renterAddress', '乙方地址', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (57, 5, 'renterNotifyEmail', '乙方邮箱', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (58, 5, 'renterNotifyContactName', '乙方联系人', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (59, 5, 'renterNotifyContactPhone', '乙方联系方式', 'string', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (60, 6, 'deliveryDate', '交付日期', 'date', NULL, 'yyyy-MM-dd', NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (61, 6, 'feeFreeContract', '装修天数', 'number', NULL, NULL, 0, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (62, 6, 'openDate', '开业日期', 'date', NULL, 'yyyy-MM-dd', NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (63, 6, 'decorationBailFeeContract', '装修押金', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (64, 7, 'posFee', 'POS机设备总价款', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (65, 8, 'rentPeriod', '租期（月）', 'number', NULL, NULL, 0, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (66, 8, 'rentStartDate', '租赁起始日期', 'date', NULL, 'yyyy-MM-dd', NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (67, 8, 'monthPriceContract', '租管费（元/㎡/月）', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (68, 8, 'monthFeeContract', '租管费（元/月）', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (69, 8, 'incrementalStartContract', '递增起始年', 'number', NULL, NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (70, 8, 'incrementalRateContract', '递增率（%）', 'number', NULL, NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (71, 8, 'rentDateDetail', '租赁期限', 'table', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (72, 8, 'monthFeeContractDetail', '月基本租管费（元）', 'table', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (73, 8, 'saleTotalDetail', '销售额', 'table', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (74, 8, 'saleRatioDetail', '销售额计提比例', 'table', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (75, 8, 'monthPriceContractDetail', '月基本租管费标准（元/㎡）', 'table', NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (76, 8, 'monthOperatePriceContract', '月运营平台软硬件使用费单价（元/㎡）', 'number', '元/㎡', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (77, 8, 'monthOperateFeeContract', '月运营平台软硬件使用费（元）', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (78, 8, 'monthManagePriceContract', '月运维平台软硬件使用费单价（元/㎡）', 'number', '元/㎡', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (79, 8, 'monthManageFeeContract', '月运维平台软硬件使用费（元）', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (80, 8, 'compositeManageFeeContract', '一次性数据服务费（元）', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (81, 8, 'prepPayDays', '运营运维使用费预付天数', 'number', NULL, NULL, 0, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (82, 8, 'prepOperateFee', '预付运营平台软硬件使用费', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (83, 8, 'prepMaintainFee', '预付运维平台软硬件使用费', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (84, 8, 'propertyManageBailFeeContract', '运维平台软硬件使用费保证金', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (85, 8, 'operateManageBailFeeContract', '运营平台软硬件使用费保证金', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (86, 8, 'otherPriceContract', '服务费单价（元/㎡/月）', 'number', '元/㎡/月', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (87, 8, 'otherFeeContract', '服务费（元）', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (88, 8, 'rentBailFeeContract', '租赁保证金（元）', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (89, 8, 'commonBailFeeContract', '公共事业保证金（元）', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (90, 8, 'rentEndDate', '租赁起止日期', 'date', NULL, 'yyyy-MM-dd', NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);
INSERT INTO `som_commerce`.`som_commerce_contract_template_field`(`id`, `parent_id`, `field_code`, `field_name`, `field_type`, `suffix`, `value_format`, `precision_number`, `sort_number`, `level_number`, `create_by`, `create_user`, `create_user_name`, `create_time`, `update_by`, `update_user`, `update_user_name`, `update_time`, `del_status`, `version`) VALUES (91, 8, 'prepRentFee', '首期预付租管费', 'number', '元', NULL, 2, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL);

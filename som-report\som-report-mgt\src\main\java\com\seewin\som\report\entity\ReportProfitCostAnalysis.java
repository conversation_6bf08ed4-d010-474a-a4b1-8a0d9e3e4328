package com.seewin.som.report.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 盈亏报告-成本分析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@TableName("som_report_profit_cost_analysis")
public class ReportProfitCostAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 租户名称
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 房间id
     */
    @TableField("store_id")
    private String storeId;

    /**
     * 年份
     */
    @TableField("year")
    private Integer year;

    /**
     * 月份
     */
    @TableField("month")
    private Integer month;

    /**
     * 月度总成本（元/月）-本期数据
     */
    @TableField("month_total")
    private BigDecimal monthTotal;

    /**
     * 月度总成本（元/月）-同比率
     */
    @TableField("month_total_same_ratio")
    private BigDecimal monthTotalSameRatio;

    /**
     * 月度总成本（元/月）-环比率
     */
    @TableField("month_total_ring_ratio")
    private BigDecimal monthTotalRingRatio;

    /**
     * 人工成本（元/月）-本期数据
     */
    @TableField("labor_cost")
    private BigDecimal laborCost;

    /**
     * 人工成本（元/月）-同比率
     */
    @TableField("labor_cost_same_ratio")
    private BigDecimal laborCostSameRatio;

    /**
     * 人工成本（元/月）-环比率
     */
    @TableField("labor_cost_ring_ratio")
    private BigDecimal laborCostRingRatio;

    /**
     * 租管费（元/月）-本期数据
     */
    @TableField("rent_pipe")
    private BigDecimal rentPipe;

    /**
     * 租管费（元/月）-同比率
     */
    @TableField("rent_pipe_same_ratio")
    private BigDecimal rentPipeSameRatio;

    /**
     * 租管费（元/月）-环比率
     */
    @TableField("rent_pipe_ring_ratio")
    private BigDecimal rentPipeRingRatio;

    /**
     * 水电费（元/月）-本期数据
     */
    @TableField("hydropower_fee")
    private BigDecimal hydropowerFee;

    /**
     * 水电费（元/月）-同比率
     */
    @TableField("hydropower_fee_same_ratio")
    private BigDecimal hydropowerFeeSameRatio;

    /**
     * 水电费（元/月）-环比率
     */
    @TableField("hydropower_fee_ring_ratio")
    private BigDecimal hydropowerFeeRingRatio;

    /**
     * 原材料费用（元/月）-本期数据
     */
    @TableField("material_cost")
    private BigDecimal materialCost;

    /**
     * 原材料费用（元/月）-同比率
     */
    @TableField("material_cost_same_ratio")
    private BigDecimal materialCostSameRatio;

    /**
     * 原材料费用（元/月）-环比率
     */
    @TableField("material_cost_ring_ratio")
    private BigDecimal materialCostRingRatio;

    /**
     * 营销费用（元/月）-本期数据
     */
    @TableField("marketing_expenses")
    private BigDecimal marketingExpenses;

    /**
     * 营销费用（元/月）-同比率
     */
    @TableField("marketing_expenses_same_ratio")
    private BigDecimal marketingExpensesSameRatio;

    /**
     * 营销费用（元/月）-环比率
     */
    @TableField("marketing_expenses_ring_ratio")
    private BigDecimal marketingExpensesRingRatio;

    /**
     * 外卖平台费用（元/月）-本期数据
     */
    @TableField("takeout_fee")
    private BigDecimal takeoutFee;

    /**
     * 外卖平台费用（元/月）-同比率
     */
    @TableField("takeout_fee_same_ratio")
    private BigDecimal takeoutFeeSameRatio;

    /**
     * 外卖平台费用（元/月）-环比率
     */
    @TableField("takeout_fee_ring_ratio")
    private BigDecimal takeoutFeeRingRatio;

    /**
     * 加盟管理费（元/月）-本期数据
     */
    @TableField("franchise_fee")
    private BigDecimal franchiseFee;

    /**
     * 加盟管理费（元/月）-同比率
     */
    @TableField("franchise_fee_same_ratio")
    private BigDecimal franchiseFeeSameRatio;

    /**
     * 加盟管理费（元/月）-环比率
     */
    @TableField("franchise_fee_ring_ratio")
    private BigDecimal franchiseFeeRingRatio;

    /**
     * 税费（元/月）-本期数据
     */
    @TableField("tax")
    private BigDecimal tax;

    /**
     * 税费（元/月）-同比率
     */
    @TableField("tax_same_ratio")
    private BigDecimal taxSameRatio;

    /**
     * 税费（元/月）-环比率
     */
    @TableField("tax_ring_ratio")
    private BigDecimal taxRingRatio;

    /**
     * 装修摊销费用（元/月）-本期数据
     */
    @TableField("decoration_fee")
    private BigDecimal decorationFee;

    /**
     * 装修摊销费用（元/月）-同比率
     */
    @TableField("decoration_fee_same_ratio")
    private BigDecimal decorationFeeSameRatio;

    /**
     * 装修摊销费用（元/月）-环比率
     */
    @TableField("decoration_fee_ring_ratio")
    private BigDecimal decorationFeeRingRatio;

    /**
     * 固定资产折旧（元/月）-本期数据
     */
    @TableField("fixed_depreciation")
    private BigDecimal fixedDepreciation;

    /**
     * 固定资产折旧（元/月）-同比率
     */
    @TableField("fixed_depreciation_same_ratio")
    private BigDecimal fixedDepreciationSameRatio;

    /**
     * 固定资产折旧（元/月）-环比率
     */
    @TableField("fixed_depreciation_ring_ratio")
    private BigDecimal fixedDepreciationRingRatio;

    /**
     * 品牌加盟费（元/月）-本期数据
     */
    @TableField("brand_fee")
    private BigDecimal brandFee;

    /**
     * 品牌加盟费（元/月）-同比率
     */
    @TableField("brand_fee_same_ratio")
    private BigDecimal brandFeeSameRatio;

    /**
     * 品牌加盟费（元/月）-环比率
     */
    @TableField("brand_fee_ring_ratio")
    private BigDecimal brandFeeRingRatio;

    /**
     * 其他成本-本期数据
     */
    @TableField("other_costs")
    private BigDecimal otherCosts;

    /**
     * 其他成本-同比率
     */
    @TableField("other_costs_same_ratio")
    private BigDecimal otherCostsSameRatio;

    /**
     * 其他成本-环比率
     */
    @TableField("other_costs_ring_ratio")
    private BigDecimal otherCostsRingRatio;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;
    /**
     * 开始时间
     */
    @TableField("start_date")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @TableField("end_date")
    private LocalDate endDate;

}

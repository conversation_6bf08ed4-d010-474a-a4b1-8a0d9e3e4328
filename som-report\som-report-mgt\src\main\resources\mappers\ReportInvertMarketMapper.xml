<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.report.mapper.ReportInvertMarketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.report.entity.ReportInvertMarket">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="data_type" property="dataType" />
        <result column="commercial_type_code" property="commercialTypeCode" />
        <result column="commercial_type_name" property="commercialTypeName" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="indicator" property="indicator" />
        <result column="indicator_type" property="indicatorType" />
        <result column="indicator_remark" property="indicatorRemark" />
        <result column="current_period_data" property="currentPeriodData" />
        <result column="year_on_year_data" property="yearOnYearData" />
        <result column="year_on_year_rate" property="yearOnYearRate" />
        <result column="month_on_month_data" property="monthOnMonthData" />
        <result column="month_on_month_rate" property="monthOnMonthRate" />
        <result column="mean_value" property="meanValue" />
        <result column="trimmed_mean" property="trimmedMean" />
        <result column="median" property="median" />
        <result column="most_frequent_value" property="mostFrequentValue" />
        <result column="standard_deviation" property="standardDeviation" />
        <result column="variance" property="variance" />
        <result column="median_absolute_deviation" property="medianAbsoluteDeviation" />
        <result column="data_range" property="dataRange" />
        <result column="interquartile_range" property="interquartileRange" />
        <result column="first_quartile" property="firstQuartile" />
        <result column="third_quartile" property="thirdQuartile" />
        <result column="result_interpretation" property="resultInterpretation" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
    </resultMap>

</mapper>

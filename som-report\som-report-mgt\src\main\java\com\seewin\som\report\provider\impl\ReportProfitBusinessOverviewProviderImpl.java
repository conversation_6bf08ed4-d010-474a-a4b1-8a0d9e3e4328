package com.seewin.som.report.provider.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.report.entity.ReportProfitBusinessOverview;
import com.seewin.som.report.req.ReportProfitBusinessOverviewAddDto;
import com.seewin.som.report.req.ReportProfitBusinessOverviewEditDto;
import com.seewin.som.report.req.ReportProfitBusinessOverviewListDto;
import com.seewin.som.report.resp.ReportProfitBusinessOverviewAddVo;
import com.seewin.som.report.resp.ReportProfitBusinessOverviewGetVo;
import com.seewin.som.report.resp.ReportProfitBusinessOverviewListVo;
import com.seewin.util.bean.BeanUtils;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.seewin.som.report.service.ReportProfitBusinessOverviewService;
import com.seewin.som.report.provider.ReportProfitBusinessOverviewProvider;

/**
 * <p>
 * 盈亏报告-经营概况 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@DubboService
public class ReportProfitBusinessOverviewProviderImpl implements ReportProfitBusinessOverviewProvider {

    @Autowired
    private ReportProfitBusinessOverviewService reportProfitBusinessOverviewService;

    /**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public PageResult<ReportProfitBusinessOverviewListVo> page(PageQuery<ReportProfitBusinessOverviewListDto> pageQuery) throws ServiceException {
        ReportProfitBusinessOverviewListDto dto = pageQuery.getQueryDto();

        //设置分页
        Page<ReportProfitBusinessOverview> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        //构造查询条件
        QueryWrapper<ReportProfitBusinessOverview> queryWrapper = queryBuild(dto);

        //查询数据
        page = reportProfitBusinessOverviewService.page(page, queryWrapper);
        List<ReportProfitBusinessOverview> records = page.getRecords();

        //响应结果封装
        PageResult<ReportProfitBusinessOverviewListVo> result = new PageResult<>();
        List<ReportProfitBusinessOverviewListVo> items = BeanUtils.copyProperties(records, ReportProfitBusinessOverviewListVo.class);

        result.setItems(items);
        result.setPages((int) page.getPages());
        result.setTotal((int) page.getTotal());
        result.setPageNum(pageQuery.getPageNum());
        result.setPageSize(pageQuery.getPageSize());

        //返回查询结果
        return result;
    }

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public List<ReportProfitBusinessOverviewListVo> list(ReportProfitBusinessOverviewListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportProfitBusinessOverview> queryWrapper = queryBuild(dto);

        //查询数据
        List<ReportProfitBusinessOverview> records = reportProfitBusinessOverviewService.list(queryWrapper);

        //响应结果封装
        List<ReportProfitBusinessOverviewListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, ReportProfitBusinessOverviewListVo.class);

        //返回查询结果
        return result;
    }

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    @Override
    public int count(ReportProfitBusinessOverviewListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportProfitBusinessOverview> queryWrapper = queryBuild(dto, false);

        //查询数据
        int result = (int) reportProfitBusinessOverviewService.count(queryWrapper);

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportProfitBusinessOverviewGetVo get(Long id) throws ServiceException {
        //查询数据
        ReportProfitBusinessOverview item = reportProfitBusinessOverviewService.getById(id);

        //响应结果封装
        ReportProfitBusinessOverviewGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportProfitBusinessOverviewGetVo.class);
        }

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportProfitBusinessOverviewGetVo get(ReportProfitBusinessOverviewListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportProfitBusinessOverview> queryWrapper = queryBuild(dto);
        queryWrapper.last(PageQuery.LIMIT_ONE);

        //查询数据
        ReportProfitBusinessOverview item = reportProfitBusinessOverviewService.getOne(queryWrapper);

        //响应结果封装
        ReportProfitBusinessOverviewGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportProfitBusinessOverviewGetVo.class);
        }

        //返回查询结果
        return result;
    }


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportProfitBusinessOverviewAddVo add(ReportProfitBusinessOverviewAddDto dto) throws ServiceException {
        ReportProfitBusinessOverview entity = BeanUtils.copyProperties(dto, ReportProfitBusinessOverview.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setCreateBy(dto.getCreateBy());
        entity.setCreateUser(dto.getCreateUser());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setUpdateBy(dto.getCreateBy());
        entity.setUpdateUser(dto.getCreateUser());
        entity.setUpdateUserName(dto.getCreateUserName());
        entity.setUpdateTime(nowTime);
        entity.setDelStatus(0);
        reportProfitBusinessOverviewService.save(entity);

        //响应结果封装
        ReportProfitBusinessOverviewAddVo result = new ReportProfitBusinessOverviewAddVo();
        result.setId(entity.getId());

        return result;
    }


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void edit(ReportProfitBusinessOverviewEditDto dto) throws ServiceException {
        ReportProfitBusinessOverview entity = BeanUtils.copyProperties(dto, ReportProfitBusinessOverview.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateUser(dto.getUpdateUser());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateTime(nowTime);

        reportProfitBusinessOverviewService.updateById(entity);
    }

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void delete(Long id) throws ServiceException {
        reportProfitBusinessOverviewService.removeById(id);
    }

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void delete(ReportProfitBusinessOverviewListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<ReportProfitBusinessOverview> queryWrapper = queryBuild(dto, false);

        //删除操作
        reportProfitBusinessOverviewService.remove(queryWrapper);
    }

    @Override
    public void addBatch(List<ReportProfitBusinessOverviewAddDto> overviewAddDtoList) {
        if (CollectionUtils.isEmpty(overviewAddDtoList)) {
            return;
        }
        LocalDateTime nowTime = LocalDateTime.now();
        List<ReportProfitBusinessOverview> businessOverviewsList = new ArrayList<>();
        for (ReportProfitBusinessOverviewAddDto addDto : overviewAddDtoList) {
            ReportProfitBusinessOverview overview = BeanUtils.copyProperties(addDto, ReportProfitBusinessOverview.class);
            overview.setId(IdWorker.getId());
            overview.setCreateTime(nowTime);
            overview.setUpdateTime(nowTime);
            businessOverviewsList.add(overview);
        }
        reportProfitBusinessOverviewService.saveBatch(businessOverviewsList);
    }

    /**
     * 修改Ai描述
     *
     * @param tenantId
     * @param entId
     * @param storeId
     * @param ai
     */
    @Override
    public void setAi(Long tenantId, Long entId, String storeId, Integer year, Integer month, String ai, LocalDate startDate , LocalDate endDate) {
        reportProfitBusinessOverviewService.lambdaUpdate().eq(ReportProfitBusinessOverview::getTenantId, tenantId)
                .eq(ReportProfitBusinessOverview::getEntId, entId).eq(ReportProfitBusinessOverview::getStoreId, storeId)
                .eq(ReportProfitBusinessOverview::getYear,year).eq(ReportProfitBusinessOverview::getMonth,month)
                .eq(ReportProfitBusinessOverview::getStartDate,startDate).eq(ReportProfitBusinessOverview::getEndDate,endDate)
                .set(ReportProfitBusinessOverview::getAlInterpret, ai).update();
    }

    /**
     * <p>构造查询条件<br>
     * <p>默认构造排序条件<br>
     *
     * @param dto 查询条件Dto
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportProfitBusinessOverview> queryBuild(ReportProfitBusinessOverviewListDto dto) throws ServiceException {
        return queryBuild(dto, true);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportProfitBusinessOverview> queryBuild(ReportProfitBusinessOverviewListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<ReportProfitBusinessOverview> queryWrapper = new QueryWrapper<>();

        ReportProfitBusinessOverview entity = BeanUtils.copyProperties(dto, ReportProfitBusinessOverview.class);
        entity.setDelStatus(0);

        /** 添加条件样例参考，不用请删除
         if (StringUtils.isNotBlank(dto.getName())) {
         entity.setName(null);
         queryWrapper.like("name", dto.getName());
         }

         queryWrapper.in(dto.getStatusIn() != null, "status", dto.getStatusIn());

         if (orderBy) {
         if (dto.getTypeOrder() != null) {
         queryWrapper.orderBy(true, dto.getTypeOrder().isAsc(), "type");
         }

         queryWrapper.orderByAsc("order_by");
         }
         */

        //按创建时间倒序排序，根据需要添加
        //queryWrapper.orderByDesc("create_time");
        queryWrapper.in(dto.getStoreIds() != null, "store_id", dto.getStoreIds());

        queryWrapper.setEntity(entity);

        return queryWrapper;
    }
}

package com.seewin.som.commerce.provider.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.seewin.som.commerce.entity.*;
import com.seewin.som.commerce.enums.ComercePlanApproveStatusEnum;
import com.seewin.som.commerce.enums.OrderExecuteStatusEnum;
import com.seewin.som.commerce.enums.SupplierUserTypeEnum;
import com.seewin.som.commerce.req.*;
import com.seewin.som.commerce.resp.*;
import com.seewin.som.commerce.service.*;
import com.seewin.som.commerce.utils.SequenceCodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.util.bean.BeanUtils;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import com.seewin.som.commerce.provider.CommerceContractProvider;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 招商合同表-商铺 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@DubboService
@Slf4j
public class CommerceContractProviderImpl implements CommerceContractProvider {

    @Autowired
    private CommerceContractService commerceContractService;
    @Autowired
    private CommerceContractInfoService commerceContractInfoService;
    @Autowired
    private CommerceContractRentService commerceContractRentService;
    @Autowired
    private CommerceOrderService commerceOrderService;
    @Autowired
    private CommerceSubOrderService commerceSubOrderService;
    @Autowired
    private CommerceEntexitService commerceEntexitService;
    @Autowired
    private CommerceInspectionScopeService commerceInspectionScopeService;
    @Autowired
    private CommercePlanDetailPresentService commercePlanDetailPresentService;
    @Autowired
    private CommerceSupplierService commerceSupplierService;

    /**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public PageResult<CommerceContractListVo> page(PageQuery<CommerceContractListDto> pageQuery) throws ServiceException {
        CommerceContractListDto dto = pageQuery.getQueryDto();

        //设置分页
        Page<CommerceContract> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        //构造查询条件
        QueryWrapper<CommerceContract> queryWrapper = queryBuild(dto);

        //查询数据
        page = commerceContractService.page(page, queryWrapper);
        List<CommerceContract> records = page.getRecords();

        if (CollectionUtils.isEmpty(records)) {
            //响应结果封装
            PageResult<CommerceContractListVo> result = new PageResult<>();
            result.setItems(Collections.emptyList());
            result.setPages((int) page.getPages());
            result.setTotal((int) page.getTotal());
            result.setPageNum(pageQuery.getPageNum());
            result.setPageSize(pageQuery.getPageSize());

            //返回查询结果
            return result;
        }
        //合同信息
        List<String> contractCodes = records.stream().map(CommerceContract::getContractCode).collect(Collectors.toList());
        QueryWrapper<CommerceContractInfo> contractInfoQueryWrapper = new QueryWrapper<>();
        contractInfoQueryWrapper.in("contract_code", contractCodes);
        List<CommerceContractInfo> contractInfoList = commerceContractInfoService.list(contractInfoQueryWrapper);
        Map<String, CommerceContractInfo> commerceContractInfoMap = new HashMap<>();
        contractInfoList.forEach(e -> commerceContractInfoMap.put(e.getContractCode(), e));

        //工单状态
        List<Long> subOrderIds = records.stream().map(CommerceContract::getSubOrderId).collect(Collectors.toList());
        QueryWrapper<CommerceSubOrder> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.in("id", subOrderIds);
        List<CommerceSubOrder> orderList = commerceSubOrderService.list(orderQueryWrapper);
        Map<String, Integer> orderStatusMap = new HashMap<>();

        LocalDate today = LocalDate.now();
        orderList.forEach(e -> {
            if (Objects.nonNull(e.getExecuteStatus()) && !e.getExecuteStatus().equals(OrderExecuteStatusEnum.DELAY.getCode()) && Objects.nonNull(e.getExecuteDeadline()) && today.isAfter(e.getExecuteDeadline())) {
                orderStatusMap.put(e.getOrderCode(), OrderExecuteStatusEnum.DELAY.getCode());
            } else {
                orderStatusMap.put(e.getOrderCode(), e.getExecuteStatus());
            }
        });

        //响应结果封装
        PageResult<CommerceContractListVo> result = new PageResult<>();
        List<CommerceContractListVo> items = BeanUtils.copyProperties(records, CommerceContractListVo.class);
        for (CommerceContractListVo item : items) {
            item.setContractType(commerceContractInfoMap.get(item.getContractCode()).getContractType());
            item.setRentType(commerceContractInfoMap.get(item.getContractCode()).getRentType());
            item.setOrderStatus(orderStatusMap.get(item.getOrderCode()));
        }

        result.setItems(items);
        result.setPages((int) page.getPages());
        result.setTotal((int) page.getTotal());
        result.setPageNum(pageQuery.getPageNum());
        result.setPageSize(pageQuery.getPageSize());

        //返回查询结果
        return result;
    }

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public List<CommerceContractListVo> list(CommerceContractListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<CommerceContract> queryWrapper = queryBuild(dto);

        //查询数据
        List<CommerceContract> records = commerceContractService.list(queryWrapper);

        //响应结果封装
        List<CommerceContractListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, CommerceContractListVo.class);

        //返回查询结果
        return result;
    }

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    @Override
    public int count(CommerceContractListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<CommerceContract> queryWrapper = queryBuild(dto, false);

        //查询数据
        int result = (int) commerceContractService.count(queryWrapper);

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public CommerceContractGetVo get(Long id) throws ServiceException {
        //查询数据
        CommerceContract item = commerceContractService.getById(id);

        //响应结果封装
        CommerceContractGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, CommerceContractGetVo.class);
        }

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public CommerceContractGetVo get(CommerceContractListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<CommerceContract> queryWrapper = queryBuild(dto);
        queryWrapper.last(PageQuery.LIMIT_ONE);

        //查询数据
        CommerceContract item = commerceContractService.getOne(queryWrapper);

        //响应结果封装
        CommerceContractGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, CommerceContractGetVo.class);
        }

        //返回查询结果
        return result;
    }

    @Override
    public Long getContractCodeNum(String tenantName) {
        QueryWrapper<CommerceContract> countWrapper = new QueryWrapper<>();
        countWrapper.eq("tenant_name", tenantName);
        countWrapper.ge("create_time",LocalDateTime.of(LocalDateTime.now().getYear(),1,1,0,0));
        countWrapper.orderByDesc("contract_code");
        countWrapper.last(PageQuery.LIMIT_ONE);
        CommerceContract last = commerceContractService.getOne(countWrapper);
        Long count = 0L;
        if(Objects.nonNull(last)){
            count = Long.valueOf(last.getContractCode().substring(8));
        }
        return count;
    }

    @Override
    public String getContractCode(String projectCode, Long count) {
        String contractCode = SequenceCodeUtils.getContractCode(projectCode, count);
        return contractCode;
    }

    @Override
    public void saveImportExcel(CommerceContractImportExcelDto excelDto) {
        LocalDateTime nowLocalDateTime = excelDto.getCreateTime();
        String contractCode = excelDto.getContractCode();

        CommerceContractImportDto contractDto = excelDto.getContractImportDto();
        CommerceContract commerceContract = BeanUtils.copyProperties(contractDto, CommerceContract.class);
        if (StringUtils.isEmpty(contractCode)){
            commerceContract.setId(IdWorker.getId());
            commerceContract.setCreateTime(nowLocalDateTime);
            commerceContract.setVersion(10);
            commerceContractService.save(commerceContract);
            log.info("已入库招商合同-商铺");
        }else {
            CommerceContract contract = commerceContractService.getOne(Wrappers.<CommerceContract>lambdaQuery().eq(CommerceContract::getContractCode, contractCode).eq(CommerceContract::getDelStatus, 0));
            if (contract!=null){
                commerceContract.setId(contract.getId());
                commerceContractService.updateById(commerceContract);
                log.info("已更新招商合同-商铺");
            }
        }

        CommerceContractInfoImportDto contractInfoDto = excelDto.getContractInfoImportDto();
        CommerceContractInfo contractInfo = BeanUtils.copyProperties(contractInfoDto, CommerceContractInfo.class);
        if (StringUtils.isEmpty(contractCode)){
            contractInfo.setId(IdWorker.getId());
            contractInfo.setCreateTime(nowLocalDateTime);
            contractInfo.setVersion(10);
            commerceContractInfoService.save(contractInfo);
            log.info("已入库招商合同信息");
        }else {
            CommerceContractInfo info = commerceContractInfoService.getOne(Wrappers.<CommerceContractInfo>lambdaQuery().eq(CommerceContractInfo::getContractCode, contractCode).eq(CommerceContractInfo::getDelStatus, 0));
            if (info!=null){
                contractInfo.setId(info.getId());
                commerceContractInfoService.updateById(contractInfo);
                log.info("已更新招商合同信息");
            }
        }

        CommerceContractRentImportDto contractRentDto = excelDto.getContractRentImportDto();
        CommerceContractRent contractRent = BeanUtils.copyProperties(contractRentDto, CommerceContractRent.class);
        if (StringUtils.isEmpty(contractCode)){
            contractRent.setId(IdWorker.getId());
            contractRent.setCreateTime(nowLocalDateTime);
            contractRent.setVersion(10);
            commerceContractRentService.save(contractRent);
            log.info("已入库招商合同租赁信息");
        }else {
            CommerceContractRent rent = commerceContractRentService.getOne(Wrappers.<CommerceContractRent>lambdaQuery().eq(CommerceContractRent::getContractCode, contractCode).eq(CommerceContractRent::getDelStatus, 0));
            if (rent!=null){
                contractRent.setId(rent.getId());
                commerceContractRentService.updateById(contractRent);
                log.info("已更新招商合同租赁信息");
                // 如果租赁类型改为了纯扣 修改月租金总额为 null
                if (contractInfo.getRentType() != null && contractInfo.getRentType() == 2){
                    commerceContractRentService.updateFeeEmpty(contractRent.getId());
                }
            }
        }

        // 生成进场工单
        CommerceEntexit entexit = BeanUtils.copyProperties(contractInfo, CommerceEntexit.class);

        entexit.setAgreedDeliveryDate(contractInfo.getDeliveryDate());
        entexit.setSupplieContact(contractInfo.getContactName());
        entexit.setContactPhone(contractInfo.getContactPhon());
        entexit.setLeaseTerm(contractInfo.getRentStartDate() + "-" + contractInfo.getRentEndDate());
        entexit.setDurationContract(contractInfo.getRentEndDate());
        entexit.setCommercialTypeCode(String.valueOf(contractDto.getCommercialTypeCode()));
        entexit.setCommercialTypeName(contractDto.getCommercialTypeName());
        entexit.setRoomBrandId(contractDto.getBrandId());
        entexit.setRoomBrand(contractDto.getBrandName());

        CommerceEntexit entexitDb = null;
        if (StringUtils.isNotEmpty(contractCode)){
           entexitDb = commerceEntexitService.getOne(Wrappers.<CommerceEntexit>lambdaQuery().eq(CommerceEntexit::getContractCode, contractCode).eq(CommerceEntexit::getDelStatus, 0));
        }

        if (StringUtils.isEmpty(contractCode) || (StringUtils.isNotEmpty(contractCode) && entexitDb==null)){
            LocalDate nowLocalDate = LocalDate.now();
            if (nowLocalDate.isAfter(contractInfoDto.getRentStartDate())){
                entexit.setId(IdWorker.getId());

                entexit.setRoomNo(contractDto.getName());
                entexit.setShopId(contractDto.getRoomId());
                entexit.setFid(contractDto.getFid());
                entexit.setFcode(contractDto.getFcode());
                entexit.setFname(contractDto.getFname());

                entexit.setOrderStatus(0);
                entexit.setRoomId(contractDto.getRoomShopId());
                entexit.setRoomStatus("no_delivered");

                entexit.setOrderGenTime(contractDto.getCreateTime());

                entexit.setCreateBy(0L);
                entexit.setCreateUser(StringUtils.EMPTY);
                entexit.setCreateUserName(StringUtils.EMPTY);
                entexit.setCreateTime(nowLocalDateTime);
                entexit.setUpdateBy(0L);
                entexit.setUpdateUser(StringUtils.EMPTY);
                entexit.setUpdateUserName(StringUtils.EMPTY);
                entexit.setUpdateTime(nowLocalDateTime);
                entexit.setVersion(10);
                entexit.setFollowId(contractDto.getCreateBy());
                entexit.setFollowName(contractDto.getCreateUserName());
                entexit.setDataType(1);
                commerceEntexitService.save(entexit);
                log.info("已入库进场工单");
            }
        }else {
            if (entexitDb!=null){
                entexit.setId(entexitDb.getId());
                commerceEntexitService.updateById(entexit);
                log.info("已更新进场工单");
            }
        }

        if (StringUtils.isEmpty(contractCode)){
            LocalDate now = LocalDate.now();
            // 更新工单执行状态为已完成，分配状态为已分配
            CommerceOrder byOrderCode = commerceOrderService.getByOrderCode(contractDto.getOrderCode());
            if (byOrderCode!=null){
                byOrderCode.setExecuteStatus(3);
                byOrderCode.setDiliveryStatus(1);
                byOrderCode.setIsOver(1);
                byOrderCode.setExecuteUsers(contractDto.getCreateUser());
                byOrderCode.setExecuteUserNames(contractDto.getCreateUserName());
                byOrderCode.setExecuteDeadline(now);
                byOrderCode.setCloseDate(now);
                commerceOrderService.updateById(byOrderCode);
            }
        }
    }

    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    @Override
    public CommerceContractAddVo add(CommerceContractAddDto dto) throws ServiceException {
        CommerceContract entity = BeanUtils.copyProperties(dto, CommerceContract.class);

        LocalDateTime nowTime = LocalDateTime.now();

        QueryWrapper<CommerceContract> countWrapper = new QueryWrapper<>();
        countWrapper.eq("tenant_id", dto.getTenantId());
        countWrapper.ge("create_time",LocalDateTime.of(LocalDateTime.now().getYear(),1,1,0,0));
        countWrapper.orderByDesc("contract_code");
        countWrapper.last(PageQuery.LIMIT_ONE);
        CommerceContract last = commerceContractService.getOne(countWrapper);
        long count = 0;
        if(Objects.nonNull(last)){
            count = Long.valueOf(last.getContractCode().substring(8));
        }

        String contractCode = SequenceCodeUtils.getContractCode(dto.getProjectCode(), count);
        entity.setContractCode(contractCode);

        //签约信息
        CommerceContractInfo contractInfo = BeanUtils.copyProperties(dto.getContractInfo(), CommerceContractInfo.class);
        contractInfo.setTenantId(entity.getTenantId());
        contractInfo.setTenantName(entity.getTenantName());
        contractInfo.setEntId(entity.getEntId());
        contractInfo.setOrgFid(entity.getOrgFid());
        contractInfo.setOrgFname(entity.getOrgFname());
        contractInfo.setContractCode(entity.getContractCode());
        contractInfo.setBrandId(dto.getBrandId());
        contractInfo.setBrandName(dto.getBrandName());
        contractInfo.setBrandCommercialTypeCode(dto.getCommercialTypeCode());
        contractInfo.setBrandCommercialTypeName(dto.getCommercialTypeName());
        contractInfo.setBrandCategoryId(dto.getCategoryId());
        contractInfo.setBrandCategoryName(dto.getCategoryName());

        contractInfo.setId(IdWorker.getId());
        contractInfo.setCreateTime(nowTime);
        contractInfo.setCreateBy(dto.getCreateBy());
        contractInfo.setCreateUser(dto.getCreateUser());
        contractInfo.setCreateUserName(dto.getCreateUserName());
        contractInfo.setUpdateBy(dto.getCreateBy());
        contractInfo.setUpdateUser(dto.getCreateUser());
        contractInfo.setUpdateUserName(dto.getCreateUserName());
        contractInfo.setUpdateTime(nowTime);
        contractInfo.setDelStatus(0);
        commerceContractInfoService.save(contractInfo);

        //租赁信息
        CommerceContractRent contractRent = BeanUtils.copyProperties(dto.getContractRent(), CommerceContractRent.class);
        contractRent.setTenantId(entity.getTenantId());
        contractRent.setTenantName(entity.getTenantName());
        contractRent.setEntId(entity.getEntId());
        contractRent.setOrgFid(entity.getOrgFid());
        contractRent.setOrgFname(entity.getOrgFname());
        contractRent.setContractCode(entity.getContractCode());
        contractRent.setId(IdWorker.getId());
        contractRent.setCreateTime(nowTime);
        contractRent.setCreateBy(dto.getCreateBy());
        contractRent.setCreateUser(dto.getCreateUser());
        contractRent.setCreateUserName(dto.getCreateUserName());
        contractRent.setUpdateBy(dto.getCreateBy());
        contractRent.setUpdateUser(dto.getCreateUser());
        contractRent.setUpdateUserName(dto.getCreateUserName());
        contractRent.setUpdateTime(nowTime);
        contractRent.setDelStatus(0);
        commerceContractRentService.save(contractRent);

        //主信息
        entity.setExecuteUser(dto.getCreateUser());
        entity.setExecuteUserName(dto.getCreateUserName());

        entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setCreateBy(dto.getCreateBy());
        entity.setCreateUser(dto.getCreateUser());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setUpdateBy(dto.getCreateBy());
        entity.setUpdateUser(dto.getCreateUser());
        entity.setUpdateUserName(dto.getCreateUserName());
        entity.setUpdateTime(nowTime);
        entity.setDelStatus(0);
        //流转
        if (Objects.nonNull(dto.getOperate()) && dto.getOperate().equals(2)) {
            entity.setApplyDate(LocalDate.now());
        }
        entity.setApproveStatus(ComercePlanApproveStatusEnum.unCommitted.getCode());
        commerceContractService.save(entity);

        //响应结果封装
        CommerceContractAddVo result = new CommerceContractAddVo();
        result.setId(entity.getId());
        result.setContractCode(contractCode);

        return result;
    }

    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    @Override
    public CommerceContractGetVo edit(CommerceContractEditDto dto) throws ServiceException {
        CommerceContract entity = commerceContractService.getById(dto.getId());
        //校验工单是否已有合同
        if (StringUtils.isNotEmpty(entity.getOrderCode())) {
            boolean hasContract = this.checkContract(entity.getOrderCode());
            if (hasContract) {
                throw new ServiceException("工单存在审批中或已通过的合同，请勿重复保存提交");
            }
        }

        //签约信息
        CommerceContractInfo contractInfo = commerceContractInfoService.getByContractCode(entity.getContractCode());
        org.springframework.beans.BeanUtils.copyProperties(dto.getContractInfo(), contractInfo);

        contractInfo.setBrandId(dto.getBrandId());
        contractInfo.setBrandName(dto.getBrandName());
        contractInfo.setBrandCommercialTypeCode(dto.getCommercialTypeCode());
        contractInfo.setBrandCommercialTypeName(dto.getCommercialTypeName());
        contractInfo.setBrandCategoryId(dto.getCategoryId());
        contractInfo.setBrandCategoryName(dto.getCategoryName());
        commerceContractInfoService.updateById(contractInfo);

        //租赁信息
        CommerceContractRent contractRent = commerceContractRentService.getByContractCode(entity.getContractCode());
        org.springframework.beans.BeanUtils.copyProperties(dto.getContractRent(), contractRent);
        commerceContractRentService.updateById(contractRent);
        // 如果租赁类型改为了纯扣 修改月租金总额为 null
        if (contractInfo.getRentType() != null && contractInfo.getRentType() == 2){
            commerceContractRentService.updateFeeEmpty(contractRent.getId());
        }

        //流转
        if (Objects.nonNull(dto.getOperate()) && dto.getOperate().equals(2)) {
            entity.setApplyDate(LocalDate.now());
            entity.setApproveStatus(ComercePlanApproveStatusEnum.beingProcessed.getCode());
        }

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateUser(dto.getUpdateUser());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateTime(nowTime);

        // 品牌、业态、一级品类修改
        entity.setBrandId(dto.getBrandId());
        entity.setBrandName(dto.getBrandName());
        entity.setCommercialTypeCode(dto.getCommercialTypeCode());
        entity.setCommercialTypeName(dto.getCommercialTypeName());
        entity.setCategoryId(dto.getCategoryId());
        entity.setCategoryName(dto.getCategoryName());
        entity.setFaddressdetail(dto.getFaddressdetail());
        commerceContractService.updateById(entity);

        return BeanUtils.copyProperties(entity, CommerceContractGetVo.class);
    }

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void delete(Long id) throws ServiceException {
        commerceContractService.removeById(id);
    }

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void delete(CommerceContractListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<CommerceContract> queryWrapper = queryBuild(dto, false);

        //删除操作
        commerceContractService.remove(queryWrapper);
    }

    @Override
    public CommerceContractGetVo getLastContract(Long roomId) throws ServiceException {
        CommerceContract record = commerceContractService.getLastContract(roomId);
        if (Objects.nonNull(record)) {
            CommerceContractGetVo getVo = BeanUtils.copyProperties(record, CommerceContractGetVo.class);
            return getVo;
        }
        return null;
    }

    @Override
    public CommerceContractArchiveVo archive(CommerceContractArchiveDto dto) throws ServiceException {
        CommerceContract contract = commerceContractService.getById(dto.getId());
        CommerceContractInfo contractInfo = commerceContractInfoService.getByContractCode(contract.getContractCode());
        contract.setArchiveDate(dto.getArchiveDate());

//        //门店ID
//        if (Objects.nonNull(contractInfo.getContractType()) && contractInfo.getContractType().equals(2)) {
//            String roomShopId = commerceContractService.getMaxShopId(contract.getRoomId());
//            contract.setRoomShopId(roomShopId);
//        } else {//新签生成新门店ID
//            Long count = commerceContractService.getShopIdCount(contract.getRoomId());
//            contract.setRoomShopId(contract.getName() + "." + SequenceCodeUtils.getSequenceCode(count.intValue() + 1, 4));
//        }
        commerceContractService.updateById(contract);

//        //工单状态变更
//        QueryWrapper<CommerceOrder> queryWrapperOrder = new QueryWrapper<>();
//        queryWrapperOrder.eq("room_id", contract.getRoomId());
//        queryWrapperOrder.in("execute_status", Arrays.asList(0, 1, 2));
//        //fixme: 兼容多条,
//        List<CommerceOrder> commerceOrders = commerceOrderService.list(queryWrapperOrder);
//        if (!CollectionUtils.isEmpty(commerceOrders)) {
//            //主工单
//            List<String> orderCodes = new ArrayList<>();
//            commerceOrders.forEach(e -> {
//                e.setExecuteStatus(3);
//                e.setCloseDate(dto.getArchiveDate());
//                orderCodes.add(e.getOrderCode());
//            });
//            commerceOrderService.updateBatchById(commerceOrders);
//
//            //其他子工单
//            CommerceSubOrder subOrderUpdate = new CommerceSubOrder();
//            subOrderUpdate.setExecuteStatus(4);
//            subOrderUpdate.setUpdateTime(LocalDateTime.now());
//
//            QueryWrapper<CommerceSubOrder> updateWrapperSub = new QueryWrapper<>();
//            updateWrapperSub.ne("execute_user", contract.getExecuteUser());
//            updateWrapperSub.in("order_code", orderCodes);
//            updateWrapperSub.ne("execute_status", OrderExecuteStatusEnum.OVER.getCode());
//            commerceSubOrderService.update(subOrderUpdate, updateWrapperSub);
//
//            //对应子工单
//            updateWrapperSub = new QueryWrapper<>();
//            subOrderUpdate.setExecuteStatus(3);
//            subOrderUpdate.setCloseDate(dto.getArchiveDate());
//            updateWrapperSub.in("order_code", orderCodes);
//            updateWrapperSub.eq("execute_user", contract.getExecuteUser());
//            updateWrapperSub.ne("execute_status", OrderExecuteStatusEnum.OVER.getCode());
//            commerceSubOrderService.update(subOrderUpdate, updateWrapperSub);
//        }

        CommerceContractArchiveVo getVo = BeanUtils.copyProperties(contract, CommerceContractArchiveVo.class);
        getVo.setContractInfo(BeanUtils.copyProperties(contractInfo, CommerceContractInfoGetVo.class));

        //合同归档，回写进撤场工单
        LocalDateTime now = LocalDateTime.now();
        if (Objects.nonNull(contractInfo.getContractType()) && contractInfo.getContractType().equals(2)) {
            //续签
            UpdateWrapper<CommerceEntexit> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("room_id", contract.getRoomShopId());
            updateWrapper.eq("shop_id",contract.getRoomId());
            updateWrapper.set("has_renewal", null);

            //更新内容
            CommerceEntexit entexit = new CommerceEntexit();
            entexit.setRoomStatus("open");
            entexit.setRoomBrand(getVo.getBrandName());
            entexit.setSupplieContact(contractInfo.getContactName());
            entexit.setContactPhone(contractInfo.getContactPhon());
            entexit.setLeaseTerm(contractInfo.getRentStartDate() + "-" + contractInfo.getRentEndDate());
            entexit.setRenewalAplTime(now);
//            entexit.setHasRenewal(null);
            entexit.setExitAplName(dto.getUpdateUserName());
            entexit.setDurationContract(contractInfo.getRentEndDate());
            entexit.setRenewalContractNo(contract.getContractCode());

            entexit.setUpdateBy(dto.getUpdateBy());
            entexit.setUpdateUser(dto.getUpdateUser());
            entexit.setUpdateUserName(dto.getUpdateUserName());
            entexit.setUpdateTime(now);
            commerceEntexitService.update(entexit, updateWrapper);
//        } else {
//            //新签
//            CommerceEntexit entexit = BeanUtils.copyProperties(contractInfo, CommerceEntexit.class);
//
//            entexit.setId(IdWorker.getId());
//            entexit.setRoomNo(getVo.getName());
//            entexit.setShopId(getVo.getRoomId());
//
//            //todo:优化
//            if(StringUtils.isNotEmpty(contract.getOrderCode())) {
//                CommerceOrder order = commerceOrderService.getByOrderCode(contract.getOrderCode());
//                entexit.setFid(order.getFid());
//                entexit.setFcode(order.getFcode());
//                entexit.setFname(order.getFname());
//            }
//
//            //获取配置跟进人
//            QueryWrapper<CommerceInspectionScope> queryWrapperScope = new QueryWrapper();
//            queryWrapperScope.last(PageQuery.LIMIT_ONE);
//            queryWrapperScope.like("scope", "\"roomId\": " + getVo.getRoomId());
//            //查询数据
//            CommerceInspectionScope item = commerceInspectionScopeService.getOne(queryWrapperScope);
//            if (Objects.nonNull(item)) {
//                entexit.setFollowId(item.getUserId());
//                entexit.setFollowName(item.getRealName());
//                entexit.setOrderStatus(1);
//
//                //营运工单接收
//                MsgLogAddVo msgLogAddVo = new MsgLogAddVo();
//                msgLogAddVo.setCode("operate_entexit_receive");
//                msgLogAddVo.setType(0);
//                msgLogAddVo.setUserIds(Arrays.asList(item.getUserId()));
//                msgLogAddVo.setTargets(Arrays.asList(item.getUserName()));
//                msgLogAddVo.setSendStatus(1);
//
//                Map<String,Object> param = new HashMap<>();
//                param.put("storeId",getVo.getRoomShopId());
//                msgLogAddVo.setTemplateParam(param);
//
//                getVo.setMsgLogAddVo(msgLogAddVo);
//            } else {
//                entexit.setOrderStatus(0);
//            }
//
//            entexit.setRoomId(getVo.getRoomShopId());
//            entexit.setRoomBrand(getVo.getBrandName());
//            entexit.setRoomStatus("no_delivered");
//            entexit.setOrderGenTime(now);
//            entexit.setAgreedDeliveryDate(contractInfo.getDeliveryDate());
//            entexit.setSupplieContact(contractInfo.getContactName());
//            entexit.setContactPhone(contractInfo.getContactPhon());
//            entexit.setLeaseTerm(contractInfo.getRentStartDate() + "-" + contractInfo.getRentEndDate());
//            entexit.setDurationContract(contractInfo.getRentEndDate());
//            entexit.setCommercialTypeCode(String.valueOf(contract.getCommercialTypeCode()));
//            entexit.setCommercialTypeName(contract.getCommercialTypeName());
//
//            entexit.setCreateBy(dto.getUpdateBy());
//            entexit.setCreateUser(dto.getUpdateUser());
//            entexit.setCreateUserName(dto.getUpdateUserName());
//            entexit.setCreateTime(now);
//            entexit.setUpdateBy(dto.getUpdateBy());
//            entexit.setUpdateUser(dto.getUpdateUser());
//            entexit.setUpdateUserName(dto.getUpdateUserName());
//            entexit.setUpdateTime(now);
//            commerceEntexitService.saveOrUpdate(entexit);
        }

        //招调详情
        try {
            QueryWrapper<CommercePlanDetailPresent> presentQueryWrapper = new QueryWrapper<>();
            presentQueryWrapper.eq("room_id", getVo.getRoomId());
            CommercePlanDetailPresent update = new CommercePlanDetailPresent();
            update.setRoomStatus(3);
            commercePlanDetailPresentService.update(update, presentQueryWrapper);
        } catch (Exception e) {
            log.error("招调更新异常：{}", e.getMessage());
        }
        return getVo;
    }


    /**
     * 合同审批通过，处理工单数据
     */
    @Override
    public void contractPassWithOrder(Long contractId) {
        CommerceContract contract = this.commerceContractService.getById(contractId);
        //工单状态变更
        QueryWrapper<CommerceOrder> queryWrapperOrder = new QueryWrapper<>();
        queryWrapperOrder.eq("room_id", contract.getRoomId());
        queryWrapperOrder.in("execute_status", Arrays.asList(0, 1, 2));

        LocalDate closeDate = LocalDate.now();
        //fixme: 兼容多条,
        List<CommerceOrder> commerceOrders = commerceOrderService.list(queryWrapperOrder);
        if (!org.springframework.util.CollectionUtils.isEmpty(commerceOrders)) {
            //主工单
            List<String> orderCodes = new ArrayList<>();
            commerceOrders.forEach(e -> {
                e.setExecuteStatus(3);
                e.setCloseDate(closeDate);
                orderCodes.add(e.getOrderCode());
            });
            commerceOrderService.updateBatchById(commerceOrders);

            //其他子工单
            CommerceSubOrder subOrderUpdate = new CommerceSubOrder();
            subOrderUpdate.setExecuteStatus(4);
            subOrderUpdate.setUpdateTime(LocalDateTime.now());

            QueryWrapper<CommerceSubOrder> updateWrapperSub = new QueryWrapper<>();
            updateWrapperSub.ne("execute_user", contract.getExecuteUser());
            updateWrapperSub.in("order_code", orderCodes);
            updateWrapperSub.ne("execute_status", OrderExecuteStatusEnum.OVER.getCode());
            commerceSubOrderService.update(subOrderUpdate, updateWrapperSub);

            //对应子工单
            updateWrapperSub = new QueryWrapper<>();
            subOrderUpdate.setExecuteStatus(3);
            subOrderUpdate.setCloseDate(closeDate);
            updateWrapperSub.in("order_code", orderCodes);
            updateWrapperSub.eq("execute_user", contract.getExecuteUser());
            updateWrapperSub.ne("execute_status", OrderExecuteStatusEnum.OVER.getCode());
            commerceSubOrderService.update(subOrderUpdate, updateWrapperSub);
        }
    }

    /**
     * 新签审批通过的合同，到达租赁开始日期，初始化进撤场工单，变更铺位状态
     */
    @Override
    public CommerceContractArchiveVo contractRentDateStart(CommerceContractArchiveVo contractArchiveVo) {
        CommerceContractInfo contractInfo = commerceContractInfoService.getByContractCode(contractArchiveVo.getContractCode());

        contractArchiveVo.setContractInfo(BeanUtils.copyProperties(contractInfo, CommerceContractInfoGetVo.class));

        //新签生成进场工单
        if (Objects.isNull(contractArchiveVo.getContractInfo().getContractType()) || !contractArchiveVo.getContractInfo().getContractType().equals(1) || Objects.nonNull(contractArchiveVo.getEntexitId())) {
            return contractArchiveVo;
        }

        //新签
        CommerceEntexit entexit = BeanUtils.copyProperties(contractInfo, CommerceEntexit.class);

        entexit.setId(IdWorker.getId());
        entexit.setRoomNo(contractArchiveVo.getName());
        entexit.setShopId(contractArchiveVo.getRoomId());

        //todo:优化
        if (StringUtils.isNotEmpty(contractArchiveVo.getOrderCode())) {
            CommerceOrder order = commerceOrderService.getByOrderCode(contractArchiveVo.getOrderCode());
            entexit.setFid(order.getFid());
            entexit.setFcode(order.getFcode());
            entexit.setFname(order.getFname());
        }

        //获取配置跟进人
        QueryWrapper<CommerceInspectionScope> queryWrapperScope = new QueryWrapper();
        queryWrapperScope.last(PageQuery.LIMIT_ONE);
        queryWrapperScope.like("scope", "\"roomId\": " + contractArchiveVo.getRoomId());
        //查询数据
        CommerceInspectionScope item = commerceInspectionScopeService.getOne(queryWrapperScope);
        if (Objects.nonNull(item)) {
            entexit.setFollowId(item.getUserId());
            entexit.setFollowName(item.getRealName());
            entexit.setOrderStatus(1);

            //营运工单接收
            MsgLogAddVo msgLogAddVo = new MsgLogAddVo();
            msgLogAddVo.setCode("operate_entexit_receive");
            msgLogAddVo.setType(0);
            msgLogAddVo.setUserIds(Arrays.asList(item.getUserId()));
            msgLogAddVo.setTargets(Arrays.asList(item.getUserName()));
            msgLogAddVo.setSendStatus(1);

            Map<String, Object> param = new HashMap<>();
            param.put("storeId", contractArchiveVo.getRoomShopId());
            msgLogAddVo.setTemplateParam(param);

            contractArchiveVo.setMsgLogAddVo(msgLogAddVo);
        } else {
            entexit.setOrderStatus(0);
        }

        LocalDateTime now = LocalDateTime.now();

        entexit.setRoomId(contractArchiveVo.getRoomShopId());
        entexit.setRoomBrandId(contractArchiveVo.getBrandId());
        entexit.setRoomBrand(contractArchiveVo.getBrandName());
        entexit.setRoomStatus("no_delivered");
        entexit.setOrderGenTime(now);
        entexit.setAgreedDeliveryDate(contractInfo.getDeliveryDate());
        entexit.setSupplieContact(contractInfo.getContactName());
        entexit.setContactPhone(contractInfo.getContactPhon());
        entexit.setLeaseTerm(contractInfo.getRentStartDate() + "-" + contractInfo.getRentEndDate());
        entexit.setDurationContract(contractInfo.getRentEndDate());
        entexit.setCommercialTypeCode(String.valueOf(contractArchiveVo.getCommercialTypeCode()));
        entexit.setCommercialTypeName(contractArchiveVo.getCommercialTypeName());

        entexit.setCreateBy(0L);
        entexit.setCreateUser(StringUtils.EMPTY);
        entexit.setCreateUserName(StringUtils.EMPTY);
        entexit.setCreateTime(now);
        entexit.setUpdateBy(0L);
        entexit.setUpdateUser(StringUtils.EMPTY);
        entexit.setUpdateUserName(StringUtils.EMPTY);
        entexit.setUpdateTime(now);
        commerceEntexitService.saveOrUpdate(entexit);

        try {
            UpdateWrapper<CommerceSupplier> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(CommerceSupplier::getId,contractInfo.getSupplierId())
                    .set(CommerceSupplier::getUserType, SupplierUserTypeEnum.OPERATING.getUserType());
            commerceSupplierService.update(updateWrapper);
        }catch (Exception e){
            log.error("在营用户升级异常:{}",e.getMessage(),e);
        }

        return contractArchiveVo;
    }


    @Override
    public CommerceContractStoreGetVo getStoreVoByContractCode(String contractCode) throws ServiceException {
        CommerceContractStoreGetVo getVo = new CommerceContractStoreGetVo();
        CommerceContractInfo contractInfo = commerceContractInfoService.getByContractCode(contractCode);
        if (Objects.nonNull(contractInfo)) {
            getVo.setRentType(contractInfo.getRentType());
            getVo.setBrandCommercialTypeCode(contractInfo.getBrandCommercialTypeCode());
            getVo.setBrandCommercialTypeName(contractInfo.getBrandCommercialTypeName());
            getVo.setBrandCategoryId(contractInfo.getBrandCategoryId());
            getVo.setBrandCategoryName(contractInfo.getBrandCategoryName());
            getVo.setSignEntity(contractInfo.getSignEntity());
            getVo.setRentStartDate(contractInfo.getRentStartDate());
            getVo.setRentEndDate(contractInfo.getRentEndDate());
        }
        CommerceContractRent contractRent = commerceContractRentService.getByContractCode(contractCode);
        if (Objects.nonNull(contractRent)) {
            getVo.setMonthManageFee(contractRent.getMonthManageFeeContract());
            getVo.setMonthOperateFee(contractRent.getMonthOperateFeeContract());
            getVo.setOtherFee(contractRent.getOtherFeeContract());
            getVo.setMonthFee(contractRent.getMonthFeeContract());
            getVo.setMonthFeePercent(contractRent.getMonthFeePercent());

            getVo.setMonthManagePrice(contractRent.getMonthManagePriceContract());
            getVo.setMonthOperatePrice(contractRent.getMonthOperatePriceContract());
            getVo.setOtherPrice(contractRent.getOtherPriceContract());
            getVo.setMonthPrice(contractRent.getMonthPriceContract());
        }
        return getVo;
    }

    @Override
    public void updateApprove(CommerceContractEditDto commerceContractEditDto) throws ServiceException {
        CommerceContract contract = new CommerceContract();
        contract.setId(commerceContractEditDto.getId());
        contract.setApproveStatus(commerceContractEditDto.getApproveStatus());
        contract.setApproveDate(commerceContractEditDto.getApproveDate());
        contract.setApproveRemark(commerceContractEditDto.getApproveRemark());
        contract.setRoomShopId(commerceContractEditDto.getRoomShopId());
        commerceContractService.updateById(contract);
    }

    @Override
    public void updateProcessInstanceId(Long id, String processInstanceId) {
        CommerceContract entity = new CommerceContract();
        entity.setId(id);
        entity.setApproveStatus(ComercePlanApproveStatusEnum.beingProcessed.getCode());
        entity.setProcessInstanceId(processInstanceId);
        this.commerceContractService.updateById(entity);
    }

    @Override
    public List<CommerceContractHistoryListVo> historyList(CommerceContractListDto listDto) throws ServiceException {
        listDto.setApproveStatus(ComercePlanApproveStatusEnum.approve.getCode());
        List<CommerceContract> contractList = commerceContractService.historyList(listDto);
        if (CollectionUtils.isEmpty(contractList)) {
            return Collections.emptyList();
        }
        List<String> contractCodes = contractList.stream().map(CommerceContract::getContractCode).collect(Collectors.toList());
        Map<String, CommerceContractInfo> contractInfoMap = commerceContractInfoService.findContractInfoMap(contractCodes);

        List<CommerceContractHistoryListVo> result = BeanUtils.copyProperties(contractList, CommerceContractHistoryListVo.class);
        result.forEach(e -> {
            if (contractInfoMap.containsKey(e.getContractCode())) {
                org.springframework.beans.BeanUtils.copyProperties(contractInfoMap.get(e.getContractCode()), e);
            }
        });
        return result;
    }

    @Override
    public boolean checkContract(String orderCode) throws ServiceException {
        QueryWrapper<CommerceContract> queryWrapper = new QueryWrapper();
        queryWrapper.eq("order_code", orderCode);
        queryWrapper.in("approve_status", Arrays.asList(ComercePlanApproveStatusEnum.approve.getCode(), ComercePlanApproveStatusEnum.beingProcessed.getCode()));
        long count = commerceContractService.count(queryWrapper);
        return count > 0;
    }

    @Override
    public CommerceContractGetVo selectByRoomIdAndApproveStatus(Long roomId, Integer approveStatus) {
        CommerceContract contract = commerceContractService.selectByRoomIdAndApproveStatus(roomId, approveStatus);
        return BeanUtils.copyProperties(contract, CommerceContractGetVo.class);
    }

    @Override
    public List<CommerceContractBillMonthVo> selectContractByBillMonth(String billMonth) {
        return commerceContractService.selectContractByBillMonth(billMonth);
    }

    @Override
    public List<CommerceContractArchiveVo> findRentStartContract(LocalDate date) {
        return commerceContractService.findRentStartContract(date);
    }

    @Override
    public List<CommerceContractListVo> contractApproveOvertime(LocalDate date) throws ServiceException {
        List<CommerceContract> contractList = commerceContractService.findContractApproveOvertime(date);
        if (CollectionUtils.isEmpty(contractList)) {
            return Collections.emptyList();
        }
        LocalDate today = LocalDate.now();
        for (CommerceContract contract : contractList) {
            contract.setApproveStatus(ComercePlanApproveStatusEnum.rejected.getCode());
            contract.setApproveDate(today);
        }
        commerceContractService.updateBatchById(contractList);

        return BeanUtils.copyProperties(contractList, CommerceContractListVo.class);
    }

    @Override
    public String getShopId(String contractName, Long roomId, boolean isNewRoomShopId) {
        if (isNewRoomShopId) {
            Long count = commerceContractService.getShopIdCount(roomId);
            return contractName + "." + SequenceCodeUtils.getSequenceCode(count.intValue() + 1, 4);
        } else {
            String roomShopId = commerceContractService.getMaxShopId(roomId);
            return roomShopId;
        }
    }

    @Override
    public List<CommerceContractMonthFeeVo> selectContractMonthFee(String type) {
        return commerceContractService.selectContractMonthFee(type);
    }

    @Override
    public void updateRenewContract(String contractCode) {
        CommerceContractInfo contractInfo = commerceContractInfoService.getByContractCode(contractCode);
        if(Objects.isNull(contractInfo.getActRetriveDate())){
            contractInfo.setActRetriveDate(contractInfo.getRentEndDate());
            commerceContractInfoService.updateById(contractInfo);
        }
    }

    @Override
    public CommerceContractGetVo getLastOneContract(Long id) {
        CommerceContract contract = commerceContractService.getLastOneContract(id);
        if(Objects.nonNull(contract)){
            return BeanUtils.copyProperties(contract,CommerceContractGetVo.class);
        }
        return null;
    }

    @Override
    public List<CommerceEffectiveContractVo> selectEffectiveContract(LocalDate currentDate) {
        return commerceContractService.selectEffectiveContract(currentDate);
    }
    @Override
    public List<CommerceEffectiveContractVo> selectEffectiveContractByContractCode(List<String>contractCodeList) {
        return commerceContractService.selectEffectiveContractByContractCode(contractCodeList);
    }
    @Override
    public CommerceEffectiveContractVo getRentFeeCalculateInfo(String contractCode) {
        return commerceContractService.getRentFeeCalculateInfo(contractCode);
    }

    @Override
    public CommerceContractGetVo correct(CommerceContractEditDto dto) throws ServiceException {
        CommerceContract entity = commerceContractService.getById(dto.getId());

        //签约信息
        CommerceContractInfo contractInfo = commerceContractInfoService.getByContractCode(entity.getContractCode());
        org.springframework.beans.BeanUtils.copyProperties(dto.getContractInfo(), contractInfo);

        contractInfo.setBrandId(dto.getBrandId());
        contractInfo.setBrandName(dto.getBrandName());
        contractInfo.setBrandCommercialTypeCode(dto.getCommercialTypeCode());
        contractInfo.setBrandCommercialTypeName(dto.getCommercialTypeName());
        contractInfo.setBrandCategoryId(dto.getCategoryId());
        contractInfo.setBrandCategoryName(dto.getCategoryName());
        commerceContractInfoService.updateById(contractInfo);

        String contactName = contractInfo.getContactName();
        String contactPhone = contractInfo.getContactPhon();
        LocalDate deliveryDate = contractInfo.getDeliveryDate();

        //租赁信息
        CommerceContractRent contractRent = commerceContractRentService.getByContractCode(entity.getContractCode());
        org.springframework.beans.BeanUtils.copyProperties(dto.getContractRent(), contractRent);
        commerceContractRentService.updateById(contractRent);
        // 如果租赁类型改为了纯扣 修改月租金总额为 null
        if (contractInfo.getRentType() != null && contractInfo.getRentType() == 2){
            commerceContractRentService.updateFeeEmpty(contractRent.getId());
        }

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateUser(dto.getUpdateUser());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateTime(nowTime);

        // 进撤场
        CommerceEntexit commerceEntexit = commerceEntexitService.getByContractCode(entity.getContractCode());
        if (commerceEntexit!=null){
            commerceEntexit.setRoomBrandId(dto.getBrandId());
            commerceEntexit.setRoomBrand(dto.getBrandName());
            commerceEntexit.setCommercialTypeCode(dto.getCommercialTypeCode()!=null ? String.valueOf(dto.getCommercialTypeCode()) : null);
            commerceEntexit.setCommercialTypeName(dto.getCommercialTypeName());
            commerceEntexit.setDurationContract(dto.getContractInfo().getRentEndDate());
            String leaseTerm = dto.getContractInfo().getRentStartDate().toString() + "-" + dto.getContractInfo().getRentEndDate().toString();
            commerceEntexit.setLeaseTerm(leaseTerm);
            commerceEntexit.setSupplieContact(contactName);
            commerceEntexit.setContactPhone(contactPhone);
            commerceEntexit.setAgreedDeliveryDate(deliveryDate);
            commerceEntexitService.updateById(commerceEntexit);
        }

        CommerceEntexit entexitDb = commerceEntexitService.getOne(Wrappers.<CommerceEntexit>lambdaQuery().eq(CommerceEntexit::getRenewalId, entity.getId()).eq(CommerceEntexit::getDelStatus, 0));
        if (entexitDb!=null){
            entexitDb.setRoomBrandId(dto.getBrandId());
            entexitDb.setRoomBrand(dto.getBrandName());
            entexitDb.setCommercialTypeCode(dto.getCommercialTypeCode()!=null ? String.valueOf(dto.getCommercialTypeCode()) : null);
            entexitDb.setCommercialTypeName(dto.getCommercialTypeName());
            entexitDb.setDurationContract(dto.getContractInfo().getRentEndDate());
            String leaseTerm = dto.getContractInfo().getRentStartDate().toString() + "-" + dto.getContractInfo().getRentEndDate().toString();
            entexitDb.setLeaseTerm(leaseTerm);
            entexitDb.setSupplieContact(contactName);
            entexitDb.setContactPhone(contactPhone);
            entexitDb.setAgreedDeliveryDate(deliveryDate);
            commerceEntexitService.updateById(entexitDb);
        }

        // 品牌、业态、一级品类修改
        entity.setBrandId(dto.getBrandId());
        entity.setBrandName(dto.getBrandName());
        entity.setCommercialTypeCode(dto.getCommercialTypeCode());
        entity.setCommercialTypeName(dto.getCommercialTypeName());
        entity.setCategoryId(dto.getCategoryId());
        entity.setCategoryName(dto.getCategoryName());
        commerceContractService.updateById(entity);

        return BeanUtils.copyProperties(entity, CommerceContractGetVo.class);
    }

    @Override
    public void editArea(CommerceContractEditDto contractEditDto) {
        CommerceContract contract = new CommerceContract();
        contract.setId(contractEditDto.getId());
        contract.setActualArea(contractEditDto.getActualArea());
        contract.setRentArea(contractEditDto.getRentArea());
        commerceContractService.updateById(contract);
    }

    @Override
    public List<CommerceContractRentFeeExportVo> getContractFeeList(CommerceContractRentFeeExportDto exportDto) {
        return commerceContractService.getContractFeeList(exportDto);
    }

    @Override
    public CommerceContractArchiveVo findSignContract(Long roomId) {
        return commerceContractService.findSignContract(roomId);
    }

    @Override
    public List<CommerceChargeContractListVo> listByReceivable(CommerceChargeContractListDto dto) {
        return commerceContractService.listByReceivable(dto);
    }

    @Override
    public void updateUserInput(CommerceContractUpdateUserInputDto dto) {
        commerceContractService.updateUserInput(dto);
    }

    @Override
    public List<CommerceInvertContractListVo> getAllInvertContract() {
        return  commerceContractService.getAllInvertContract();
    }

    /**
     * <p>构造查询条件<br>
     * <p>默认构造排序条件<br>
     *
     * @param dto 查询条件Dto
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<CommerceContract> queryBuild(CommerceContractListDto dto) throws ServiceException {
        return queryBuild(dto, true);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<CommerceContract> queryBuild(CommerceContractListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<CommerceContract> queryWrapper = new QueryWrapper<>();

        CommerceContract entity = BeanUtils.copyProperties(dto, CommerceContract.class);
        entity.setDelStatus(0);

        if (StringUtils.isNotBlank(dto.getContractCode())) {
            entity.setContractCode(null);
            queryWrapper.like("contract_code", dto.getContractCode());
        }

        if (StringUtils.isNotBlank(dto.getBrandName())) {
            entity.setBrandName(null);
            queryWrapper.like("brand_name", dto.getBrandName());
        }

        if (!CollectionUtils.isEmpty(dto.getContractCodeList())) {
            queryWrapper.in("contract_code", dto.getContractCodeList());
        }

        if (!CollectionUtils.isEmpty(dto.getTenantIdList())) {
            queryWrapper.in("tenant_id", dto.getTenantIdList());
        }

        if (!CollectionUtils.isEmpty(dto.getCategoryIdList())) {
            queryWrapper.in("category_id", dto.getCategoryIdList());
        }

        /** 添加条件样例参考，不用请删除
         if (StringUtils.isNotBlank(dto.getName())) {
         entity.setName(null);
         queryWrapper.like("name", dto.getName());
         }

         queryWrapper.in(dto.getStatusIn() != null, "status", dto.getStatusIn());

         if (orderBy) {
         if (dto.getTypeOrder() != null) {
         queryWrapper.orderBy(true, dto.getTypeOrder().isAsc(), "type");
         }

         queryWrapper.orderByAsc("order_by");
         }
         */
        if (StringUtils.isNotBlank(dto.getOrgFid())) {
            entity.setOrgFid(null);
            queryWrapper.likeRight("org_fid", dto.getOrgFid());
        }

        if (!CollectionUtils.isEmpty(dto.getRoomIdList())) {
            entity.setRoomId(null);
            queryWrapper.in("room_id", dto.getRoomIdList());
        }
        if (!CollectionUtils.isEmpty(dto.getIdList())) {
            entity.setId(null);
            queryWrapper.in("id", dto.getIdList());
        }
        //按创建时间倒序排序，根据需要添加
        queryWrapper.orderByDesc("create_time").orderByAsc("name");

        queryWrapper.setEntity(entity);

        return queryWrapper;
    }
}

package com.seewin.som.report.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 项目面积数据统计到天
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@Getter
@Setter
public class ReportOperateDayListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 房间id
     */
    private Long roomId;

    /**
     * 铺位状态
     */
    private Integer status;

    /**
     * 铺位实用面积
     */
    private Double roomArea;

    /**
     * 项目实用面积
     */
    private Double actualArea;

    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 空间完整编码 楼层编码
     */
    private String fcode;

    /**
     * 空间完整名称
     */
    private String fname;

    /**
     *适配业态字典id
     */
    private Long commercialTypeCode;

    /**
     * 适配业态名称
     */
    private String commercialTypeName;
    /**
     * 所属一级品类
     */
    private Long categoryId;

    /**
     * 所属一级品类名称
     */
    private String categoryName;
    /**
     * 二级类品id
     */
    private Long commercialTwoId;

    /**
     * 二级类品名称
     */
    private String commercialTwo;
    /**
     * 天数
     */
    private Integer day;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private LocalDate startDate;
    private LocalDate endDate;

    private LocalDate date;

    private Long frontCount;

    private Long inShopCount;

    /**
     * 销售总额（元）
     */
    private BigDecimal totalAmount;

    /**
     * 销售总笔数（单）
     */
    private Integer totalOrder;

    /**
     * 租管费（元）
     */
    private BigDecimal theoryRent;

    /**
     * 租赁起始时间
     */
    private LocalDate rentStartDate;

    /**
     * 带看日期
     */
    private LocalDate visitDate;
    /**
     * 招商周期
     */
    private int investCycle;
    /**
     * 经营模式：1 品牌直营，2 品牌代理，3 品牌加盟，4 个体经营
     */
    private Integer businessType;

    /**
     * 净利润
     */
    private BigDecimal netProfit;
}

package com.seewin.som.report.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 盈亏报告-经营概况
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@TableName("som_report_profit_business_overview")
public class ReportProfitBusinessOverview implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 租户名称
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 房间id
     */
    @TableField("store_id")
    private String storeId;

    /**
     * 年份
     */
    @TableField("year")
    private Integer year;

    /**
     * 月份
     */
    @TableField("month")
    private Integer month;

    /**
     * 总销售额-本期数据
     */
    @TableField("current_period")
    private BigDecimal currentPeriod;

    /**
     * 总销售额-同比率
     */
    @TableField("current_period_same_ratio")
    private BigDecimal currentPeriodSameRatio;

    /**
     * 总销售额-环比率
     */
    @TableField("current_period_ring_ratio")
    private BigDecimal currentPeriodRingRatio;

    /**
     * 总成本（元）-本期数据
     */
    @TableField("total_cost")
    private BigDecimal totalCost;

    /**
     * 总成本（元）-同比率
     */
    @TableField("total_cost_same_ratio")
    private BigDecimal totalCostSameRatio;

    /**
     * 总成本（元）-环比率
     */
    @TableField("total_cost_ring_ratio")
    private BigDecimal totalCostRingRatio;

    /**
     * 净利润（元）-本期数据
     */
    @TableField("net_profit")
    private BigDecimal netProfit;

    /**
     * 净利润（元）-同比率
     */
    @TableField("net_profit_same_ratio")
    private BigDecimal netProfitSameRatio;

    /**
     * 净利润（元）-环比率
     */
    @TableField("net_profit_ring_ratio")
    private BigDecimal netProfitRingRatio;

    /**
     * al解读
     */
    @TableField("al_interpret")
    private String alInterpret;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 开始时间
     */
    @TableField("start_date")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @TableField("end_date")
    private LocalDate endDate;
}

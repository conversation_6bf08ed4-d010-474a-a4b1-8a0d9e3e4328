package com.seewin.som.report.vo.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 招商市场报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@Setter
public class ReportInvertMarketAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户名称(项目名称)
     */
    @Schema(description = "租户名称(项目名称)")
    @Size(max=255,message = "租户名称(项目名称)最大长度不能超过255")
    private String tenantName;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @Schema(description = "所属组织ID路径")
    @Size(max=255,message = "所属组织ID路径最大长度不能超过255")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @Schema(description = "所属组织名称路径")
    @Size(max=255,message = "所属组织名称路径最大长度不能超过255")
    private String orgFname;

    /**
     * 开始日期
     */
    @Schema(description = "开始日期")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    private LocalDate endDate;

    /**
     * 0-业态维度 1 品类维度
     */
    @Schema(description = "0-业态维度 1 品类维度")
    private Integer dataType;

    /**
     * 业态ID
     */
    @Schema(description = "业态ID")
    private Long commercialTypeCode;

    /**
     * 业态名称
     */
    @Schema(description = "业态名称")
    @Size(max=255,message = "业态名称最大长度不能超过255")
    private String commercialTypeName;

    /**
     * 一级品类ID
     */
    @Schema(description = "一级品类ID")
    private Long categoryId;

    /**
     * 一级品类名称
     */
    @Schema(description = "一级品类名称")
    @Size(max=255,message = "一级品类名称最大长度不能超过255")
    private String categoryName;

    /**
     * 分析指标
     */
    @Schema(description = "分析指标")
    @Size(max=255,message = "分析指标最大长度不能超过255")
    private String indicator;

    /**
     * 分析指标类型（0-业态占比  1- 一级品类竞争度 2-门前客流 3-进店客流  4-门前客流流量成本  5-门前客流销售转化率  6-一级品类租售比 7-一级品类租管费单价  8-一级品类销售坪效 9-一级品类门前客流销售转化率  10一级品类门前客流  11-一级品类门前客流流量成本  12-一级品类销售笔数）
     */
    @Schema(description = "分析指标类型（0-业态占比  1- 一级品类竞争度 2-门前客流 3-进店客流  4-门前客流流量成本  5-门前客流销售转化率  6-一级品类租售比 7-一级品类租管费单价  8-一级品类销售坪效 9-一级品类门前客流销售转化率  10一级品类门前客流  11-一级品类门前客流流量成本  12-一级品类销售笔数）")
    private Integer indicatorType;

    /**
     * 分析指标描述
     */
    @Schema(description = "分析指标描述")
    @Size(max=255,message = "分析指标描述最大长度不能超过255")
    private String indicatorRemark;

    /**
     * 本期数据
     */
    @Schema(description = "本期数据")
    private BigDecimal currentPeriodData;

    /**
     * 同比数据
     */
    @Schema(description = "同比数据")
    private BigDecimal yearOnYearData;

    /**
     * 同比率
     */
    @Schema(description = "同比率")
    private BigDecimal yearOnYearRate;

    /**
     * 环比数据
     */
    @Schema(description = "环比数据")
    private BigDecimal monthOnMonthData;

    /**
     * 环比率
     */
    @Schema(description = "环比率")
    private BigDecimal monthOnMonthRate;

    /**
     * 均值
     */
    @Schema(description = "均值")
    private BigDecimal meanValue;

    /**
     * 切尾均值
     */
    @Schema(description = "切尾均值")
    private BigDecimal trimmedMean;

    /**
     * 中位数
     */
    @Schema(description = "中位数")
    private BigDecimal median;

    /**
     * 众数
     */
    @Schema(description = "众数")
    private BigDecimal mostFrequentValue;

    /**
     * 标准差
     */
    @Schema(description = "标准差")
    private BigDecimal standardDeviation;

    /**
     * 方差
     */
    @Schema(description = "方差")
    private BigDecimal variance;

    /**
     * 中位数绝对偏差
     */
    @Schema(description = "中位数绝对偏差")
    private BigDecimal medianAbsoluteDeviation;

    /**
     * 极差
     */
    @Schema(description = "极差")
    private BigDecimal dataRange;

    /**
     * 四分位差
     */
    @Schema(description = "四分位差")
    private BigDecimal interquartileRange;

    /**
     * 第一四分位
     */
    @Schema(description = "第一四分位")
    private BigDecimal firstQuartile;

    /**
     * 第三四分位
     */
    @Schema(description = "第三四分位")
    private BigDecimal thirdQuartile;

    /**
     * 结果解读
     */
    @Schema(description = "结果解读")
    @Size(max=65535,message = "结果解读最大长度不能超过65,535")
    private String resultInterpretation;

    @Schema(description = "可视化图表地址")
    private String chartData;
}

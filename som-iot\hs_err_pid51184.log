#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 4161536 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=51184, tid=22720
#
# JRE version:  (21.0.7+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Password for 'https://<EMAIL>': 

Host: Intel(R) Core(TM) i5-9400 CPU @ 2.90GHz, 6 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Mon Jul  7 09:57:51 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 0.031526 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000029878aa5520):  JavaThread "Unknown thread" [_thread_in_vm, id=22720, stack(0x000000d558e00000,0x000000d558f00000) (1024K)]

Stack: [0x000000d558e00000,0x000000d558f00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e6049]
V  [jvm.dll+0x8c4343]
V  [jvm.dll+0x8c689e]
V  [jvm.dll+0x8c6f83]
V  [jvm.dll+0x289266]
V  [jvm.dll+0x6e28c5]
V  [jvm.dll+0x6d634a]
V  [jvm.dll+0x36388b]
V  [jvm.dll+0x36b456]
V  [jvm.dll+0x3bd7fb]
V  [jvm.dll+0x3bdab8]
V  [jvm.dll+0x335fdc]
V  [jvm.dll+0x336ccb]
V  [jvm.dll+0x88b7a9]
V  [jvm.dll+0x3ca9b8]
V  [jvm.dll+0x8747f8]
V  [jvm.dll+0x45f3ce]
V  [jvm.dll+0x4610b1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff9958cb148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x000002987af10a70 WorkerThread "GC Thread#0"                     [id=47388, stack(0x000000d558f00000,0x000000d559000000) (1024K)]
  0x000002987af217d0 ConcurrentGCThread "G1 Main Marker"            [id=51660, stack(0x000000d559000000,0x000000d559100000) (1024K)]
  0x000002987af23150 WorkerThread "G1 Conc#0"                       [id=50276, stack(0x000000d559100000,0x000000d559200000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff994fb90b7]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff99593fab8] Heap_lock - owner thread: 0x0000029878aa5520

Heap address: 0x0000000702800000, size: 4056 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000702800000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000002987f7f0000,0x000002987ffe0000] _byte_map_base: 0x000002987bfdc000

Marking Bits: (CMBitMap*) 0x000002987af11170
 Bits: [0x000002980f000000, 0x0000029812f60000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.008 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6226a0000 - 0x00007ff6226aa000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.exe
0x00007ffa153e0000 - 0x00007ffa15645000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa143f0000 - 0x00007ffa144b9000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa12910000 - 0x00007ffa12cf8000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa13030000 - 0x00007ffa1317b000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa0aa20000 - 0x00007ffa0aa38000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jli.dll
0x00007ffa13d70000 - 0x00007ffa13f3a000 	C:\Windows\System32\USER32.dll
0x00007ffa12e80000 - 0x00007ffa12ea7000 	C:\Windows\System32\win32u.dll
0x00007ffa144c0000 - 0x00007ffa144eb000 	C:\Windows\System32\GDI32.dll
0x00007ffa12530000 - 0x00007ffa12667000 	C:\Windows\System32\gdi32full.dll
0x00007ffa12860000 - 0x00007ffa12903000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa0dfd0000 - 0x00007ffa0dfeb000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\VCRUNTIME140.dll
0x00007ff9e2090000 - 0x00007ff9e232a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffa146b0000 - 0x00007ffa14759000 	C:\Windows\System32\msvcrt.dll
0x00007ffa148f0000 - 0x00007ffa14920000 	C:\Windows\System32\IMM32.DLL
0x00007ffa0d970000 - 0x00007ffa0d97c000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\vcruntime140_1.dll
0x00007ff9efac0000 - 0x00007ff9efb4d000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\msvcp140.dll
0x00007ff994c70000 - 0x00007ff995a31000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\server\jvm.dll
0x00007ffa14760000 - 0x00007ffa14813000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa13330000 - 0x00007ffa133d6000 	C:\Windows\System32\sechost.dll
0x00007ffa14ab0000 - 0x00007ffa14bc5000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa13560000 - 0x00007ffa135d4000 	C:\Windows\System32\WS2_32.dll
0x00007ffa122d0000 - 0x00007ffa1232e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa04c10000 - 0x00007ffa04c45000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa0e190000 - 0x00007ffa0e19b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa122b0000 - 0x00007ffa122c4000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa112f0000 - 0x00007ffa1130b000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa0d8b0000 - 0x00007ffa0d8ba000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jimage.dll
0x00007ffa06230000 - 0x00007ffa06471000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa13980000 - 0x00007ffa13d05000 	C:\Windows\System32\combase.dll
0x00007ffa14930000 - 0x00007ffa14a11000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa03bf0000 - 0x00007ffa03c29000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa12670000 - 0x00007ffa12709000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa0aa40000 - 0x00007ffa0aa60000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\IntelliJ IDEA 2025.1.3\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;D:\IntelliJ IDEA 2025.1.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Password for 'https://<EMAIL>': 
java_class_path (initial): D:/IntelliJ IDEA 2025.1.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/IntelliJ IDEA 2025.1.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 6                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4253024256                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4253024256                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\jdk-17.0.10
CLASSPATH=.;D:\jdk-17.0.10\lib;D:\jdk-17.0.10\lib\tools.jar
PATH=D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;D:\ShadowBot;D:\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\jdk-17.0.10\bin;E:\JavaSpace\peizhi\apache-maven-3.5.4\apache-maven-3.5.4\bin;D:\Git\cmd;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;D:\cursor\resources\app\bin;E:\node;C:\Program Files\dotnet;D:\ShadowBot;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Microsoft VS Code\bin;D:\IntelliJ IDEA 2025.1.3\bin
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 14156K (0% of 16609336K total physical memory with 2408036K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 25 days 14:26 hours

CPU: total 6 (initial active 6) (6 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xde, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 6 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 16220M (2351M free)
TotalPageFile size 32693M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 311M, peak: 314M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130) for windows-amd64 JRE (21.0.7+9-b895.130), built on 2025-05-13 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.

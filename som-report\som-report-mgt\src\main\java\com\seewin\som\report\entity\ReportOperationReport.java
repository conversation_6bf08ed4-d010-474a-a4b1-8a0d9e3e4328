package com.seewin.som.report.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 项目运营报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@TableName("som_report_operation_report")
public class ReportOperationReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id(项目id)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 年份
     */
    @TableField("year")
    private Integer year;

    /**
     * 月份
     */
    @TableField("month")
    private Integer month;

    /**
     * 分析指标
     */
    @TableField("indicator")
    private String indicator;

    /**
     * 分析指标类型（0-门前客流（人）1-进店客流（人） 2-销售额（元）3-租管费（元）4-租售比（%））
     */
    @TableField("indicator_type")
    private Integer indicatorType;

    /**
     * 本期数据
     */
    @TableField("current_period_data")
    private BigDecimal currentPeriodData;

    /**
     * 同比数据
     */
    @TableField("year_on_year_data")
    private BigDecimal yearOnYearData;

    /**
     * 同比率
     */
    @TableField("year_on_year_rate")
    private BigDecimal yearOnYearRate;

    /**
     * 环比数据
     */
    @TableField("month_on_month_data")
    private BigDecimal monthOnMonthData;

    /**
     * 环比率
     */
    @TableField("month_on_month_rate")
    private BigDecimal monthOnMonthRate;

    /**
     * 均值
     */
    @TableField("mean_value")
    private BigDecimal meanValue;

    /**
     * 切尾均值
     */
    @TableField("trimmed_mean")
    private BigDecimal trimmedMean;

    /**
     * 中位数
     */
    @TableField("median")
    private BigDecimal median;

    /**
     * 众数
     */
    @TableField("most_frequent_value")
    private BigDecimal mostFrequentValue;

    /**
     * 标准差
     */
    @TableField("standard_deviation")
    private BigDecimal standardDeviation;

    /**
     * 方差
     */
    @TableField("variance")
    private BigDecimal variance;

    /**
     * 中位数绝对偏差
     */
    @TableField("median_absolute_deviation")
    private BigDecimal medianAbsoluteDeviation;

    /**
     * 极差
     */
    @TableField("data_range")
    private BigDecimal dataRange;

    /**
     * 四分位差
     */
    @TableField("interquartile_range")
    private BigDecimal interquartileRange;

    /**
     * 第一四分位
     */
    @TableField("first_quartile")
    private BigDecimal firstQuartile;

    /**
     * 第三四分位
     */
    @TableField("third_quartile")
    private BigDecimal thirdQuartile;

    /**
     * 排名top5
     */
    @TableField("top_five_rank")
    private String topFiveRank;

    /**
     * 排名bottom5
     */
    @TableField("bottom_five_rank")
    private String bottomFiveRank;

    /**
     * 结果解读
     */
    @TableField("result_interpretation")
    private String resultInterpretation;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 可视化图表地址
     */
    @TableField("chart_data")
    private String chartData;

    /**
     * 开始时间
     */
    @TableField("start_date")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @TableField("end_date")
    private LocalDate endDate;
}

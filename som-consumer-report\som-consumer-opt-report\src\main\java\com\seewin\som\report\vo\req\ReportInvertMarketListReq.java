package com.seewin.som.report.vo.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import com.seewin.consumer.data.ApiPageReq;

/**
 * <p>
 * 招商市场报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@Setter
public class ReportInvertMarketListReq extends ApiPageReq {



    /**
     * 开始日期
     */
    @Schema(description = "开始日期")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期")
    private LocalDate endDate;

    /**
     * 0-业态维度 1 品类维度
     */
    @Schema(description = "0-业态维度 1 品类维度")
    private Integer dataType;

    /**
     * 业态ID
     */
    @Schema(description = "业态ID")
    private Long commercialTypeCode;

    /**
     * 业态名称
     */
    @Schema(description = "业态名称")
    private String commercialTypeName;

    /**
     * 一级品类ID
     */
    @Schema(description = "一级品类ID")
    private Long categoryId;

    /**
     * 一级品类名称
     */
    @Schema(description = "一级品类名称")
    private String categoryName;


}

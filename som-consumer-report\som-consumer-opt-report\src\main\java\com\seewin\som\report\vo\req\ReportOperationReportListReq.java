package com.seewin.som.report.vo.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import com.seewin.consumer.data.ApiPageReq;

/**
 * <p>
 * 项目运营报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Getter
@Setter
public class ReportOperationReportListReq extends ApiPageReq {



    /**
     * 年份
     */
    @Schema(description = "年份")
    private Integer year;

    /**
     * 月份
     */
    @Schema(description = "月份")
    private Integer month;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private LocalDate endDate;


}

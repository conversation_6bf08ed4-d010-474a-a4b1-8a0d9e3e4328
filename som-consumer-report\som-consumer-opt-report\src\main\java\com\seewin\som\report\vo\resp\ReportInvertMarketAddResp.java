package com.seewin.som.report.vo.resp;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招商市场报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@Setter
public class ReportInvertMarketAddResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    private Long id;
}

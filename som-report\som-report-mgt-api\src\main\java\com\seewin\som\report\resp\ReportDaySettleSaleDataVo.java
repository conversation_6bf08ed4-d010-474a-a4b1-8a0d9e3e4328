package com.seewin.som.report.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 修改前日结单销售数据
 * 不允许在此类添加新的字段
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Getter
@Setter
public class ReportDaySettleSaleDataVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 销售总额（元）
     */
    private BigDecimal totalAmount;

    /**
     * 销售总笔数（单）
     */
    private Integer totalOrder;

    /**
     * 门店销售额（元）
     */
    private BigDecimal storeAmount;

    /**
     * 门店销售笔数（单）
     */
    private Integer storeOrder;

    /**
     * 外卖销售额（元）
     */
    private BigDecimal takeawayAmount;

    /**
     * 外卖销售笔数（单）
     */
    private Integer takeawayOrder;

}

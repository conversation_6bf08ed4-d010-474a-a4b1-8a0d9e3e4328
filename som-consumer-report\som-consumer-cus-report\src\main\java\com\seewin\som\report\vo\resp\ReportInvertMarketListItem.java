package com.seewin.som.report.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 招商市场报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@Setter
public class ReportInvertMarketListItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 分析指标
     */
    @Schema(description = "分析指标")
    private String indicator;
    /**
     * 分析指标类型（0-业态占比  1- 一级品类竞争度 2-门前客流 3-进店客流  4-门前客流流量成本  5-门前客流销售转化率  6-一级品类租售比 7-一级品类租管费单价  8-一级品类销售坪效 9-一级品类门前客流销售转化率  10一级品类门前客流  11-一级品类门前客流流量成本  12-一级品类销售笔数）
     */
    @Schema(description = "分析指标类型（0-业态占比  1- 一级品类竞争度 2-门前客流 3-进店客流  4-门前客流流量成本  5-门前客流销售转化率  6-一级品类租售比 7-一级品类租管费单价  8-一级品类销售坪效 9-一级品类门前客流销售转化率  10一级品类门前客流  11-一级品类门前客流流量成本  12-一级品类销售笔数）")
    private Integer indicatorType;
    /**
     * 分析指标描述
     */
    @Schema(description = "分析指标描述")
    private String indicatorRemark;
    /**
     * 本期数据
     */
    @Schema(description = "本期数据")
    private BigDecimal currentPeriodData;
    /**
     * 同比数据
     */
    @Schema(description = "同比数据")
    private BigDecimal yearOnYearData;
    /**
     * 同比率
     */
    @Schema(description = "同比率")
    private BigDecimal yearOnYearRate;
    /**
     * 环比数据
     */
    @Schema(description = "环比数据")
    private BigDecimal monthOnMonthData;
    /**
     * 环比率
     */
    @Schema(description = "环比率")
    private BigDecimal monthOnMonthRate;
    /**
     * 均值
     */
    @Schema(description = "均值")
    private BigDecimal meanValue;
    /**
     * 切尾均值
     */
    @Schema(description = "切尾均值")
    private BigDecimal trimmedMean;
    /**
     * 中位数
     */
    @Schema(description = "中位数")
    private BigDecimal median;
    /**
     * 众数
     */
    @Schema(description = "众数")
    private BigDecimal mostFrequentValue;
    /**
     * 标准差
     */
    @Schema(description = "标准差")
    private BigDecimal standardDeviation;
    /**
     * 方差
     */
    @Schema(description = "方差")
    private BigDecimal variance;
    /**
     * 中位数绝对偏差
     */
    @Schema(description = "中位数绝对偏差")
    private BigDecimal medianAbsoluteDeviation;
    /**
     * 极差
     */
    @Schema(description = "极差")
    private BigDecimal dataRange;
    /**
     * 四分位差
     */
    @Schema(description = "四分位差")
    private BigDecimal interquartileRange;
    /**
     * 第一四分位
     */
    @Schema(description = "第一四分位")
    private BigDecimal firstQuartile;
    /**
     * 第三四分位
     */
    @Schema(description = "第三四分位")
    private BigDecimal thirdQuartile;
    /**
     * 结果解读
     */
    @Schema(description = "结果解读")
    private String resultInterpretation;

    @Schema(description = "可视化图表地址")
    private String chartData;
}

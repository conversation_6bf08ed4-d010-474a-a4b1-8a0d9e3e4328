package com.seewin.som.rent.provider;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.som.rent.resp.RentChargingBaseListVo;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.rent.req.RentChargingStandardStoreAddDto;
import com.seewin.som.rent.req.RentChargingStandardStoreEditDto;
import com.seewin.som.rent.req.RentChargingStandardStoreListDto;
import com.seewin.som.rent.req.RentChargingStandardStoreBindDto;
import com.seewin.som.rent.resp.RentChargingStandardStoreAddVo;
import com.seewin.som.rent.resp.RentChargingStandardStoreGetVo;
import com.seewin.som.rent.resp.RentChargingStandardStoreListVo;


import java.util.List;

/**
 * <p>
 * 收费标准关联门店表 API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
public interface RentChargingStandardStoreProvider {

	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    PageResult<RentChargingStandardStoreListVo> page(PageQuery<RentChargingStandardStoreListDto> pageQuery) throws ServiceException;

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    List<RentChargingStandardStoreListVo> list(RentChargingStandardStoreListDto dto) throws ServiceException;

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    int count(RentChargingStandardStoreListDto dto) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    RentChargingStandardStoreGetVo get(Long id) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    RentChargingStandardStoreGetVo get(RentChargingStandardStoreListDto dto) throws ServiceException;




    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    RentChargingStandardStoreAddVo add(RentChargingStandardStoreAddDto dto) throws ServiceException;


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    void edit(RentChargingStandardStoreEditDto dto) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    void delete(Long id) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    void delete(RentChargingStandardStoreListDto dto) throws ServiceException;

    /**
     * <p>合同关联的标准、项目详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    List<RentChargingBaseListVo> getRentChargingBaseList(RentChargingStandardStoreListDto dto) throws ServiceException;

    /**
     * 批量删除
     * @param idList
     * @return
     * @throws ServiceException
     */
    boolean removeBatchByIds(List<Long> idList) throws ServiceException;

    /**
     * 批量新增
     * @param dtoList
     * @return
     * @throws ServiceException
     */
    boolean saveBatch(List<RentChargingStandardStoreAddDto> dtoList) throws ServiceException;

    void deleteByContactCodes(List<String> contractCodes);
}

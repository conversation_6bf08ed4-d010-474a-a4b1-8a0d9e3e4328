package com.seewin.som.report.provider;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.report.req.ReportInvertMarketAddDto;
import com.seewin.som.report.req.ReportInvertMarketEditDto;
import com.seewin.som.report.req.ReportInvertMarketListDto;
import com.seewin.som.report.resp.ReportInvertMarketAddVo;
import com.seewin.som.report.resp.ReportInvertMarketGetVo;
import com.seewin.som.report.resp.ReportInvertMarketListVo;

import java.util.List;

/**
 * <p>
 * 招商市场报告表 API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface ReportInvertMarketProvider {

	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    PageResult<ReportInvertMarketListVo> page(PageQuery<ReportInvertMarketListDto> pageQuery) throws ServiceException;

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    List<ReportInvertMarketListVo> list(ReportInvertMarketListDto dto) throws ServiceException;

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    int count(ReportInvertMarketListDto dto) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportInvertMarketGetVo get(Long id) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportInvertMarketGetVo get(ReportInvertMarketListDto dto) throws ServiceException;


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    ReportInvertMarketAddVo add(ReportInvertMarketAddDto dto) throws ServiceException;


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    void edit(ReportInvertMarketEditDto dto) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    void delete(Long id) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    void delete(ReportInvertMarketListDto dto) throws ServiceException;
}

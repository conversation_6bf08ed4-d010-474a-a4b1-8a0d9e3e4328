alter table som_report.som_report_operation_report add column end_date  date comment '结束日期'after month;
alter table som_report.som_report_operation_report add column start_date date comment '开始日期'after month;

alter table som_report.som_report_profit_business_overview add column end_date  date comment '结束日期'after month;
alter table som_report.som_report_profit_business_overview add column start_date date comment '开始日期'after month;

alter table som_report.som_report_profit_cost_analysis add column end_date  date comment '结束日期'after month;
alter table som_report.som_report_profit_cost_analysis add column start_date date comment '开始日期'after month;

alter table som_report.som_report_profit_revenue_analysis add column end_date  date comment '结束日期'after month;
alter table som_report.som_report_profit_revenue_analysis add column start_date date comment '开始日期'after month;


drop table if exists som_report.som_report_invert_market;

/*==============================================================*/
/* Table: som_report_invert_market                           */
/*==============================================================*/
create table som_report.som_report_invert_market
(
    id                   bigint not null comment '主键',
    tenant_id            bigint comment '租户id(项目id)',
    tenant_name          varchar(255) comment '租户名称(项目名称)',
    ent_id               bigint comment '企业ID',
    org_fid              varchar(255) comment '所属组织ID路径',
    org_fname            varchar(255) comment '所属组织名称路径',
    start_date					 date comment '开始日期',
    end_date						 date comment '结束日期',
    data_type            int comment '0-业态维度 1 品类维度',
    commercial_type_code  bigint  comment '业态ID',
    commercial_type_name 	varchar(255) comment '业态名称',
    category_id          	bigint  comment '一级品类ID',
    category_name    			varchar(255) comment '一级品类名称',
    indicator            	varchar(255) comment '分析指标',
    indicator_type       	int comment '分析指标类型（0-业态占比  1- 一级品类竞争度 2-门前客流 3-进店客流  4-门前客流流量成本  5-门前客流销售转化率  6-一级品类租售比 7-一级品类租管费单价  8-一级品类销售坪效 9-一级品类门前客流销售转化率  10一级品类门前客流  11-一级品类门前客流流量成本  12-一级品类销售笔数）',
    indicator_remark     varchar(255) comment '分析指标描述',
    current_period_data  decimal(20,2) comment '本期数据',
    year_on_year_data    decimal(20,2) comment '同比数据',
    year_on_year_rate    decimal(20,2) comment '同比率',
    month_on_month_data  decimal(20,2) comment '环比数据',
    month_on_month_rate  decimal(20,2) comment '环比率',
    mean_value           decimal(20,2) comment '均值',
    trimmed_mean         decimal(20,2) comment '切尾均值',
    median               decimal(20,2) comment '中位数',
    most_frequent_value  decimal(20,2) comment '众数',
    standard_deviation   decimal(20,2) comment '标准差',
    variance             decimal(20,2) comment '方差',
    median_absolute_deviation decimal(20,2) comment '中位数绝对偏差',
    data_range           decimal(20,2) comment '极差',
    interquartile_range  decimal(20,2) comment '四分位差',
    first_quartile       decimal(20,2) comment '第一四分位',
    third_quartile       decimal(20,2) comment '第三四分位',
    result_interpretation text comment '结果解读',
    chart_data          text comment '可视化图表地址',
    create_by            bigint comment '创建人id',
    create_user          varchar(32) comment '创建人账号/手机号',
    create_user_name     varchar(32) comment '创建人姓名/昵称',
    create_time          datetime comment '创建时间',
    update_by            bigint comment '修改人id',
    update_user          varchar(32) comment '修改人账号/手机号',
    update_user_name     varchar(32) comment '修改人姓名/昵称',
    update_time          datetime comment '修改时间',
    del_status           int comment '是否已删除: 0-否，1-是',
    version              int comment '乐观锁',
    primary key (id)
);

alter table som_report.som_report_invert_market comment '招商市场报告表';

INSERT INTO `som_system`.`som_sys_api`(`id`, `service_code`, `service_name`, `api_code`, `api_name`, `api_path`, `api_type`, `api_level`, `api_source`, `auth_type`, `parent_id`, `parent_name`, `log_enable`, `remark`, `sort_num`, `version`, `platform`) VALUES (1784855976882400226, 'report', '报表统计(小程序端)', 'cus:report:reportmobilesaledata:getInvertMarketReport', '拓店市场报告', '/reportMobileSaleData/getInvertMarketReport', 3, 1, 13, 2, 1784855976759816193, '主数据库销售数据', 1, '', 7, NULL, 'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, `sort_num`, `version`, `platform`)
VALUES (1935971518123229185, 'report', '报表管理', 'opt:report:reportinvertmarket', '招商市场报告表', '', 2, 0, 11, 2, 0, '-', 0, '', 0, NULL, 'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, `sort_num`, `version`, `platform`)
VALUES (1935971518135812097, 'report', '报表管理', 'opt:report:reportinvertmarket:page', '招商市场报告表分页', '/reportInvertMarket/page', 2, 1, 11, 2, 1935971518123229185, '招商市场报告表', 0, '', 1, NULL, 'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, `sort_num`, `version`, `platform`)
VALUES (1935971518135812098, 'report', '报表管理', 'opt:report:reportinvertmarket:add', '招商市场报告表新增', '/reportInvertMarket/add', 2, 1,  11, 2, 1935971518123229185, '招商市场报告表', 0, '', 2, NULL, 'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, `sort_num`, `version`, `platform`)
VALUES (1935971518135812099, 'report', '报表管理', 'opt:report:reportinvertmarket:edit', '招商市场报告表编辑', '/reportInvertMarket/edit', 2, 1,  11, 2, 1935971518123229185, '招商市场报告表', 0, '', 3, NULL, 'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, `sort_num`, `version`, `platform`)
VALUES (1935971518135812100, 'report', '报表管理', 'opt:report:reportinvertmarket:get', '招商市场报告表查看', '/reportInvertMarket/get', 2, 1,  11, 2, 1935971518123229185, '招商市场报告表', 0, '', 4, NULL, 'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, `sort_num`, `version`, `platform`)
VALUES (1935971518135812101, 'report', '报表管理', 'opt:report:reportinvertmarket:del', '招商市场报告表删除', '/reportInvertMarket/del', 2, 1,  11, 2, 1935971518123229185, '招商市场报告表', 0, '', 5, NULL, 'som');

INSERT INTO `som_system`.`som_sys_api` (id, service_code, service_name, api_code, api_name, api_path, api_type, api_level, api_source, auth_type, parent_id, parent_name, log_enable, remark, `sort_num`, `version`, `platform`)
VALUES (1935971518135812102, 'report', '报表管理', 'opt:report:reportinvertmarket:list', '招商市场报告表列表', '/reportInvertMarket/list', 2, 1,  11, 2, 1935971518123229185, '招商市场报告表', 0, '', 6, NULL, 'som');
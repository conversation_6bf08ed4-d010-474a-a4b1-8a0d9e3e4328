package com.seewin.som.rent.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.seewin.som.rent.enums.DataSourceEnum;
import com.seewin.som.rent.provider.RentChargingItemProvider;
import com.seewin.som.rent.provider.RentChargingStandardParamsProvider;
import com.seewin.som.rent.provider.RentChargingStandardStoreProvider;
import com.seewin.som.rent.req.*;
import com.seewin.som.rent.resp.*;
import com.seewin.som.rent.service.RentChargingStandardService;
import com.seewin.som.rent.vo.req.*;
import com.seewin.som.rent.vo.resp.*;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import com.seewin.som.rent.provider.RentChargingStandardProvider;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 收费标准表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Service
public class RentChargingStandardServiceImpl implements RentChargingStandardService {

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingStandardProvider rentChargingStandardProvider;

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingStandardParamsProvider rentChargingStandardParamsProvider;

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingStandardStoreProvider rentChargingStandardStoreProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingItemProvider rentChargingItemProvider;


    /**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<RentChargingStandardListItem> page(RentChargingStandardListReq listReq) {
        PageResp<RentChargingStandardListItem> pageResp = new PageResp<>();
        User curUser = ApiUtils.getUser(User.class);

        RentChargingStandardListDto queryDto = BeanUtils.copyProperties(listReq, RentChargingStandardListDto.class);

        PageQuery<RentChargingStandardListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<RentChargingStandardListVo> pageResult = rentChargingStandardProvider.page(pageQuery);

        List<RentChargingStandardListItem> newStandardListItemList = new ArrayList<>();

        // 处理参数
        if (CollectionUtil.isNotEmpty(pageResult.getItems())) {

            for (RentChargingStandardListVo rentChargingStandardListVo : pageResult.getItems()) {
                RentChargingStandardListItem rentChargingStandardListItem = BeanUtils.copyProperties(rentChargingStandardListVo, RentChargingStandardListItem.class);
                // 查询参数
                RentChargingStandardParamsListDto paramsListDto = new RentChargingStandardParamsListDto();
                paramsListDto.setChargingStandardId(rentChargingStandardListVo.getId());
                List<RentChargingStandardParamsListVo> standardParamsListVoList = rentChargingStandardParamsProvider.list(paramsListDto);
                // 修复bug 库里有很多重复的param 使用List会查出一堆  但一个本金标准只对应一个param才对
                if (!standardParamsListVoList.isEmpty()) {
                    standardParamsListVoList = standardParamsListVoList.subList(0, 1);
                }
                if (CollectionUtil.isNotEmpty(standardParamsListVoList)) {
                    rentChargingStandardListItem.setStandardParams(BeanUtils.copyProperties(standardParamsListVoList, RentChargingStandardParamsListItem.class));
                }
                newStandardListItemList.add(rentChargingStandardListItem);
            }
        }

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(newStandardListItemList);

        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public RentChargingStandardGetResp get(RentChargingStandardGetReq getReq) {
        RentChargingStandardGetVo getVo = rentChargingStandardProvider.get(getReq.getId());

        RentChargingStandardGetResp getResp = BeanUtils.copyProperties(getVo, RentChargingStandardGetResp.class);

        // 查询参数
        RentChargingStandardParamsListDto paramsListDto = new RentChargingStandardParamsListDto();
        paramsListDto.setChargingStandardId(getReq.getId());
        List<RentChargingStandardParamsListVo> standardParamsListVoList = rentChargingStandardParamsProvider.list(paramsListDto);
        if (CollectionUtil.isNotEmpty(standardParamsListVoList)) {
            getResp.setStandardParams(BeanUtils.copyProperties(standardParamsListVoList, RentChargingStandardParamsGetResp.class));
        }

        return getResp;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public RentChargingStandardAddResp add(RentChargingStandardAddReq addReq) {
        RentChargingStandardAddDto dto = BeanUtils.copyProperties(addReq, RentChargingStandardAddDto.class);

        //设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());


        RentChargingStandardAddVo addVo = rentChargingStandardProvider.add(dto);
        // 标准id
        Long standardId = addVo.getId();
        // 添加参数
        if (CollectionUtil.isNotEmpty(addReq.getStandardParams())) {
            for (RentChargingStandardParamsAddReq paramsAddReq : addReq.getStandardParams()) {
                paramsAddReq.setChargingStandardId(standardId);
                RentChargingStandardParamsAddDto paramsAddDto = BeanUtils.copyProperties(paramsAddReq, RentChargingStandardParamsAddDto.class);
                rentChargingStandardParamsProvider.add(paramsAddDto);
            }
        }

        // 新增参数
        RentChargingStandardAddResp addResp = BeanUtils.copyProperties(addVo, RentChargingStandardAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(RentChargingStandardEditReq editReq) {
        RentChargingStandardEditDto dto = BeanUtils.copyProperties(editReq, RentChargingStandardEditDto.class);

        //设置修改人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        rentChargingStandardProvider.edit(dto);

        // 编辑参数 先删后增
        // 标准id
        Long standardId = editReq.getId();
        if (CollectionUtil.isNotEmpty(editReq.getStandardParams())) {

            List<Long> removeIdList = new ArrayList<>();

            // 查询参数
            RentChargingStandardParamsListDto paramsListDto = new RentChargingStandardParamsListDto();
            paramsListDto.setChargingStandardId(editReq.getId());
            List<RentChargingStandardParamsListVo> oldList = rentChargingStandardParamsProvider.list(paramsListDto);
            if (CollectionUtil.isNotEmpty(oldList)) {
                for (RentChargingStandardParamsListVo old : oldList) {
                    removeIdList.add(old.getId());
                }
            }

            // 批量删除
            rentChargingStandardParamsProvider.removeBatchByIds(removeIdList);

            List<RentChargingStandardParamsAddDto> addList = new ArrayList<>();

            // 添加参数
            for (RentChargingStandardParamsAddReq paramsAddReq : editReq.getStandardParams()) {
                RentChargingStandardParamsAddDto paramsAddDto = BeanUtils.copyProperties(paramsAddReq, RentChargingStandardParamsAddDto.class);
                paramsAddReq.setChargingStandardId(standardId);
                addList.add(paramsAddDto);
            }
            rentChargingStandardParamsProvider.saveBatch(addList);
        }
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(RentChargingStandardDelReq delReq) {
        rentChargingStandardProvider.delete(delReq.getId());
        // 删除标准关联门店，和标准关联参数

        // 批量删除标准关联门店
        List<Long> removeStandardStoreIdList = new ArrayList<>();
        RentChargingStandardStoreListDto standardStoreListDto = new RentChargingStandardStoreListDto();
        standardStoreListDto.setChargingStandardId(delReq.getId());
        List<RentChargingStandardStoreListVo> oldStandardStoreList = rentChargingStandardStoreProvider.list(standardStoreListDto);
        if (CollectionUtil.isNotEmpty(oldStandardStoreList)) {
            for (RentChargingStandardStoreListVo old : oldStandardStoreList) {
                removeStandardStoreIdList.add(old.getId());
            }
        }
        // 批量删除标准关联门店
        rentChargingStandardStoreProvider.removeBatchByIds(removeStandardStoreIdList);

        // 批量删除标准关联参数
        List<Long> removeStandardParamsIdList = new ArrayList<>();
        RentChargingStandardParamsListDto paramsListDto = new RentChargingStandardParamsListDto();
        paramsListDto.setChargingStandardId(delReq.getId());
        List<RentChargingStandardParamsListVo> oldStandardParamsList = rentChargingStandardParamsProvider.list(paramsListDto);
        if (CollectionUtil.isNotEmpty(oldStandardParamsList)) {
            for (RentChargingStandardParamsListVo old : oldStandardParamsList) {
                removeStandardParamsIdList.add(old.getId());
            }
        }
        // 批量删除标准关联参数
        rentChargingStandardParamsProvider.removeBatchByIds(removeStandardParamsIdList);
    }

    /**
     * <p>公式查询<br>
     *
     * @param expReq
     */
    @Override
    public List<RentChargingStandardListItem> exp(RentChargingStandardExpReq expReq) {
        RentChargingStandardListDto listDto = new RentChargingStandardListDto();
        if (expReq.getChargingItemId() != null) {
            listDto.setChargingItemId(expReq.getChargingItemId());
        } else {
            // 如果没有传入收费项目id，且类型是本金，则只返回有参数的公式
            if (expReq.getType() == 1) {
                listDto.setParamInputFlag(1);
            }
        }
        listDto.setType(expReq.getType());
        // 过滤出系统内置的规则和公式
        listDto.setDataSource(DataSourceEnum.SYS_DEFINE.getCode());

        List<RentChargingStandardListVo> ret = rentChargingStandardProvider.list(listDto);
        List<RentChargingStandardListItem> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(ret)) {
            for (RentChargingStandardListVo vo : ret) {
                RentChargingStandardListItem standardListItem = BeanUtils.copyProperties(vo, RentChargingStandardListItem.class);
                // 查询参数
                RentChargingStandardParamsListDto paramsListDto = new RentChargingStandardParamsListDto();
                paramsListDto.setChargingStandardId(vo.getId());
                List<RentChargingStandardParamsListVo> standardParamsListVoList = rentChargingStandardParamsProvider.list(paramsListDto);
                if (CollectionUtil.isNotEmpty(standardParamsListVoList)) {
                    standardListItem.setStandardParams(BeanUtils.copyProperties(standardParamsListVoList, RentChargingStandardParamsListItem.class));
                }
                result.add(standardListItem);
            }
        }
        return result;

    }

    @Override
    public List<RentChargingStandardListAllItem> listAll(RentChargingStandardListAllReq req) {
        List<RentChargingStandardListAllItem> result = new ArrayList<>();
        // 项目
        RentChargingItemListDto rentChargingItemListDto = new RentChargingItemListDto();
        rentChargingItemListDto.setType(req.getType());
        List<RentChargingItemListVo> chargingItemList = rentChargingItemProvider.list(rentChargingItemListDto);
        Map<Long, RentChargingItemListVo> itemIdVoMap = new HashMap<>();
        List<Long> allItemIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(chargingItemList)) {
            for (RentChargingItemListVo item : chargingItemList) {
                allItemIdList.add(item.getId());
                itemIdVoMap.put(item.getId(), item);
            }
        }
        // 标准
        RentChargingStandardListDto rentChargingStandardListDto = new RentChargingStandardListDto();
        rentChargingStandardListDto.setType(req.getType());
        rentChargingStandardListDto.setChargingItemIdList(allItemIdList);
        List<RentChargingStandardListVo> standardList = rentChargingStandardProvider.list(rentChargingStandardListDto);
        List<Long> standardIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(standardIdList)) {
            standardIdList = standardList.stream().map(RentChargingStandardListVo::getId).collect(Collectors.toList());
        }

        // 查询参数
        RentChargingStandardParamsListDto paramsListDto = new RentChargingStandardParamsListDto();
        paramsListDto.setChargingStandardIdList(standardIdList);
        List<RentChargingStandardParamsListVo> standardParamsListVoList = rentChargingStandardParamsProvider.list(paramsListDto);
        Map<Long, List<RentChargingStandardParamsListVo>> standardParamsMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(standardParamsListVoList)) {
            standardParamsMap = standardParamsListVoList.stream().collect(Collectors.groupingBy(RentChargingStandardParamsListVo::getChargingStandardId));
        }

        if (CollectionUtil.isNotEmpty(standardList)) {
            for (RentChargingStandardListVo vo : standardList) {
                // 标准
                RentChargingStandardListAllItem standardListAllItem = BeanUtils.copyProperties(vo, RentChargingStandardListAllItem.class);

                // 项目
                standardListAllItem.setChargingItemId(vo.getChargingItemId());
                RentChargingItemListVo rentChargingItemListVo = itemIdVoMap.get(vo.getChargingItemId());
                if (rentChargingItemListVo != null) {
                    standardListAllItem.setChargingItemName(rentChargingItemListVo.getFeeName());
                }
                // 参数
                List<RentChargingStandardParamsListVo> paramsList = standardParamsMap.get(vo.getId());
                if (CollectionUtil.isNotEmpty(paramsList)) {
                    standardListAllItem.setStandardParams(BeanUtils.copyProperties(paramsList, RentChargingStandardParamsListItem.class));
                }
                result.add(standardListAllItem);
            }
        }

        return result;
    }
}

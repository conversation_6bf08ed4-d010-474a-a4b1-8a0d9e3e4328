package com.seewin.som.report.provider.impl;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.report.entity.ReportInvertMarket;
import com.seewin.som.report.req.ReportInvertMarketAddDto;
import com.seewin.som.report.req.ReportInvertMarketEditDto;
import com.seewin.som.report.req.ReportInvertMarketListDto;
import com.seewin.som.report.resp.ReportInvertMarketAddVo;
import com.seewin.som.report.resp.ReportInvertMarketGetVo;
import com.seewin.som.report.resp.ReportInvertMarketListVo;
import com.seewin.util.bean.BeanUtils;


import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.seewin.som.report.service.ReportInvertMarketService;
import com.seewin.som.report.provider.ReportInvertMarketProvider;
/**
 * <p>
 * 招商市场报告表 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@DubboService
public class ReportInvertMarketProviderImpl implements ReportInvertMarketProvider{

	@Autowired
	private ReportInvertMarketService reportInvertMarketService;
	
	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public PageResult<ReportInvertMarketListVo> page(PageQuery<ReportInvertMarketListDto> pageQuery) throws ServiceException
    {
    	ReportInvertMarketListDto dto = pageQuery.getQueryDto();

        //设置分页
        Page<ReportInvertMarket> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        //构造查询条件
        QueryWrapper<ReportInvertMarket> queryWrapper = queryBuild(dto);

        //查询数据
        page = reportInvertMarketService.page(page, queryWrapper);
        List<ReportInvertMarket> records = page.getRecords();

        //响应结果封装
        PageResult<ReportInvertMarketListVo> result = new PageResult<>();
        List<ReportInvertMarketListVo> items = BeanUtils.copyProperties(records, ReportInvertMarketListVo.class);
        
        result.setItems(items);
        result.setPages((int)page.getPages());
        result.setTotal((int)page.getTotal());
        result.setPageNum(pageQuery.getPageNum());
        result.setPageSize(pageQuery.getPageSize());

        //返回查询结果
        return result;
    }

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public List<ReportInvertMarketListVo> list(ReportInvertMarketListDto dto) throws ServiceException
    {
    	  //构造查询条件
        QueryWrapper<ReportInvertMarket> queryWrapper = queryBuild(dto);

        //查询数据
        List<ReportInvertMarket> records = reportInvertMarketService.list(queryWrapper);

        //响应结果封装
        List<ReportInvertMarketListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, ReportInvertMarketListVo.class);

        //返回查询结果
        return result;
    }

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    @Override
    public int count(ReportInvertMarketListDto dto) throws ServiceException
    {
     	//构造查询条件
        QueryWrapper<ReportInvertMarket> queryWrapper = queryBuild(dto, false);

        //查询数据
        int result = (int) reportInvertMarketService.count(queryWrapper);

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportInvertMarketGetVo get(Long id) throws ServiceException
    {
    	//查询数据
        ReportInvertMarket item = reportInvertMarketService.getById(id);

        //响应结果封装
        ReportInvertMarketGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportInvertMarketGetVo.class);
        }

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
     @Override
    public ReportInvertMarketGetVo get(ReportInvertMarketListDto dto) throws ServiceException
    {
        //构造查询条件
        QueryWrapper<ReportInvertMarket> queryWrapper = queryBuild(dto);
        queryWrapper.last(PageQuery.LIMIT_ONE);

        //查询数据
        ReportInvertMarket item = reportInvertMarketService.getOne(queryWrapper);

        //响应结果封装
        ReportInvertMarketGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportInvertMarketGetVo.class);
        }

        //返回查询结果
        return result; 	
    }


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
     @Override
    public ReportInvertMarketAddVo add(ReportInvertMarketAddDto dto) throws ServiceException
    {
    	 ReportInvertMarket entity = BeanUtils.copyProperties(dto, ReportInvertMarket.class);

        LocalDateTime nowTime = LocalDateTime.now();                
     	entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setCreateBy(dto.getCreateBy());
        entity.setCreateUser(dto.getCreateUser());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setUpdateBy(dto.getCreateBy());
        entity.setUpdateUser(dto.getCreateUser());
        entity.setUpdateUserName(dto.getCreateUserName());
        entity.setUpdateTime(nowTime);
	    entity.setDelStatus(0);
        reportInvertMarketService.save(entity);

        //响应结果封装
        ReportInvertMarketAddVo result = new ReportInvertMarketAddVo();
        result.setId(entity.getId());

        return result;
    }


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void edit(ReportInvertMarketEditDto dto) throws ServiceException
    {
    	ReportInvertMarket entity = BeanUtils.copyProperties(dto, ReportInvertMarket.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateUser(dto.getUpdateUser());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateTime(nowTime);

        reportInvertMarketService.updateById(entity);
    }

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(Long id) throws ServiceException
    {
    	 reportInvertMarketService.removeById(id);
    }

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(ReportInvertMarketListDto dto) throws ServiceException
    {
    	//构造查询条件
        QueryWrapper<ReportInvertMarket> queryWrapper = queryBuild(dto, false);

        //删除操作
        reportInvertMarketService.remove(queryWrapper);
    }
    
     /**
     * <p>构造查询条件<br>
     * <p>默认构造排序条件<br>
     *
     * @param dto 查询条件Dto
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportInvertMarket> queryBuild(ReportInvertMarketListDto dto) throws ServiceException {
        return queryBuild(dto, true);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportInvertMarket> queryBuild(ReportInvertMarketListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<ReportInvertMarket> queryWrapper = new QueryWrapper<>();

        ReportInvertMarket entity = BeanUtils.copyProperties(dto, ReportInvertMarket.class);
	    entity.setDelStatus(0);

        /** 添加条件样例参考，不用请删除
        if (StringUtils.isNotBlank(dto.getName())) {
            entity.setName(null);
            queryWrapper.like("name", dto.getName());
        }

        queryWrapper.in(dto.getStatusIn() != null, "status", dto.getStatusIn());

        if (orderBy) {
            if (dto.getTypeOrder() != null) {
                queryWrapper.orderBy(true, dto.getTypeOrder().isAsc(), "type");
            }

            queryWrapper.orderByAsc("order_by");
        }
        */
        if(dto.getDataType()!=null&& dto.getDataType()==1){
            entity.setDataType(null);
            entity.setCommercialTypeCode(null);
            entity.setCommercialTypeName(null);
            entity.setCategoryId(null);
            entity.setCategoryName(null);
            queryWrapper.and(wrapper -> wrapper
                    .or(wrapper1 -> wrapper1
                            .eq("data_type", 1)
                            .eq("commercial_type_code", dto.getCommercialTypeCode())
//                            .eq("commercial_type_name", dto.getCommercialTypeName())
                            .eq("category_id", dto.getCategoryId())
//                            .eq("category_name", dto.getCategoryName())
                    )
                    .or(wrapper2 -> wrapper2
                            .eq("data_type", 0)
                            .eq("commercial_type_code", dto.getCommercialTypeCode())
//                            .eq("commercial_type_name", dto.getCommercialTypeName())
                    )
            );
        }
        //按创建时间倒序排序，根据需要添加
        //queryWrapper.orderByDesc("create_time");

        queryWrapper.setEntity(entity);

        return queryWrapper;
    }
}

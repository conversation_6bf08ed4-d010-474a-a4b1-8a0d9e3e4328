#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32784 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=41028, tid=50716
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Trae\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Trae\User\workspaceStorage\b29e7cfb7295f7524603f8110d6c5aab\redhat.java\ss_ws --pipe=\\.\pipe\lsp-36e66cca9d3ad0f9da7a93fed5720447-sock

Host: Intel(R) Core(TM) i5-9400 CPU @ 2.90GHz, 6 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Mon Jul  7 09:43:57 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 2.719050 seconds (0d 0h 0m 2s)

---------------  T H R E A D  ---------------

Current thread (0x000001b3c5101c00):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=50716, stack(0x00000070cc100000,0x00000070cc200000) (1024K)]


Current CompileTask:
C2:2719  676       4       lombok.patcher.scripts.MethodLevelPatchScript::patch (21 bytes)

Stack: [0x00000070cc100000,0x00000070cc200000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0xc51a5]
V  [jvm.dll+0x6a96fc]
V  [jvm.dll+0x24f3a9]
V  [jvm.dll+0x6f38e7]
V  [jvm.dll+0x246def]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001b3e12b91b0, length=12, elements={
0x000001b3c51bc380, 0x000001b3c50f7710, 0x000001b3c50f8e70, 0x000001b3c50fcdd0,
0x000001b3c50ff930, 0x000001b3c5100380, 0x000001b3c5100dd0, 0x000001b3c5101c00,
0x000001b3c516cb40, 0x000001b3c522a8b0, 0x000001b3e1234540, 0x000001b3e12ed6b0
}

Java Threads: ( => current thread )
  0x000001b3c51bc380 JavaThread "main"                              [_thread_in_Java, id=50740, stack(0x00000070cb700000,0x00000070cb800000) (1024K)]
  0x000001b3c50f7710 JavaThread "Reference Handler"          daemon [_thread_blocked, id=24724, stack(0x00000070cbb00000,0x00000070cbc00000) (1024K)]
  0x000001b3c50f8e70 JavaThread "Finalizer"                  daemon [_thread_blocked, id=49736, stack(0x00000070cbc00000,0x00000070cbd00000) (1024K)]
  0x000001b3c50fcdd0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=50156, stack(0x00000070cbd00000,0x00000070cbe00000) (1024K)]
  0x000001b3c50ff930 JavaThread "Attach Listener"            daemon [_thread_blocked, id=24524, stack(0x00000070cbe00000,0x00000070cbf00000) (1024K)]
  0x000001b3c5100380 JavaThread "Service Thread"             daemon [_thread_blocked, id=24676, stack(0x00000070cbf00000,0x00000070cc000000) (1024K)]
  0x000001b3c5100dd0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=49236, stack(0x00000070cc000000,0x00000070cc100000) (1024K)]
=>0x000001b3c5101c00 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=50716, stack(0x00000070cc100000,0x00000070cc200000) (1024K)]
  0x000001b3c516cb40 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=43276, stack(0x00000070cc200000,0x00000070cc300000) (1024K)]
  0x000001b3c522a8b0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=3640, stack(0x00000070cc300000,0x00000070cc400000) (1024K)]
  0x000001b3e1234540 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=49568, stack(0x00000070cc400000,0x00000070cc500000) (1024K)]
  0x000001b3e12ed6b0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=7976, stack(0x00000070cc500000,0x00000070cc600000) (1024K)]
Total: 12

Other Threads:
  0x000001b3c50d19b0 VMThread "VM Thread"                           [id=37624, stack(0x00000070cba00000,0x00000070cbb00000) (1024K)]
  0x000001b3c504ca00 WatcherThread "VM Periodic Task Thread"        [id=21348, stack(0x00000070cb900000,0x00000070cba00000) (1024K)]
  0x000001b3c51dc740 WorkerThread "GC Thread#0"                     [id=6588, stack(0x00000070cb800000,0x00000070cb900000) (1024K)]
Total: 3

Threads with active compile tasks:
C2 CompilerThread0  3673  676       4       lombok.patcher.scripts.MethodLevelPatchScript::patch (21 bytes)
C2 CompilerThread1  3673  650       4       lombok.patcher.PatchScript::classMatches (41 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001b380000000-0x000001b380ba0000-0x000001b380ba0000), size 12189696, SharedBaseAddress: 0x000001b380000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001b381000000-0x000001b3c1000000, reserved size: 1073741824
Narrow klass base: 0x000001b380000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 6 total, 6 available
 Memory: 16220M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 6

Heap:
 PSYoungGen      total 29696K, used 15175K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 59% used [0x00000000d5580000,0x00000000d6451c40,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 2865K, committed 3072K, reserved 1114112K
  class space    used 291K, committed 384K, reserved 1048576K

Card table byte_map: [0x000001b3c4b60000,0x000001b3c4f70000] _byte_map_base: 0x000001b3c4760000

Marking Bits: (ParMarkBitMap*) 0x00007ff9696b31f0
 Begin Bits: [0x000001b3d7660000, 0x000001b3d9660000)
 End Bits:   [0x000001b3d9660000, 0x000001b3db660000)

Polling page: 0x000001b3c3030000

Metaspace:

Usage:
  Non-class:      2.51 MB used.
      Class:    291.28 KB used.
       Both:      2.80 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       2.62 MB (  4%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     384.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       3.00 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.65 MB
       Class:  15.67 MB
        Both:  27.32 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 82.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 48.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 104.
num_chunk_merges: 0.
num_chunk_splits: 72.
num_chunks_enlarged: 47.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=251Kb max_used=251Kb free=119748Kb
 bounds [0x000001b3cfe80000, 0x000001b3d00f0000, 0x000001b3d73b0000]
CodeHeap 'profiled nmethods': size=120000Kb used=1082Kb max_used=1082Kb free=118917Kb
 bounds [0x000001b3c83b0000, 0x000001b3c8620000, 0x000001b3cf8e0000]
CodeHeap 'non-nmethods': size=5760Kb used=1185Kb max_used=1195Kb free=4574Kb
 bounds [0x000001b3cf8e0000, 0x000001b3cfb50000, 0x000001b3cfe80000]
 total_blobs=1155 nmethods=710 adapters=352
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 2.511 Thread 0x000001b3c516cb40  708       3       jdk.internal.misc.Unsafe::getIntUnaligned (12 bytes)
Event: 2.512 Thread 0x000001b3c516cb40 nmethod 708 0x000001b3c84b8c90 code [0x000001b3c84b8e40, 0x000001b3c84b9098]
Event: 2.512 Thread 0x000001b3c516cb40  709       1       java.lang.invoke.MethodHandleImpl::isCompileConstant (2 bytes)
Event: 2.512 Thread 0x000001b3c516cb40 nmethod 709 0x000001b3cfebe910 code [0x000001b3cfebeaa0, 0x000001b3cfebeb48]
Event: 2.512 Thread 0x000001b3c516cb40  710       3       java.lang.invoke.VarHandleGuards::guard_LI_I (78 bytes)
Event: 2.513 Thread 0x000001b3c516cb40 nmethod 710 0x000001b3c84b9190 code [0x000001b3c84b94c0, 0x000001b3c84bac90]
Event: 2.513 Thread 0x000001b3c516cb40  714       3       java.lang.invoke.VarHandle::checkAccessModeThenIsDirect (29 bytes)
Event: 2.513 Thread 0x000001b3c516cb40 nmethod 714 0x000001b3c84bb390 code [0x000001b3c84bb5a0, 0x000001b3c84bbe08]
Event: 2.513 Thread 0x000001b3c516cb40  715       3       java.lang.invoke.VarHandle::accessModeType (41 bytes)
Event: 2.712 Thread 0x000001b3c516cb40 nmethod 715 0x000001b3c84bc110 code [0x000001b3c84bc300, 0x000001b3c84bca30]
Event: 2.712 Thread 0x000001b3c516cb40  712       3       java.lang.invoke.VarForm::getMemberName (31 bytes)
Event: 2.713 Thread 0x000001b3c516cb40 nmethod 712 0x000001b3c84bcc90 code [0x000001b3c84bce60, 0x000001b3c84bd0f0]
Event: 2.713 Thread 0x000001b3c516cb40  718   !   3       sun.instrument.TransformerManager::transform (100 bytes)
Event: 2.713 Thread 0x000001b3c516cb40 nmethod 718 0x000001b3c84bd210 code [0x000001b3c84bd3e0, 0x000001b3c84bd860]
Event: 2.713 Thread 0x000001b3c516cb40  713       3       java.lang.invoke.VarHandleByteArrayAsInts$ArrayHandle::index (12 bytes)
Event: 2.713 Thread 0x000001b3c516cb40 nmethod 713 0x000001b3c84bdb10 code [0x000001b3c84bdcc0, 0x000001b3c84bde08]
Event: 2.713 Thread 0x000001b3c516cb40  711       3       java.lang.invoke.VarHandleByteArrayAsInts$ArrayHandle::get (36 bytes)
Event: 2.714 Thread 0x000001b3c516cb40 nmethod 711 0x000001b3c84bdf10 code [0x000001b3c84be0e0, 0x000001b3c84be4b8]
Event: 2.714 Thread 0x000001b3c516cb40  717       3       java.lang.StringBuilder::<init> (6 bytes)
Event: 2.714 Thread 0x000001b3c516cb40 nmethod 717 0x000001b3c84be690 code [0x000001b3c84be840, 0x000001b3c84be978]

GC Heap History (0 events):
No events

Dll operation events (8 events):
Event: 0.072 Loaded shared library c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.106 Loaded shared library c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 1.186 Loaded shared library C:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 1.230 Loaded shared library C:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 1.235 Loaded shared library C:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 1.238 Loaded shared library C:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 1.255 Loaded shared library C:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 1.326 Loaded shared library c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

Deoptimization events (20 events):
Event: 1.463 Thread 0x000001b3c51bc380 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001b3cfeb0304 relative=0x0000000000000164
Event: 1.463 Thread 0x000001b3c51bc380 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001b3cfeb0304 method=jdk.internal.util.ArraysSupport.vectorizedHashCode(Ljava/lang/Object;IIII)I @ 2 c2
Event: 1.463 Thread 0x000001b3c51bc380 DEOPT PACKING pc=0x000001b3cfeb0304 sp=0x00000070cb7fc9e0
Event: 1.463 Thread 0x000001b3c51bc380 DEOPT UNPACKING pc=0x000001b3cf933aa2 sp=0x00000070cb7fc990 mode 2
Event: 1.474 Thread 0x000001b3c51bc380 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001b3cfea3884 relative=0x00000000000000e4
Event: 1.474 Thread 0x000001b3c51bc380 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001b3cfea3884 method=java.net.URI.match(CJJ)Z @ 45 c2
Event: 1.474 Thread 0x000001b3c51bc380 DEOPT PACKING pc=0x000001b3cfea3884 sp=0x00000070cb7fcc80
Event: 1.474 Thread 0x000001b3c51bc380 DEOPT UNPACKING pc=0x000001b3cf933aa2 sp=0x00000070cb7fcbe8 mode 2
Event: 1.504 Thread 0x000001b3c51bc380 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001b3cfea1fc0 relative=0x00000000000001a0
Event: 1.504 Thread 0x000001b3c51bc380 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001b3cfea1fc0 method=java.lang.StringUTF16.compress([CI[BII)I @ 20 c2
Event: 1.504 Thread 0x000001b3c51bc380 DEOPT PACKING pc=0x000001b3cfea1fc0 sp=0x00000070cb7fb6f0
Event: 1.504 Thread 0x000001b3c51bc380 DEOPT UNPACKING pc=0x000001b3cf933aa2 sp=0x00000070cb7fb660 mode 2
Event: 2.511 Thread 0x000001b3c51bc380 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001b3cfeb10a0 relative=0x0000000000000940
Event: 2.511 Thread 0x000001b3c51bc380 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001b3cfeb10a0 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 91 c2
Event: 2.511 Thread 0x000001b3c51bc380 DEOPT PACKING pc=0x000001b3cfeb10a0 sp=0x00000070cb7fda10
Event: 2.511 Thread 0x000001b3c51bc380 DEOPT UNPACKING pc=0x000001b3cf933aa2 sp=0x00000070cb7fd9d0 mode 2
Event: 2.511 Thread 0x000001b3c51bc380 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001b3cfeb10a0 relative=0x0000000000000940
Event: 2.511 Thread 0x000001b3c51bc380 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001b3cfeb10a0 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 91 c2
Event: 2.511 Thread 0x000001b3c51bc380 DEOPT PACKING pc=0x000001b3cfeb10a0 sp=0x00000070cb7fdb30
Event: 2.511 Thread 0x000001b3c51bc380 DEOPT UNPACKING pc=0x000001b3cf933aa2 sp=0x00000070cb7fdac0 mode 2

Classes loaded (20 events):
Event: 1.507 Loading class java/security/interfaces/RSAPrivateCrtKey done
Event: 1.507 Loading class sun/security/rsa/RSAPrivateCrtKeyImpl done
Event: 1.507 Loading class sun/security/rsa/RSACore
Event: 1.507 Loading class sun/security/rsa/RSACore done
Event: 1.507 Loading class sun/security/rsa/RSAPadding
Event: 1.507 Loading class sun/security/rsa/RSAPadding done
Event: 1.507 Loading class java/math/MutableBigInteger
Event: 1.507 Loading class java/math/MutableBigInteger done
Event: 2.510 Loading class sun/security/provider/certpath/X509CertPath
Event: 2.511 Loading class java/security/cert/CertPath
Event: 2.511 Loading class java/security/cert/CertPath done
Event: 2.511 Loading class sun/security/provider/certpath/X509CertPath done
Event: 2.511 Loading class sun/security/timestamp/TimestampToken
Event: 2.511 Loading class sun/security/timestamp/TimestampToken done
Event: 2.511 Loading class java/security/Timestamp
Event: 2.511 Loading class java/security/Timestamp done
Event: 2.516 Loading class java/security/CodeSigner
Event: 2.713 Loading class java/security/CodeSigner done
Event: 2.713 Loading class sun/security/util/JarConstraintsParameters
Event: 2.715 Loading class sun/security/util/ConstraintsParameters

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (12 events):
Event: 1.163 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57e0b00}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d57e0b00) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.249 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5949e18}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5949e18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.283 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a02848}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5a02848) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.285 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a15230}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5a15230) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.286 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a20670}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a20670) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.286 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a24250}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5a24250) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.289 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a3cf40}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000d5a3cf40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.289 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a41890}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a41890) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.290 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a45420}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a45420) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.290 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a48890}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a48890) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.345 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5c08890}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5c08890) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.458 Thread 0x000001b3c51bc380 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d608f8e8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000d608f8e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 1.112 Executing VM operation: Cleanup
Event: 1.157 Executing VM operation: Cleanup done
Event: 1.165 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.165 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.264 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.265 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.276 Executing VM operation: Cleanup
Event: 2.509 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (12 events):
Event: 0.083 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c51bc380
Event: 0.114 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c50f7710
Event: 0.114 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c50f8e70
Event: 0.115 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c50fcdd0
Event: 0.115 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c50ff930
Event: 0.115 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c5100380
Event: 0.115 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c5100dd0
Event: 0.115 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c5101c00
Event: 0.120 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c516cb40
Event: 1.157 Thread 0x000001b3c51bc380 Thread added: 0x000001b3c522a8b0
Event: 1.319 Thread 0x000001b3c5101c00 Thread added: 0x000001b3e1234540
Event: 1.407 Thread 0x000001b3c51bc380 Thread added: 0x000001b3e12ed6b0


Dynamic libraries:
0x00007ff69c8b0000 - 0x00007ff69c8be000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffa153e0000 - 0x00007ffa15645000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa143f0000 - 0x00007ffa144b9000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa12910000 - 0x00007ffa12cf8000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa13030000 - 0x00007ffa1317b000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa09e20000 - 0x00007ffa09e38000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffa13d70000 - 0x00007ffa13f3a000 	C:\Windows\System32\USER32.dll
0x00007ffa12e80000 - 0x00007ffa12ea7000 	C:\Windows\System32\win32u.dll
0x00007ffa144c0000 - 0x00007ffa144eb000 	C:\Windows\System32\GDI32.dll
0x00007ffa12530000 - 0x00007ffa12667000 	C:\Windows\System32\gdi32full.dll
0x00007ffa12860000 - 0x00007ffa12903000 	C:\Windows\System32\msvcp_win.dll
0x00007ff9e2090000 - 0x00007ff9e232a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffa146b0000 - 0x00007ffa14759000 	C:\Windows\System32\msvcrt.dll
0x00007ffa093e0000 - 0x00007ffa093fe000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffa148f0000 - 0x00007ffa14920000 	C:\Windows\System32\IMM32.DLL
0x00007ffa09e60000 - 0x00007ffa09e6c000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff9f0570000 - 0x00007ff9f05fd000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ff968a00000 - 0x00007ff969790000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffa14760000 - 0x00007ffa14813000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa13330000 - 0x00007ffa133d6000 	C:\Windows\System32\sechost.dll
0x00007ffa14ab0000 - 0x00007ffa14bc5000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa13560000 - 0x00007ffa135d4000 	C:\Windows\System32\WS2_32.dll
0x00007ffa122d0000 - 0x00007ffa1232e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa04c10000 - 0x00007ffa04c45000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa0e190000 - 0x00007ffa0e19b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa122b0000 - 0x00007ffa122c4000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa112f0000 - 0x00007ffa1130b000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa093d0000 - 0x00007ffa093da000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffa06230000 - 0x00007ffa06471000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa13980000 - 0x00007ffa13d05000 	C:\Windows\System32\combase.dll
0x00007ffa14930000 - 0x00007ffa14a11000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa03bf0000 - 0x00007ffa03c29000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa12670000 - 0x00007ffa12709000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa093c0000 - 0x00007ffa093cf000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffa09170000 - 0x00007ffa0918f000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffa14c40000 - 0x00007ffa15382000 	C:\Windows\System32\SHELL32.dll
0x00007ffa12d00000 - 0x00007ffa12e74000 	C:\Windows\System32\wintypes.dll
0x00007ffa101d0000 - 0x00007ffa10a28000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa136a0000 - 0x00007ffa13791000 	C:\Windows\System32\SHCORE.dll
0x00007ffa14bd0000 - 0x00007ffa14c3a000 	C:\Windows\System32\shlwapi.dll
0x00007ffa12440000 - 0x00007ffa1246f000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff9f0bc0000 - 0x00007ff9f0bd8000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff9f0bb0000 - 0x00007ff9f0bc0000 	C:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffa0db10000 - 0x00007ffa0dc2e000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffa11860000 - 0x00007ffa118ca000 	C:\Windows\system32\mswsock.dll
0x00007ff9f0b90000 - 0x00007ff9f0ba6000 	C:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff9f0b50000 - 0x00007ff9f0b60000 	c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Trae\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Trae\User\workspaceStorage\b29e7cfb7295f7524603f8110d6c5aab\redhat.java\ss_ws --pipe=\\.\pipe\lsp-36e66cca9d3ad0f9da7a93fed5720447-sock
java_class_path (initial): c:\Users\<USER>\.trae\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\jdk-17.0.10
CLASSPATH=.;D:\jdk-17.0.10\lib;D:\jdk-17.0.10\lib\tools.jar
PATH=C:\Users\<USER>\.trae\tools\gradle\latest\bin;D:\jdk-17.0.10\bin;C:\Users\<USER>\.trae\tools\maven\latest\bin;D:\ShadowBot;d:\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\jdk-17.0.10\bin;E:\JavaSpace\peizhi\apache-maven-3.5.4\apache-maven-3.5.4\bin;D:\Git\cmd;C:\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;d:\cursor\resources\app\bin;E:\node\;C:\Program Files\dotnet\;D:\ShadowBot;C:\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Microsoft VS Code\bin;D:\IntelliJ IDEA 2025.1.3\bin;;e:\Trae\resources\app\bin\lib
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 25 days 14:12 hours

CPU: total 6 (initial active 6) (6 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xde, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 6 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 16220M (2010M free)
TotalPageFile size 32693M (AvailPageFile size 53M)
current process WorkingSet (physical memory assigned to process): 64M, peak: 64M
current process commit charge ("private bytes"): 225M, peak: 226M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.

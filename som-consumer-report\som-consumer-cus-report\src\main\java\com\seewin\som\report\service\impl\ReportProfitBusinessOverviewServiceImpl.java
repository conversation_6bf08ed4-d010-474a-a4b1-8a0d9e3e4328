package com.seewin.som.report.service.impl;

import com.seewin.model.base.CusUser;
import com.seewin.som.report.provider.ReportProfitCostAnalysisProvider;
import com.seewin.som.report.provider.ReportProfitRevenueAnalysisProvider;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import com.seewin.som.report.service.ReportProfitBusinessOverviewService;
import com.seewin.som.report.vo.req.*;
import com.seewin.som.report.vo.resp.*;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import com.seewin.som.report.provider.ReportProfitBusinessOverviewProvider;

import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 盈亏报告-经营概况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Service
public class ReportProfitBusinessOverviewServiceImpl implements ReportProfitBusinessOverviewService {

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-report-mgt")
    private ReportProfitBusinessOverviewProvider reportProfitBusinessOverviewProvider;
    @DubboReference(providedBy = "som-report-mgt")
    private ReportProfitCostAnalysisProvider reportProfitCostAnalysisProvider;
    @DubboReference(providedBy = "som-report-mgt")
    private ReportProfitRevenueAnalysisProvider reportProfitRevenueAnalysisProvider;

    @Override
    public ReportGetProfitCostAnalysisResp getReportProfit(ReportProfitReq req) {
        ReportGetProfitCostAnalysisResp result = new ReportGetProfitCostAnalysisResp();
        CusUser curUser = ApiUtils.getUser(CusUser.class);
        //经营概况
        List<ReportGetProfitCostAnalysisItemResp> businessOverviewGetList = new ArrayList<>();
        ReportProfitBusinessOverviewListDto businessOverviewListDto = new ReportProfitBusinessOverviewListDto();
        businessOverviewListDto.setStoreId(req.getStoreId());
//        businessOverviewListDto.setYear(req.getYear());
//        businessOverviewListDto.setMonth(req.getMonth());
        businessOverviewListDto.setTenantId(req.getTenantId());
        businessOverviewListDto.setStartDate(req.getStartDate());
        businessOverviewListDto.setEndDate(req.getEndDate());
        ReportProfitBusinessOverviewGetVo businessOverviewGetVo = reportProfitBusinessOverviewProvider.get(businessOverviewListDto);
        if (!ObjectUtils.isEmpty(businessOverviewGetVo)) {
            ReportGetProfitCostAnalysisItemResp currentResp = new ReportGetProfitCostAnalysisItemResp();
            currentResp.setSort(0);
            currentResp.setName("总销售额（元）");
            currentResp.setCurrentData(businessOverviewGetVo.getCurrentPeriod());
            currentResp.setRingCompareRatio(businessOverviewGetVo.getCurrentPeriodRingRatio());
            currentResp.setSameCompareRatio(businessOverviewGetVo.getCurrentPeriodSameRatio());
            businessOverviewGetList.add(currentResp);
            ReportGetProfitCostAnalysisItemResp totalResp = new ReportGetProfitCostAnalysisItemResp();
            totalResp.setSort(1);
            totalResp.setName("总成本（元）");
            totalResp.setCurrentData(businessOverviewGetVo.getTotalCost());
            totalResp.setRingCompareRatio(businessOverviewGetVo.getTotalCostRingRatio());
            totalResp.setSameCompareRatio(businessOverviewGetVo.getTotalCostSameRatio());
            businessOverviewGetList.add(totalResp);
            ReportGetProfitCostAnalysisItemResp netProfitResp = new ReportGetProfitCostAnalysisItemResp();
            netProfitResp.setSort(2);
            netProfitResp.setName("净利润（元）");
            netProfitResp.setCurrentData(businessOverviewGetVo.getNetProfit());
            netProfitResp.setRingCompareRatio(businessOverviewGetVo.getNetProfitRingRatio());
            netProfitResp.setSameCompareRatio(businessOverviewGetVo.getNetProfitSameRatio());
            businessOverviewGetList.add(netProfitResp);
            result.setAi(businessOverviewGetVo.getAlInterpret());
        }
        result.setBusinessOverviewGetList(businessOverviewGetList);
        // todo 收入分析
        List<ReportGetProfitCostAnalysisItemResp> analysisGetList = new ArrayList<>();
        ReportProfitRevenueAnalysisListDto revenueAnalysisListDto = new ReportProfitRevenueAnalysisListDto();
        revenueAnalysisListDto.setStoreId(req.getStoreId());
//        revenueAnalysisListDto.setYear(req.getYear());
//        revenueAnalysisListDto.setMonth(req.getMonth());
        revenueAnalysisListDto.setTenantId(req.getTenantId());
        revenueAnalysisListDto.setStartDate(req.getStartDate());
        revenueAnalysisListDto.setEndDate(req.getEndDate());
        ReportProfitRevenueAnalysisGetVo revenueAnalysisGetVo = reportProfitRevenueAnalysisProvider.get(revenueAnalysisListDto);
        if (!ObjectUtils.isEmpty(revenueAnalysisGetVo)) {
            ReportGetProfitCostAnalysisItemResp currentResp = new ReportGetProfitCostAnalysisItemResp();
            currentResp.setSort(0);
            currentResp.setName("总销售额（元）");
            currentResp.setCurrentData(revenueAnalysisGetVo.getCurrentPeriod());
            currentResp.setRingCompareRatio(revenueAnalysisGetVo.getCurrentPeriodRingRatio());
            currentResp.setSameCompareRatio(revenueAnalysisGetVo.getCurrentPeriodSameRatio());
            analysisGetList.add(currentResp);
            ReportGetProfitCostAnalysisItemResp totalNumber = new ReportGetProfitCostAnalysisItemResp();
            totalNumber.setSort(1);
            totalNumber.setName("总销售笔数（单）");
            totalNumber.setCurrentData(revenueAnalysisGetVo.getTotalNumber());
            totalNumber.setRingCompareRatio(revenueAnalysisGetVo.getTotalNumberRingRatio());
            totalNumber.setSameCompareRatio(revenueAnalysisGetVo.getTotalNumberSameRatio());
            analysisGetList.add(totalNumber);
            ReportGetProfitCostAnalysisItemResp totalPrice = new ReportGetProfitCostAnalysisItemResp();
            totalPrice.setSort(2);
            totalPrice.setName("总客单价（元/单）");
            totalPrice.setCurrentData(revenueAnalysisGetVo.getTotalPrice());
            totalPrice.setRingCompareRatio(revenueAnalysisGetVo.getTotalPriceRingRatio());
            totalPrice.setSameCompareRatio(revenueAnalysisGetVo.getTotalPriceSameRatio());
            analysisGetList.add(totalPrice);
            ReportGetProfitCostAnalysisItemResp onlineVolume = new ReportGetProfitCostAnalysisItemResp();
            onlineVolume.setSort(3);
            onlineVolume.setName("线上销售额（元）");
            onlineVolume.setCurrentData(revenueAnalysisGetVo.getOnlineVolume());
            onlineVolume.setRingCompareRatio(revenueAnalysisGetVo.getOnlineVolumeRingRatio());
            onlineVolume.setSameCompareRatio(revenueAnalysisGetVo.getOnlineVolumeSameRatio());
            analysisGetList.add(onlineVolume);
            ReportGetProfitCostAnalysisItemResp onlineNumber = new ReportGetProfitCostAnalysisItemResp();
            onlineNumber.setSort(4);
            onlineNumber.setName("线上销售笔数（单）");
            onlineNumber.setCurrentData(revenueAnalysisGetVo.getOnlineNumber());
            onlineNumber.setRingCompareRatio(revenueAnalysisGetVo.getOnlineNumberRingRatio());
            onlineNumber.setSameCompareRatio(revenueAnalysisGetVo.getOnlineNumberSameRatio());
            analysisGetList.add(onlineNumber);
            ReportGetProfitCostAnalysisItemResp onlinePrice = new ReportGetProfitCostAnalysisItemResp();
            onlinePrice.setSort(5);
            onlinePrice.setName("线上客单价（元/单）");
            onlinePrice.setCurrentData(revenueAnalysisGetVo.getOnlinePrice());
            onlinePrice.setRingCompareRatio(revenueAnalysisGetVo.getOnlinePriceRingRatio());
            onlinePrice.setSameCompareRatio(revenueAnalysisGetVo.getOnlinePriceSameRatio());
            analysisGetList.add(onlinePrice);
            ReportGetProfitCostAnalysisItemResp offlineVolume = new ReportGetProfitCostAnalysisItemResp();
            offlineVolume.setSort(6);
            offlineVolume.setName("线下销售额（元）");
            offlineVolume.setCurrentData(revenueAnalysisGetVo.getOfflineVolume());
            offlineVolume.setRingCompareRatio(revenueAnalysisGetVo.getOfflineVolumeRingRatio());
            offlineVolume.setSameCompareRatio(revenueAnalysisGetVo.getOfflineVolumeSameRatio());
            analysisGetList.add(offlineVolume);
            ReportGetProfitCostAnalysisItemResp offlinePrice = new ReportGetProfitCostAnalysisItemResp();
            offlinePrice.setSort(7);
            offlinePrice.setName("线下客单价（元/单）");
            offlinePrice.setCurrentData(revenueAnalysisGetVo.getOfflinePrice());
            offlinePrice.setRingCompareRatio(revenueAnalysisGetVo.getOfflinePriceRingRatio());
            offlinePrice.setSameCompareRatio(revenueAnalysisGetVo.getOfflinePriceSameRatio());
            analysisGetList.add(offlinePrice);
            ReportGetProfitCostAnalysisItemResp storeNumber = new ReportGetProfitCostAnalysisItemResp();
            storeNumber.setSort(8);
            storeNumber.setName("线下销售笔数（单）");
            storeNumber.setCurrentData(revenueAnalysisGetVo.getStoreNumber());
            storeNumber.setRingCompareRatio(revenueAnalysisGetVo.getStoreNumberRingRatio());
            storeNumber.setSameCompareRatio(revenueAnalysisGetVo.getStoreNumberSameRatio());
            analysisGetList.add(storeNumber);
        }
        result.setRevenueAnalysisGetList(analysisGetList);
        // todo 成本分析
        List<ReportGetProfitCostAnalysisItemResp> revenueAnalysisGetResp = new ArrayList<>();
        ReportProfitCostAnalysisListDto costAnalysisListDto = new ReportProfitCostAnalysisListDto();
        costAnalysisListDto.setStoreId(req.getStoreId());
//        costAnalysisListDto.setYear(req.getYear());
//        costAnalysisListDto.setMonth(req.getMonth());
        costAnalysisListDto.setTenantId(req.getTenantId());
        costAnalysisListDto.setStartDate(req.getStartDate());
        costAnalysisListDto.setEndDate(req.getEndDate());
        ReportProfitCostAnalysisGetVo analysisGetVo = reportProfitCostAnalysisProvider.get(costAnalysisListDto);
        if (!ObjectUtils.isEmpty(analysisGetVo)) {
            ReportGetProfitCostAnalysisItemResp monthTotal = new ReportGetProfitCostAnalysisItemResp();
            monthTotal.setSort(0);
            monthTotal.setName("月度总成本（元/月）");
            monthTotal.setCurrentData(analysisGetVo.getMonthTotal());
            monthTotal.setRingCompareRatio(analysisGetVo.getMonthTotalRingRatio());
            monthTotal.setSameCompareRatio(analysisGetVo.getMonthTotalSameRatio());
            revenueAnalysisGetResp.add(monthTotal);
            ReportGetProfitCostAnalysisItemResp laborCost = new ReportGetProfitCostAnalysisItemResp();
            laborCost.setSort(1);
            laborCost.setName("人工成本（元/月）");
            laborCost.setCurrentData(analysisGetVo.getLaborCost());
            laborCost.setRingCompareRatio(analysisGetVo.getLaborCostRingRatio());
            laborCost.setSameCompareRatio(analysisGetVo.getLaborCostSameRatio());
            revenueAnalysisGetResp.add(laborCost);
            ReportGetProfitCostAnalysisItemResp rentPipe = new ReportGetProfitCostAnalysisItemResp();
            rentPipe.setSort(2);
            rentPipe.setName("租管费（元/月）");
            rentPipe.setCurrentData(analysisGetVo.getRentPipe());
            rentPipe.setRingCompareRatio(analysisGetVo.getRentPipeRingRatio());
            rentPipe.setSameCompareRatio(analysisGetVo.getRentPipeSameRatio());
            revenueAnalysisGetResp.add(rentPipe);
            ReportGetProfitCostAnalysisItemResp hydropowerFee = new ReportGetProfitCostAnalysisItemResp();
            hydropowerFee.setSort(3);
            hydropowerFee.setName("水电费（元/月）");
            hydropowerFee.setCurrentData(analysisGetVo.getHydropowerFee());
            hydropowerFee.setRingCompareRatio(analysisGetVo.getHydropowerFeeRingRatio());
            hydropowerFee.setSameCompareRatio(analysisGetVo.getHydropowerFeeSameRatio());
            revenueAnalysisGetResp.add(hydropowerFee);
            ReportGetProfitCostAnalysisItemResp materialCost = new ReportGetProfitCostAnalysisItemResp();
            materialCost.setSort(4);
            materialCost.setName("原材料费用（元/月）");
            materialCost.setCurrentData(analysisGetVo.getMaterialCost());
            materialCost.setRingCompareRatio(analysisGetVo.getMaterialCostRingRatio());
            materialCost.setSameCompareRatio(analysisGetVo.getMaterialCostSameRatio());
            revenueAnalysisGetResp.add(materialCost);
            ReportGetProfitCostAnalysisItemResp marketingExpenses = new ReportGetProfitCostAnalysisItemResp();
            marketingExpenses.setSort(5);
            marketingExpenses.setName("营销费用（元/月）");
            marketingExpenses.setCurrentData(analysisGetVo.getMarketingExpenses());
            marketingExpenses.setRingCompareRatio(analysisGetVo.getMarketingExpensesRingRatio());
            marketingExpenses.setSameCompareRatio(analysisGetVo.getMarketingExpensesSameRatio());
            revenueAnalysisGetResp.add(marketingExpenses);
            ReportGetProfitCostAnalysisItemResp takeoutFee = new ReportGetProfitCostAnalysisItemResp();
            takeoutFee.setSort(6);
            takeoutFee.setName("外卖平台费用（元/月）");
            takeoutFee.setCurrentData(analysisGetVo.getTakeoutFee());
            takeoutFee.setRingCompareRatio(analysisGetVo.getTakeoutFeeRingRatio());
            takeoutFee.setSameCompareRatio(analysisGetVo.getTakeoutFeeSameRatio());
            revenueAnalysisGetResp.add(takeoutFee);
            ReportGetProfitCostAnalysisItemResp franchiseFee = new ReportGetProfitCostAnalysisItemResp();
            franchiseFee.setSort(7);
            franchiseFee.setName("加盟管理费（元/月）");
            franchiseFee.setCurrentData(analysisGetVo.getFranchiseFee());
            franchiseFee.setRingCompareRatio(analysisGetVo.getFranchiseFeeRingRatio());
            franchiseFee.setSameCompareRatio(analysisGetVo.getFranchiseFeeSameRatio());
            revenueAnalysisGetResp.add(franchiseFee);
            ReportGetProfitCostAnalysisItemResp tax = new ReportGetProfitCostAnalysisItemResp();
            tax.setSort(8);
            tax.setName("税费（元/月）");
            tax.setCurrentData(analysisGetVo.getTax());
            tax.setRingCompareRatio(analysisGetVo.getTaxRingRatio());
            tax.setSameCompareRatio(analysisGetVo.getTaxSameRatio());
            revenueAnalysisGetResp.add(tax);
            ReportGetProfitCostAnalysisItemResp decorationFee = new ReportGetProfitCostAnalysisItemResp();
            decorationFee.setSort(9);
            decorationFee.setName("装修摊销费用（元/月）");
            decorationFee.setCurrentData(analysisGetVo.getDecorationFee());
            decorationFee.setRingCompareRatio(analysisGetVo.getDecorationFeeRingRatio());
            decorationFee.setSameCompareRatio(analysisGetVo.getDecorationFeeSameRatio());
            revenueAnalysisGetResp.add(decorationFee);
            ReportGetProfitCostAnalysisItemResp fixedDepreciation = new ReportGetProfitCostAnalysisItemResp();
            fixedDepreciation.setSort(10);
            fixedDepreciation.setName("固定资产折旧（元/月）");
            fixedDepreciation.setCurrentData(analysisGetVo.getFixedDepreciation());
            fixedDepreciation.setRingCompareRatio(analysisGetVo.getFixedDepreciationRingRatio());
            fixedDepreciation.setSameCompareRatio(analysisGetVo.getFixedDepreciationSameRatio());
            revenueAnalysisGetResp.add(fixedDepreciation);
            ReportGetProfitCostAnalysisItemResp brandFee = new ReportGetProfitCostAnalysisItemResp();
            brandFee.setSort(12);
            brandFee.setName("品牌加盟费（元/月）");
            brandFee.setCurrentData(analysisGetVo.getBrandFee());
            brandFee.setRingCompareRatio(analysisGetVo.getBrandFeeRingRatio());
            brandFee.setSameCompareRatio(analysisGetVo.getBrandFeeSameRatio());
            revenueAnalysisGetResp.add(brandFee);
            ReportGetProfitCostAnalysisItemResp otherCosts = new ReportGetProfitCostAnalysisItemResp();
            otherCosts.setSort(13);
            otherCosts.setName("其他成本（元/月）");
            otherCosts.setCurrentData(analysisGetVo.getOtherCosts());
            otherCosts.setRingCompareRatio(analysisGetVo.getOtherCostsRingRatio());
            otherCosts.setSameCompareRatio(analysisGetVo.getOtherCostsSameRatio());
            revenueAnalysisGetResp.add(otherCosts);
        }
        result.setAnalysisGetList(revenueAnalysisGetResp);
        return result;
    }
}

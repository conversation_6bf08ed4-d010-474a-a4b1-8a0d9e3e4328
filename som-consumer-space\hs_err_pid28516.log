#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 101040 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=28516, tid=16580
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.7******.130-jcef (21.0.7+9) (build 21.0.7+9-b895.130)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.7******.130-jcef (21.0.7+9-b895.130, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitlab.seewintech.com': 

Host: Intel(R) Core(TM) i5-9400 CPU @ 2.90GHz, 6 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Mon Jul  7 10:09:58 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 1.560764 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x0000029854cc5c70):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=16580, stack(0x000000f5b7a00000,0x000000f5b7b00000) (1024K)]


Current CompileTask:
C2:1560 1097       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)

Stack: [0x000000f5b7a00000,0x000000f5b7b00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e6049]
V  [jvm.dll+0x8c4343]
V  [jvm.dll+0x8c689e]
V  [jvm.dll+0x8c6f83]
V  [jvm.dll+0x289266]
V  [jvm.dll+0xc675d]
V  [jvm.dll+0xc6c93]
V  [jvm.dll+0x3c4a0b]
V  [jvm.dll+0x1e6462]
V  [jvm.dll+0x250a64]
V  [jvm.dll+0x24fee1]
V  [jvm.dll+0x1cd734]
V  [jvm.dll+0x25f76c]
V  [jvm.dll+0x25dcb6]
V  [jvm.dll+0x3ffa86]
V  [jvm.dll+0x86bea8]
V  [jvm.dll+0x6e480d]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002989a7cd480, length=15, elements={
0x0000029838113350, 0x0000029854cbcd20, 0x0000029854cc05a0, 0x0000029854cc0ff0,
0x0000029854cc3a50, 0x0000029854cc44a0, 0x0000029854cc4ef0, 0x0000029854cc5c70,
0x0000029854ccac30, 0x000002989a10fee0, 0x000002989a111b20, 0x000002989a788bd0,
0x000002989a8ec530, 0x000002989a904130, 0x000002989a906290
}

Java Threads: ( => current thread )
  0x0000029838113350 JavaThread "main"                              [_thread_blocked, id=49196, stack(0x000000f5b6c00000,0x000000f5b6d00000) (1024K)]
  0x0000029854cbcd20 JavaThread "Reference Handler"          daemon [_thread_blocked, id=47184, stack(0x000000f5b7400000,0x000000f5b7500000) (1024K)]
  0x0000029854cc05a0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=50088, stack(0x000000f5b7500000,0x000000f5b7600000) (1024K)]
  0x0000029854cc0ff0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=10512, stack(0x000000f5b7600000,0x000000f5b7700000) (1024K)]
  0x0000029854cc3a50 JavaThread "Attach Listener"            daemon [_thread_blocked, id=38120, stack(0x000000f5b7700000,0x000000f5b7800000) (1024K)]
  0x0000029854cc44a0 JavaThread "Service Thread"             daemon [_thread_blocked, id=49728, stack(0x000000f5b7800000,0x000000f5b7900000) (1024K)]
  0x0000029854cc4ef0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=37360, stack(0x000000f5b7900000,0x000000f5b7a00000) (1024K)]
=>0x0000029854cc5c70 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=16580, stack(0x000000f5b7a00000,0x000000f5b7b00000) (1024K)]
  0x0000029854ccac30 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=43732, stack(0x000000f5b7b00000,0x000000f5b7c00000) (1024K)]
  0x000002989a10fee0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=34088, stack(0x000000f5b7c00000,0x000000f5b7d00000) (1024K)]
  0x000002989a111b20 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=40028, stack(0x000000f5b7d00000,0x000000f5b7e00000) (1024K)]
  0x000002989a788bd0 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=19500, stack(0x000000f5b7f00000,0x000000f5b8000000) (1024K)]
  0x000002989a8ec530 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_vm, id=51452, stack(0x000000f5b8100000,0x000000f5b8200000) (1024K)]
  0x000002989a904130 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=19596, stack(0x000000f5b8200000,0x000000f5b8300000) (1024K)]
  0x000002989a906290 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=16388, stack(0x000000f5b7e00000,0x000000f5b7f00000) (1024K)]
Total: 15

Other Threads:
  0x0000029854c9c570 VMThread "VM Thread"                           [id=40688, stack(0x000000f5b7300000,0x000000f5b7400000) (1024K)] _threads_hazard_ptr=0x000002989a7cd480
  0x0000029854c89b80 WatcherThread "VM Periodic Task Thread"        [id=16108, stack(0x000000f5b7200000,0x000000f5b7300000) (1024K)]
  0x000002983a4707f0 WorkerThread "GC Thread#0"                     [id=30892, stack(0x000000f5b6d00000,0x000000f5b6e00000) (1024K)]
  0x000002983a481550 ConcurrentGCThread "G1 Main Marker"            [id=47684, stack(0x000000f5b6e00000,0x000000f5b6f00000) (1024K)]
  0x000002983a482050 WorkerThread "G1 Conc#0"                       [id=38028, stack(0x000000f5b6f00000,0x000000f5b7000000) (1024K)]
  0x0000029854b54580 ConcurrentGCThread "G1 Refine#0"               [id=46232, stack(0x000000f5b7000000,0x000000f5b7100000) (1024K)]
  0x0000029854b55000 ConcurrentGCThread "G1 Service"                [id=45732, stack(0x000000f5b7100000,0x000000f5b7200000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0  2074 1097       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)
C2 CompilerThread1  2074 1103       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 2

VM state: synchronizing (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff964f6f9b8] Threads_lock - owner thread: 0x0000029854c9c570

Heap address: 0x0000000702800000, size: 4056 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000029855000000-0x0000029855d10000-0x0000029855d10000), size 13697024, SharedBaseAddress: 0x0000029855000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000029856000000-0x0000029896000000, reserved size: 1073741824
Narrow klass base: 0x0000029855000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 6 total, 6 available
 Memory: 16220M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4056M
 Pre-touch: Disabled
 Parallel Workers: 6
 Concurrent Workers: 2
 Concurrent Refinement Workers: 6
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 260096K, used 14336K [0x0000000702800000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 0 survivors (0K)
 Metaspace       used 8756K, committed 9024K, reserved 1114112K
  class space    used 1004K, committed 1152K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|   1|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|   2|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Untracked 
|   3|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Untracked 
|   4|0x0000000703000000, 0x0000000703000000, 0x0000000703200000|  0%| F|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Untracked 
|   5|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Untracked 
|   6|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|   7|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|   8|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000| PB 0x0000000703800000| Untracked 
|   9|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  10|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  11|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  12|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  13|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  14|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  15|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  16|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  17|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  18|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  19|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  20|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  21|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  22|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|  23|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|  24|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  25|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  26|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  27|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  28|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  29|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  30|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  31|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  32|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  33|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  34|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked 
|  35|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked 
|  36|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  37|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  38|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  39|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  40|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  41|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  42|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  43|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  44|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  45|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  46|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  47|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  48|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  49|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  50|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  51|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  52|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  53|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  54|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  55|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  56|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  57|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  58|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  59|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  60|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  61|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  62|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked 
|  63|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  64|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  65|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked 
|  66|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  67|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  68|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  69|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  70|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  71|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  72|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  73|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  74|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  75|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  76|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  77|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  78|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  79|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
|  80|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
|  81|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
|  82|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
|  83|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
|  84|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
|  85|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
|  86|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
|  87|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
|  88|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
|  89|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
|  90|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
|  91|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
|  92|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
|  93|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
|  94|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
|  95|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
|  96|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked 
|  97|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
|  98|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
|  99|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
| 100|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
| 101|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
| 102|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
| 103|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
| 104|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked 
| 105|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked 
| 106|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Untracked 
| 107|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000| PB 0x000000070fe00000| Untracked 
| 108|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000| PB 0x0000000710000000| Untracked 
| 109|0x0000000710200000, 0x0000000710200000, 0x0000000710400000|  0%| F|  |TAMS 0x0000000710200000| PB 0x0000000710200000| Untracked 
| 110|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000| PB 0x0000000710400000| Untracked 
| 111|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000| PB 0x0000000710600000| Untracked 
| 112|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000| PB 0x0000000710800000| Untracked 
| 113|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000| PB 0x0000000710a00000| Untracked 
| 114|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000| PB 0x0000000710c00000| Untracked 
| 115|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000| PB 0x0000000710e00000| Untracked 
| 116|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000| PB 0x0000000711000000| Untracked 
| 117|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000| PB 0x0000000711200000| Untracked 
| 118|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000| PB 0x0000000711400000| Untracked 
| 119|0x0000000711600000, 0x000000071179bd00, 0x0000000711800000| 80%| E|  |TAMS 0x0000000711600000| PB 0x0000000711600000| Complete 
| 120|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%| E|CS|TAMS 0x0000000711800000| PB 0x0000000711800000| Complete 
| 121|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| E|CS|TAMS 0x0000000711a00000| PB 0x0000000711a00000| Complete 
| 122|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| E|CS|TAMS 0x0000000711c00000| PB 0x0000000711c00000| Complete 
| 123|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| E|CS|TAMS 0x0000000711e00000| PB 0x0000000711e00000| Complete 
| 124|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| E|CS|TAMS 0x0000000712000000| PB 0x0000000712000000| Complete 
| 125|0x0000000712200000, 0x0000000712400000, 0x0000000712400000|100%| E|CS|TAMS 0x0000000712200000| PB 0x0000000712200000| Complete 
| 126|0x0000000712400000, 0x0000000712600000, 0x0000000712600000|100%| E|CS|TAMS 0x0000000712400000| PB 0x0000000712400000| Complete 

Card table byte_map: [0x000002984dd50000,0x000002984e540000] _byte_map_base: 0x000002984a53c000

Marking Bits: (CMBitMap*) 0x000002983a470ef0
 Bits: [0x000002984e540000, 0x00000298524a0000)

Polling page: 0x0000029838330000

Metaspace:

Usage:
  Non-class:      7.57 MB used.
      Class:   1004.45 KB used.
       Both:      8.55 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.69 MB ( 12%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.12 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.81 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.14 MB
       Class:  14.73 MB
        Both:  22.87 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 228.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 141.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 383.
num_chunk_merges: 0.
num_chunk_splits: 218.
num_chunks_enlarged: 96.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=387Kb max_used=387Kb free=119612Kb
 bounds [0x0000029845f20000, 0x0000029846190000, 0x000002984d450000]
CodeHeap 'profiled nmethods': size=120000Kb used=1747Kb max_used=1747Kb free=118252Kb
 bounds [0x000002983e450000, 0x000002983e6c0000, 0x0000029845980000]
CodeHeap 'non-nmethods': size=5760Kb used=1433Kb max_used=1447Kb free=4326Kb
 bounds [0x0000029845980000, 0x0000029845bf0000, 0x0000029845f20000]
 total_blobs=1650 nmethods=1131 adapters=424
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.315 Thread 0x0000029854ccac30 nmethod 1119 0x000002983e5ff590 code [0x000002983e5ff780, 0x000002983e5ffc20]
Event: 1.316 Thread 0x0000029854ccac30 1120       3       java.lang.Boolean::valueOf (14 bytes)
Event: 1.316 Thread 0x0000029854ccac30 nmethod 1120 0x000002983e5ffd90 code [0x000002983e5fff40, 0x000002983e6000a8]
Event: 1.316 Thread 0x0000029854cc5c70 nmethod 1095 0x0000029845f7e510 code [0x0000029845f7e6c0, 0x0000029845f7ec18]
Event: 1.316 Thread 0x0000029854cc5c70 1097       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)
Event: 1.317 Thread 0x0000029854ccac30 1121       3       java.lang.invoke.MethodHandles$Lookup::resolveOrFail (48 bytes)
Event: 1.317 Thread 0x0000029854ccac30 nmethod 1121 0x000002983e600110 code [0x000002983e600360, 0x000002983e600a18]
Event: 1.318 Thread 0x0000029854ccac30 1122       3       sun.security.ec.XECOperations::bitAt (23 bytes)
Event: 1.318 Thread 0x0000029854ccac30 nmethod 1122 0x000002983e600d10 code [0x000002983e600ec0, 0x000002983e601058]
Event: 1.318 Thread 0x0000029854ccac30 1123       3       sun.security.util.math.intpoly.IntegerPolynomial25519::reduce (40 bytes)
Event: 1.318 Thread 0x0000029854ccac30 nmethod 1123 0x000002983e601190 code [0x000002983e601360, 0x000002983e601660]
Event: 1.318 Thread 0x0000029854ccac30 1126       3       sun.security.util.math.intpoly.IntegerPolynomial25519::carryReduce (391 bytes)
Event: 1.319 Thread 0x0000029854ccac30 nmethod 1126 0x000002983e601810 code [0x000002983e6019c0, 0x000002983e601e20]
Event: 1.322 Thread 0x0000029854ccac30 1127       3       sun.security.provider.SHA2::implCompress0 (448 bytes)
Event: 1.323 Thread 0x0000029854ccac30 nmethod 1127 0x000002983e602010 code [0x000002983e602260, 0x000002983e602e70]
Event: 1.325 Thread 0x0000029854ccac30 1130       3       java.nio.ByteBuffer::arrayOffset (35 bytes)
Event: 1.325 Thread 0x0000029854ccac30 nmethod 1130 0x000002983e603590 code [0x000002983e603760, 0x000002983e603a30]
Event: 1.325 Thread 0x0000029854ccac30 1131       3       java.util.Objects::checkFromIndexSize (8 bytes)
Event: 1.325 Thread 0x0000029854ccac30 nmethod 1131 0x000002983e603b90 code [0x000002983e603d40, 0x000002983e603f48]
Event: 1.551 Thread 0x000002989a906290 nmethod 1073 0x0000029845f7f390 code [0x0000029845f7f540, 0x0000029845f7f720]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.009 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.dll
Event: 0.037 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.602 Thread 0x000002989a788bd0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000029845f40da8 relative=0x00000000000001c8
Event: 0.602 Thread 0x000002989a788bd0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000029845f40da8 method=java.lang.invoke.VarHandle.checkAccessModeThenIsDirect(Ljava/lang/invoke/VarHandle$AccessDescriptor;)Z @ 4 c2
Event: 0.602 Thread 0x000002989a788bd0 DEOPT PACKING pc=0x0000029845f40da8 sp=0x000000f5b7fff020
Event: 0.602 Thread 0x000002989a788bd0 DEOPT UNPACKING pc=0x00000298459d46a2 sp=0x000000f5b7ffef58 mode 2
Event: 1.318 Thread 0x000002989a8ec530 Uncommon trap: trap_request=0xffffff66 fr.pc=0x0000029845f7ba7c relative=0x000000000000031c
Event: 1.318 Thread 0x000002989a8ec530 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x0000029845f7ba7c method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 1.318 Thread 0x000002989a8ec530 DEOPT PACKING pc=0x0000029845f7ba7c sp=0x000000f5b81fe550
Event: 1.318 Thread 0x000002989a8ec530 DEOPT UNPACKING pc=0x00000298459d46a2 sp=0x000000f5b81fe490 mode 2
Event: 1.318 Thread 0x000002989a8ec530 Uncommon trap: trap_request=0xffffff66 fr.pc=0x0000029845f7ba7c relative=0x000000000000031c
Event: 1.318 Thread 0x000002989a8ec530 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x0000029845f7ba7c method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 1.318 Thread 0x000002989a8ec530 DEOPT PACKING pc=0x0000029845f7ba7c sp=0x000000f5b81fe550
Event: 1.318 Thread 0x000002989a8ec530 DEOPT UNPACKING pc=0x00000298459d46a2 sp=0x000000f5b81fe490 mode 2
Event: 1.318 Thread 0x000002989a8ec530 Uncommon trap: trap_request=0xffffff66 fr.pc=0x0000029845f7ba7c relative=0x000000000000031c
Event: 1.318 Thread 0x000002989a8ec530 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x0000029845f7ba7c method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 1.318 Thread 0x000002989a8ec530 DEOPT PACKING pc=0x0000029845f7ba7c sp=0x000000f5b81fe550
Event: 1.318 Thread 0x000002989a8ec530 DEOPT UNPACKING pc=0x00000298459d46a2 sp=0x000000f5b81fe490 mode 2
Event: 1.318 Thread 0x000002989a8ec530 Uncommon trap: trap_request=0xffffff66 fr.pc=0x0000029845f7ba7c relative=0x000000000000031c
Event: 1.318 Thread 0x000002989a8ec530 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x0000029845f7ba7c method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 1.318 Thread 0x000002989a8ec530 DEOPT PACKING pc=0x0000029845f7ba7c sp=0x000000f5b81fe550
Event: 1.318 Thread 0x000002989a8ec530 DEOPT UNPACKING pc=0x00000298459d46a2 sp=0x000000f5b81fe490 mode 2

Classes loaded (20 events):
Event: 1.324 Loading class com/sun/crypto/provider/GaloisCounterMode$DecryptOp done
Event: 1.325 Loading class sun/security/ssl/EncryptedExtensions$EncryptedExtensionsMessage
Event: 1.325 Loading class sun/security/ssl/EncryptedExtensions$EncryptedExtensionsMessage done
Event: 1.325 Loading class sun/security/ssl/CertificateMessage$T13CertificateMessage
Event: 1.325 Loading class sun/security/ssl/CertificateMessage$T13CertificateMessage done
Event: 1.325 Loading class sun/security/ssl/CertificateMessage$CertificateEntry
Event: 1.325 Loading class sun/security/ssl/CertificateMessage$CertificateEntry done
Event: 1.325 Loading class java/security/cert/X509Certificate
Event: 1.325 Loading class java/security/cert/X509Extension
Event: 1.325 Loading class java/security/cert/X509Extension done
Event: 1.325 Loading class java/security/cert/X509Certificate done
Event: 1.326 Loading class java/security/cert/CertificateFactory
Event: 1.326 Loading class java/security/cert/CertificateFactory done
Event: 1.326 Loading class java/security/cert/CertificateFactorySpi
Event: 1.326 Loading class java/security/cert/CertificateFactorySpi done
Event: 1.326 Loading class sun/security/provider/X509Factory
Event: 1.326 Loading class sun/security/provider/X509Factory done
Event: 1.327 Loading class sun/security/util/Cache$EqualByteArray
Event: 1.327 Loading class sun/security/util/Cache$EqualByteArray done
Event: 1.327 Loading class sun/security/x509/X509CertImpl

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.357 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711eaffe0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000711eaffe0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.357 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711eb6a78}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x0000000711eb6a78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.358 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711ebd508}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000711ebd508) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.362 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711ec1b90}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000711ec1b90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.388 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711eeb950}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000711eeb950) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.388 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711eef2e0}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000711eef2e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.392 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711efe600}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711efe600) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.397 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f30288}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x0000000711f30288) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.398 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f36da0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711f36da0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.399 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f3a1c0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711f3a1c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.525 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711d4e850}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711d4e850) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.570 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711b40328}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000711b40328) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.570 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711b43c90}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000711b43c90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.576 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711b71f88}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711b71f88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.603 Thread 0x000002989a788bd0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711a21200}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000711a21200) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.609 Thread 0x000002989a788bd0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711a5d7d8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000711a5d7d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.616 Thread 0x0000029838113350 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711bff650}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711bff650) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.640 Thread 0x000002989a8ec530 Exception <a 'java/lang/NoSuchMethodError'{0x000000071188a870}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000071188a870) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.714 Thread 0x000002989a8ec530 Exception <a 'java/lang/NoSuchMethodError'{0x00000007118dabc8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000007118dabc8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.306 Thread 0x000002989a8ec530 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711746080}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000711746080) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 0.075 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.075 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.080 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.080 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.251 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.251 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.605 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.606 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.046 Thread 0x0000029838113350 Thread added: 0x0000029854cc0ff0
Event: 0.046 Thread 0x0000029838113350 Thread added: 0x0000029854cc3a50
Event: 0.046 Thread 0x0000029838113350 Thread added: 0x0000029854cc44a0
Event: 0.046 Thread 0x0000029838113350 Thread added: 0x0000029854cc4ef0
Event: 0.046 Thread 0x0000029838113350 Thread added: 0x0000029854cc5c70
Event: 0.050 Thread 0x0000029838113350 Thread added: 0x0000029854ccac30
Event: 0.063 Thread 0x0000029838113350 Thread added: 0x000002989a10fee0
Event: 0.065 Thread 0x0000029838113350 Thread added: 0x000002989a111b20
Event: 0.068 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\net.dll
Event: 0.070 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\nio.dll
Event: 0.072 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\zip.dll
Event: 0.117 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\jimage.dll
Event: 0.267 Thread 0x0000029854ccac30 Thread added: 0x000002989a21b9e0
Event: 0.444 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\sunmscapi.dll
Event: 0.515 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\extnet.dll
Event: 0.521 Thread 0x0000029838113350 Thread added: 0x000002989a788bd0
Event: 0.609 Thread 0x000002989a788bd0 Thread added: 0x000002989a8ec530
Event: 0.720 Thread 0x000002989a788bd0 Thread added: 0x000002989a904130
Event: 0.923 Thread 0x000002989a21b9e0 Thread exited: 0x000002989a21b9e0
Event: 1.242 Thread 0x0000029854ccac30 Thread added: 0x000002989a906290


Dynamic libraries:
0x00007ff7ae320000 - 0x00007ff7ae32a000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.exe
0x00007ffa153e0000 - 0x00007ffa15645000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa143f0000 - 0x00007ffa144b9000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa12910000 - 0x00007ffa12cf8000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa13030000 - 0x00007ffa1317b000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa093e0000 - 0x00007ffa093f8000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jli.dll
0x00007ffa13d70000 - 0x00007ffa13f3a000 	C:\Windows\System32\USER32.dll
0x00007ffa12e80000 - 0x00007ffa12ea7000 	C:\Windows\System32\win32u.dll
0x00007ffa144c0000 - 0x00007ffa144eb000 	C:\Windows\System32\GDI32.dll
0x00007ffa12530000 - 0x00007ffa12667000 	C:\Windows\System32\gdi32full.dll
0x00007ffa12860000 - 0x00007ffa12903000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa0d8a0000 - 0x00007ffa0d8bb000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\VCRUNTIME140.dll
0x00007ff9e2090000 - 0x00007ff9e232a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffa146b0000 - 0x00007ffa14759000 	C:\Windows\System32\msvcrt.dll
0x00007ffa148f0000 - 0x00007ffa14920000 	C:\Windows\System32\IMM32.DLL
0x00007ffa0dfd0000 - 0x00007ffa0dfdc000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\vcruntime140_1.dll
0x00007ff9f0a80000 - 0x00007ff9f0b0d000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\msvcp140.dll
0x00007ff9642a0000 - 0x00007ff965061000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\server\jvm.dll
0x00007ffa14760000 - 0x00007ffa14813000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa13330000 - 0x00007ffa133d6000 	C:\Windows\System32\sechost.dll
0x00007ffa14ab0000 - 0x00007ffa14bc5000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa13560000 - 0x00007ffa135d4000 	C:\Windows\System32\WS2_32.dll
0x00007ffa122d0000 - 0x00007ffa1232e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa04c10000 - 0x00007ffa04c45000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa0e190000 - 0x00007ffa0e19b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa122b0000 - 0x00007ffa122c4000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa112f0000 - 0x00007ffa1130b000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa0d970000 - 0x00007ffa0d97a000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jimage.dll
0x00007ffa06230000 - 0x00007ffa06471000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa13980000 - 0x00007ffa13d05000 	C:\Windows\System32\combase.dll
0x00007ffa14930000 - 0x00007ffa14a11000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa03bf0000 - 0x00007ffa03c29000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa12670000 - 0x00007ffa12709000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa09e20000 - 0x00007ffa09e40000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.dll
0x00007ffa14c40000 - 0x00007ffa15382000 	C:\Windows\System32\SHELL32.dll
0x00007ffa12d00000 - 0x00007ffa12e74000 	C:\Windows\System32\wintypes.dll
0x00007ffa101d0000 - 0x00007ffa10a28000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa136a0000 - 0x00007ffa13791000 	C:\Windows\System32\SHCORE.dll
0x00007ffa14bd0000 - 0x00007ffa14c3a000 	C:\Windows\System32\shlwapi.dll
0x00007ffa12440000 - 0x00007ffa1246f000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa093c0000 - 0x00007ffa093d8000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\zip.dll
0x00007ffa09180000 - 0x00007ffa09190000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\net.dll
0x00007ffa0db10000 - 0x00007ffa0dc2e000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffa11860000 - 0x00007ffa118ca000 	C:\Windows\system32\mswsock.dll
0x00007ffa045d0000 - 0x00007ffa045e6000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\nio.dll
0x00007ffa11b10000 - 0x00007ffa11b2b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffa11250000 - 0x00007ffa1128a000 	C:\Windows\system32\rsaenh.dll
0x00007ffa11900000 - 0x00007ffa1192b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffa12410000 - 0x00007ffa12436000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffa11b30000 - 0x00007ffa11b3c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffa10d20000 - 0x00007ffa10d53000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffa143d0000 - 0x00007ffa143da000 	C:\Windows\System32\NSI.dll
0x00007ffa02d80000 - 0x00007ffa02d8e000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\sunmscapi.dll
0x00007ffa12eb0000 - 0x00007ffa13027000 	C:\Windows\System32\CRYPT32.dll
0x00007ffa11d30000 - 0x00007ffa11d60000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffa11ce0000 - 0x00007ffa11d1f000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ff9e0070000 - 0x00007ff9e0078000 	C:\Windows\system32\wshunix.dll
0x00007ff9fdae0000 - 0x00007ff9fdae9000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\IntelliJ IDEA 2025.1.3\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;D:\IntelliJ IDEA 2025.1.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitlab.seewintech.com': 
java_class_path (initial): D:/IntelliJ IDEA 2025.1.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/IntelliJ IDEA 2025.1.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 6                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4253024256                                {product} {ergonomic}
   size_t MaxNewSize                               = 2550136832                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4253024256                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\jdk-17.0.10
CLASSPATH=.;D:\jdk-17.0.10\lib;D:\jdk-17.0.10\lib\tools.jar
PATH=D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;D:\ShadowBot;D:\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\jdk-17.0.10\bin;E:\JavaSpace\peizhi\apache-maven-3.5.4\apache-maven-3.5.4\bin;D:\Git\cmd;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;D:\cursor\resources\app\bin;E:\node;C:\Program Files\dotnet;D:\ShadowBot;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Microsoft VS Code\bin;D:\IntelliJ IDEA 2025.1.3\bin
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 80088K (0% of 16609336K total physical memory with 2804268K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 7112K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1627K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16448B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 25 days 14:38 hours

CPU: total 6 (initial active 6) (6 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xde, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 6 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 16220M (2736M free)
TotalPageFile size 32693M (AvailPageFile size 57M)
current process WorkingSet (physical memory assigned to process): 78M, peak: 78M
current process commit charge ("private bytes"): 351M, peak: 352M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130) for windows-amd64 JRE (21.0.7+9-b895.130), built on 2025-05-13 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.

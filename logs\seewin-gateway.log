{"appName":"seewin-gateway","time":"2025-07-03 10:48:22","level":"INFO","class":"com.seewin.GateWayApp","method":"logStartupProfileInfo","line":"662","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"appName":"seewin-gateway","time":"2025-07-03 10:48:26","level":"INFO","class":"com.seewin.GateWayApp","method":"logStarted","line":"56","message":"Started GateWayApp in 9.399 seconds (process running for 11.036)"}
{"appName":"seewin-gateway","time":"2025-07-04 10:56:37","level":"INFO","class":"com.seewin.GateWayApp","method":"logStartupProfileInfo","line":"662","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"appName":"seewin-gateway","time":"2025-07-04 11:00:44","level":"INFO","class":"com.seewin.GateWayApp","method":"logStartupProfileInfo","line":"662","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"appName":"seewin-gateway","time":"2025-07-04 11:01:57","level":"INFO","class":"com.seewin.GateWayApp","method":"logStartupProfileInfo","line":"662","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"appName":"seewin-gateway","time":"2025-07-04 11:02:05","level":"INFO","class":"com.seewin.GateWayApp","method":"logStarted","line":"56","message":"Started GateWayApp in 11.501 seconds (process running for 12.285)"}
{"appName":"seewin-gateway","time":"2025-07-04 11:03:00","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-07-04 11:03:00","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:63485|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-07-04 11:05:37","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-07-04 11:05:37","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:64082|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-07-04 11:06:01","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-07-04 11:06:01","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:64143|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-07-04 11:07:37","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-07-04 11:07:37","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:64143|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-07-04 11:09:49","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-07-04 11:09:49","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:64143|Real-IP:0:0:0:0:0:0:0:1"}

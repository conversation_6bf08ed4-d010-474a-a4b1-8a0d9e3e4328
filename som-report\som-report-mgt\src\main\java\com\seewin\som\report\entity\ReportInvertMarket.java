package com.seewin.som.report.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 招商市场报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@TableName("som_report_invert_market")
public class ReportInvertMarket implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id(项目id)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 开始日期
     */
    @TableField("start_date")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @TableField("end_date")
    private LocalDate endDate;

    /**
     * 0-业态维度 1 品类维度
     */
    @TableField("data_type")
    private Integer dataType;

    /**
     * 业态ID
     */
    @TableField("commercial_type_code")
    private Long commercialTypeCode;

    /**
     * 业态名称
     */
    @TableField("commercial_type_name")
    private String commercialTypeName;

    /**
     * 一级品类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 一级品类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 分析指标
     */
    @TableField("indicator")
    private String indicator;

    /**
     * 分析指标类型（0-业态占比  1- 一级品类竞争度 2-门前客流 3-进店客流  4-门前客流流量成本  5-门前客流销售转化率  6-一级品类租售比 7-一级品类租管费单价  8-一级品类销售坪效 9-一级品类门前客流销售转化率  10一级品类门前客流  11-一级品类门前客流流量成本  12-一级品类销售笔数）
     */
    @TableField("indicator_type")
    private Integer indicatorType;

    /**
     * 分析指标描述
     */
    @TableField("indicator_remark")
    private String indicatorRemark;

    /**
     * 本期数据
     */
    @TableField("current_period_data")
    private BigDecimal currentPeriodData;

    /**
     * 同比数据
     */
    @TableField("year_on_year_data")
    private BigDecimal yearOnYearData;

    /**
     * 同比率
     */
    @TableField("year_on_year_rate")
    private BigDecimal yearOnYearRate;

    /**
     * 环比数据
     */
    @TableField("month_on_month_data")
    private BigDecimal monthOnMonthData;

    /**
     * 环比率
     */
    @TableField("month_on_month_rate")
    private BigDecimal monthOnMonthRate;

    /**
     * 均值
     */
    @TableField("mean_value")
    private BigDecimal meanValue;

    /**
     * 切尾均值
     */
    @TableField("trimmed_mean")
    private BigDecimal trimmedMean;

    /**
     * 中位数
     */
    @TableField("median")
    private BigDecimal median;

    /**
     * 众数
     */
    @TableField("most_frequent_value")
    private BigDecimal mostFrequentValue;

    /**
     * 标准差
     */
    @TableField("standard_deviation")
    private BigDecimal standardDeviation;

    /**
     * 方差
     */
    @TableField("variance")
    private BigDecimal variance;

    /**
     * 中位数绝对偏差
     */
    @TableField("median_absolute_deviation")
    private BigDecimal medianAbsoluteDeviation;

    /**
     * 极差
     */
    @TableField("data_range")
    private BigDecimal dataRange;

    /**
     * 四分位差
     */
    @TableField("interquartile_range")
    private BigDecimal interquartileRange;

    /**
     * 第一四分位
     */
    @TableField("first_quartile")
    private BigDecimal firstQuartile;

    /**
     * 第三四分位
     */
    @TableField("third_quartile")
    private BigDecimal thirdQuartile;

    /**
     * 结果解读
     */
    @TableField("result_interpretation")
    private String resultInterpretation;
    /**
     * 可视化图表地址
     */
    @TableField("chart_data")
    private String chartData;
    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;


}

package com.seewin.som.report.resp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 盈亏报告-经营概况
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Getter
@Setter
public class ReportProfitBusinessOverviewListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 房间id
     */
    private Long roomId;
    /**
     * 门店ID
     */
    private String storeId;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 总销售额-本期数据
     */
    private BigDecimal currentPeriod;

    /**
     * 总销售额-同比率
     */
    private BigDecimal currentPeriodSameRatio;

    /**
     * 总销售额-环比率
     */
    private BigDecimal currentPeriodRingRatio;

    /**
     * 总成本（元）-本期数据
     */
    private BigDecimal totalCost;

    /**
     * 总成本（元）-同比率
     */
    private BigDecimal totalCostSameRatio;

    /**
     * 总成本（元）-环比率
     */
    private BigDecimal totalCostRingRatio;

    /**
     * 净利润（元）-本期数据
     */
    private BigDecimal netProfit;

    /**
     * 净利润（元）-同比率
     */
    private BigDecimal netProfitSameRatio;

    /**
     * 净利润（元）-环比率
     */
    private BigDecimal netProfitRingRatio;

    /**
     * al解读
     */
    private String alInterpret;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


}

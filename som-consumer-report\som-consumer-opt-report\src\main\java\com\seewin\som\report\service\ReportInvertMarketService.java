package com.seewin.som.report.service;

import com.seewin.consumer.vo.PageResp;
import com.seewin.som.report.vo.req.ReportInvertMarketListReq;
import com.seewin.som.report.vo.req.ReportInvertMarketGetReq;
import com.seewin.som.report.vo.req.ReportInvertMarketAddReq;
import com.seewin.som.report.vo.req.ReportInvertMarketEditReq;
import com.seewin.som.report.vo.req.ReportInvertMarketDelReq;
import com.seewin.som.report.vo.resp.ReportInvertMarketListItem;
import com.seewin.som.report.vo.resp.ReportInvertMarketGetResp;
import com.seewin.som.report.vo.resp.ReportInvertMarketAddResp;

import java.util.List;

/**
 * <p>
 * 招商市场报告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface ReportInvertMarketService {
    
    /**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    PageResp<ReportInvertMarketListItem> page(ReportInvertMarketListReq listReq);

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    ReportInvertMarketGetResp get(ReportInvertMarketGetReq getReq);

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    ReportInvertMarketAddResp add(ReportInvertMarketAddReq addReq);

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    void edit(ReportInvertMarketEditReq editReq);

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    void del(ReportInvertMarketDelReq delReq);

    List<ReportInvertMarketListItem> list(ReportInvertMarketListReq listReq);
}

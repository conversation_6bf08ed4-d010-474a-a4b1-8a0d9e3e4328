package com.seewin.som.report.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 主数据库销售数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
@Getter
@Setter
public class ReportMasterSaleDataEditDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 铺位号
     */
    private String shopNo;

    /**
     * 应用对象(铺位所属位置)
     */
    private String useObj;

    /**
     * 所属位置(铺位所属位置空间全编码) fcode
     */
    private String objCode;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店品牌名称
     */
    private String brandName;

    /**
     * 适配业态字典id
     */
    private Long commercialTypeCode;

    /**
     * 适配业态名称
     */
    private String commercialTypeName;

    /**
     * 所属一级品类
     */
    private Long categoryId;

    /**
     * 所属一级品类名称
     */
    private String categoryName;

    /**
     * 销售总额（元）
     */
    private BigDecimal totalAmount;

    /**
     * 修改前销售总额（元）
     */
    private BigDecimal beforeTotalAmount;

    /**
     * 销售总笔数（单）
     */
    private Integer totalOrder;

    /**
     * 修改前销售总笔数（单）
     */
    private Integer beforeTotalOrder;

    /**
     * 门店销售额（元）
     */
    private BigDecimal storeAmount;

    /**
     * 修改前门店销售额（元）
     */
    private BigDecimal beforeStoreAmount;

    /**
     * 门店销售笔数（单）
     */
    private Integer storeOrder;

    /**
     * 修改前门店销售笔数（单）
     */
    private Integer beforeStoreOrder;

    /**
     * 外卖销售额（元）
     */
    private BigDecimal takeawayAmount;

    /**
     * 修改前外卖销售额（元）
     */
    private BigDecimal beforeTakeawayAmount;

    /**
     * 外卖销售笔数（单）
     */
    private Integer takeawayOrder;

    /**
     * 修改前外卖销售笔数（单）
     */
    private Integer beforeTakeawayOrder;

    /**
     * 数据来源(0-自动采集  1-手动录入)
     */
    private Integer dataSources;

    /**
     * 数据状态(0-未修改  1-已修改)
     */
    private Integer dataStatus;

    /**
     * 销售日期
     */
    private LocalDate saleTime;

    /**
     * 修改前销售单的销售数据
     */
    private String beforeSaleData;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 城市
     */
    private String city;

    /**
     * 二级类品id
     */
    private Long commercialTwoId;

    /**
     * 二级类品名称
     */
    private String commercialTwo;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}

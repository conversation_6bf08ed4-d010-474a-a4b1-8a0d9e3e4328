package com.seewin.som.commerce.vo.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 招商合同打印表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
@Setter
public class CommerceContractPrintEditReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    /**
     * 租户名称(项目名称)
     */
    @Schema(description = "租户名称(项目名称)")
    @Size(max=255,message = "租户名称(项目名称)最大长度不能超过255")
    private String tenantName;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @Schema(description = "所属组织ID路径")
    @NotBlank(message = "所属组织ID路径不能为空")
    @Size(max=255,message = "所属组织ID路径最大长度不能超过255")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @Schema(description = "所属组织名称路径")
    @Size(max=255,message = "所属组织名称路径最大长度不能超过255")
    private String orgFname;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    @Size(max=255,message = "合同编号最大长度不能超过255")
    private String contractCode;

    /**
     * 合同模板ID
     */
    @Schema(description = "合同模板ID")
    private Long contractTemplateId;

    /**
     * 合同号
     */
    @Schema(description = "合同号")
    @Size(max=255,message = "合同号最大长度不能超过255")
    private String contractNo;

    /**
     * 单据号
     */
    @Schema(description = "单据号")
    @Size(max=255,message = "单据号最大长度不能超过255")
    private String invoiceNo;

    /**
     * 合同登记(备案)号
     */
    @Schema(description = "合同登记(备案)号")
    @Size(max=255,message = "合同登记(备案)号最大长度不能超过255")
    private String registerNo;

    /**
     * 甲方(出租方)
     */
    @Schema(description = "甲方(出租方)")
    @Size(max=64,message = "甲方(出租方)最大长度不能超过64")
    private String lessor;

    /**
     * 甲方(出租方)法定代表人
     */
    @Schema(description = "甲方(出租方)法定代表人")
    @Size(max=64,message = "甲方(出租方)法定代表人最大长度不能超过64")
    private String lessorRepresentative;

    /**
     * 甲方(出租方)住所
     */
    @Schema(description = "甲方(出租方)住所")
    @Size(max=255,message = "甲方(出租方)住所最大长度不能超过255")
    private String lessorAddress;

    /**
     * 乙方(承租方)
     */
    @Schema(description = "乙方(承租方)")
    @Size(max=64,message = "乙方(承租方)最大长度不能超过64")
    private String renter;

    /**
     * 乙方(承租方)法定代表人
     */
    @Schema(description = "乙方(承租方)法定代表人")
    @Size(max=64,message = "乙方(承租方)法定代表人最大长度不能超过64")
    private String renterRepresentative;

    /**
     * 乙方(承租方)住所
     */
    @Schema(description = "乙方(承租方)住所")
    @Size(max=255,message = "乙方(承租方)住所最大长度不能超过255")
    private String renterAddress;

    /**
     * 商铺所在城市
     */
    @Schema(description = "商铺所在区域")
    @Size(max=128,message = "商铺所在区域最大长度不能超过128")
    private String roomCity;

    /**
     * 商铺所在地址
     */
    @Schema(description = "商铺所在地址")
    @Size(max=255,message = "商铺所在地址最大长度不能超过255")
    private String roomAddress;

    /**
     * 铺位号
     */
    @Schema(description = "铺位号")
    @Size(max=64,message = "铺位号最大长度不能超过64")
    private String roomName;

    /**
     * 商铺建筑面积
     */
    @Schema(description = "商铺建筑面积")
    private Double roomBuildArea;

    /**
     * 租期(月份)
     */
    @Schema(description = "租期(月份)")
    private Integer rentPeriod;

    /**
     * 租赁起始时间
     */
    @Schema(description = "租赁起始时间")
    private LocalDate rentStartDate;

    /**
     * 租赁截止时间
     */
    @Schema(description = "租赁截止时间")
    private LocalDate rentEndDate;

    /**
     * 品牌ID
     */
    @Schema(description = "品牌ID")
    private Long brandId;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    @Size(max=255,message = "品牌名称最大长度不能超过255")
    private String brandName;

    /**
     * 经营品牌业态字典code
     */
    @Schema(description = "经营品牌业态字典code")
    private Long brandCommercialTypeCode;

    /**
     * 经营品牌业态名称
     */
    @Schema(description = "经营品牌业态名称")
    @Size(max=255,message = "经营品牌业态名称最大长度不能超过255")
    private String brandCommercialTypeName;

    /**
     * 经营范围
     */
    @Schema(description = "经营范围")
    @Size(max=255,message = "经营范围最大长度不能超过255")
    private String businessScope;

    /**
     * 月租金单价
     */
    @Schema(description = "月租金单价")
    private BigDecimal monthPriceContract;

    /**
     * 月租金总额（元）
     */
    @Schema(description = "月租金总额（元）")
    private BigDecimal monthFeeContract;

    /**
     * 租金递增起始年
     */
    @Schema(description = "租金递增起始年")
    private BigDecimal incrementalStartContract;

    /**
     * 租金递增率(%)
     */
    @Schema(description = "租金递增率(%)")
    private String incrementalRateContract;

    /**
     * 租赁保证金
     */
    @Schema(description = "租赁保证金")
    private BigDecimal rentBailFeeContract;

    /**
     * 公共事业保证金
     */
    @Schema(description = "公共事业保证金")
    private BigDecimal commonBailFeeContract;

    /**
     * 甲方账户名称(核算组织名称)
     */
    @Schema(description = "甲方账户名称(核算组织名称)")
    @Size(max=255,message = "甲方账户名称(核算组织名称)最大长度不能超过255")
    private String accountingOrgName;

    /**
     * 甲方银行名称
     */
    @Schema(description = "甲方银行名称")
    @Size(max=255,message = "甲方银行名称最大长度不能超过255")
    private String bankAccountName;

    /**
     * 甲方银行账号编码
     */
    @Schema(description = "甲方银行账号编码")
    @Size(max=255,message = "甲方银行账号编码最大长度不能超过255")
    private String bankAccountCode;

    /**
     * 商户交付日期
     */
    @Schema(description = "商户交付日期")
    private LocalDate deliveryDate;

    /**
     * 装修免租期(天数)
     */
    @Schema(description = "装修免租期(天数)")
    private Integer feeFreeContract;

    /**
     * 商户开业日期
     */
    @Schema(description = "商户开业日期")
    private LocalDate openDate;

    /**
     * 装修押金(元)
     */
    @Schema(description = "装修押金(元)")
    private BigDecimal decorationBailFeeContract;

    /**
     * POS机设备总价款(元)
     */
    @Schema(description = "POS机设备总价款(元)")
    private BigDecimal posFee;

    /**
     * 通知信息-甲方(出租方)
     */
    @Schema(description = "通知信息-甲方(出租方)")
    @Size(max=64,message = "通知信息-甲方(出租方)最大长度不能超过64")
    private String lessorNotify;

    /**
     * 通知信息-甲方(出租方)地址
     */
    @Schema(description = "通知信息-甲方(出租方)地址")
    @Size(max=255,message = "通知信息-甲方(出租方)地址最大长度不能超过255")
    private String lessorNotifyAddress;

    /**
     * 通知信息-乙方(承租方)
     */
    @Schema(description = "通知信息-乙方(承租方)")
    @Size(max=64,message = "通知信息-乙方(承租方)最大长度不能超过64")
    private String renterNotify;

    /**
     * 通知信息-乙方(承租方)电话
     */
    @Schema(description = "通知信息-乙方(承租方)电话")
    @Size(max=64,message = "通知信息-乙方(承租方)电话最大长度不能超过64")
    private String renterNotifyPhone;

    /**
     * 通知信息-乙方(承租方)地址
     */
    @Schema(description = "通知信息-乙方(承租方)地址")
    @Size(max=255,message = "通知信息-乙方(承租方)地址最大长度不能超过255")
    private String renterNotifyAddress;

    /**
     * 通知信息-乙方(承租方)邮箱
     */
    @Schema(description = "通知信息-乙方(承租方)邮箱")
    @Size(max=64,message = "通知信息-乙方(承租方)邮箱最大长度不能超过64")
    private String renterNotifyEmail;

    /**
     * 通知信息-乙方(承租方)联系人
     */
    @Schema(description = "通知信息-乙方(承租方)联系人")
    @Size(max=64,message = "通知信息-乙方(承租方)联系人最大长度不能超过64")
    private String renterNotifyContactName;

    /**
     * 通知信息-乙方(承租方)联系方式
     */
    @Schema(description = "通知信息-乙方(承租方)联系方式")
    @Size(max=64,message = "通知信息-乙方(承租方)联系方式最大长度不能超过64")
    private String renterNotifyContactPhone;

    /**
     * 标准明细
     */
    @Schema(description = "标准明细")
    @Size(max=65535,message = "标准明细最大长度不能超过65,535")
    private String standardDetail;

    /**
     * 甲方银行账号
     */
    @Schema(description = "甲方银行账号")
    private String bankAccount;

    /**
     * 第三方
     */
    @Schema(description = "第三方")
    private String thirdparty;

    /**
     * 丙方
     */
    @Schema(description = "丙方")
    private String interparty;

    /**
     * 首期预付租管费
     */
    @Schema(description = "首期预付租管费")
    private BigDecimal prepRentFee;

    /**
     * 楼层名称
     */
    @Schema(description = "楼层名称")
    private String floorName;

    /**
     * 功能
     */
    @Schema(description = "功能")
    private String functionName;

    /**
     * 甲方(出租方)邮箱
     */
    @Schema(description = "甲方(出租方)邮箱")
    private String lessorEmail;

    /**
     * 甲方(出租方)联系人
     */
    @Schema(description = "甲方(出租方)联系人")
    private String lessorContactName;

    /**
     * 甲方(出租方)联系方式
     */
    @Schema(description = "甲方(出租方)联系方式")
    private String lessorContactPhone;

    /**
     * 乙方(承租方)身份证号
     */
    @Schema(description = "乙方(承租方)身份证号")
    private String renterCardNumber;

    /**
     * 月物业管理费单价
     */
    @Schema(description = "月物业管理费单价")
    private BigDecimal monthOperatePriceContract;

    /**
     * 月物业管理费总额（元）
     */
    @Schema(description = "月物业管理费总额（元）")
    private BigDecimal monthOperateFeeContract;

    /**
     * 月运营管理费单价
     */
    @Schema(description = "月运营管理费单价")
    private BigDecimal monthManagePriceContract;

    /**
     * 月运营管理费总额（元）
     */
    @Schema(description = "月运营管理费总额（元）")
    private BigDecimal monthManageFeeContract;

    /**
     * 综合管理费
     */
    @Schema(description = "综合管理费")
    private BigDecimal compositeManageFeeContract;

    /**
     * 运营运维使用费预付天数
     */
    @Schema(description = "运营运维使用费预付天数")
    private Integer prepPayDays;

    /**
     * 预付运营平台软硬件使用费
     */
    @Schema(description = "预付运营平台软硬件使用费")
    private BigDecimal prepOperateFee;

    /**
     * 预付运维平台软硬件使用费
     */
    @Schema(description = "预付运维平台软硬件使用费")
    private BigDecimal prepMaintainFee;

    /**
     * 物业管理费保证金
     */
    @Schema(description = "物业管理费保证金")
    private BigDecimal propertyManageBailFeeContract;

    /**
     * 运营管理费保证金
     */
    @Schema(description = "运营管理费保证金")
    private BigDecimal operateManageBailFeeContract;

    /**
     * 其他费用单价
     */
    @Schema(description = "其他费用单价")
    private BigDecimal otherPriceContract;

    /**
     * 其他服务费用（元）
     */
    @Schema(description = "其他服务费用（元）")
    private BigDecimal otherFeeContract;
    @Schema(description = "支付停止日期")
    private LocalDate payStopDate;
    /*
     * 广告位多经点位编号
     */
    private String advertName;

    /*
     * 面积长字符
     */
    private String advertAreaStr;

    /*
     * 场地使用面积（m2）
     */
    private BigDecimal advertArea;

    /*
     * 场地用途
     */
    private String siteUse;

    /*
     * 运营管理费收取方式(0-月,1-季度,2-半年,3-年,4-定价)
     */
    private Integer operatePricePayType;

    /*
     * 运营管理费单价（大写）（圆）
     */
    private String operatePriceContractUpper;

    /*
     * 运营管理费总额（大写）（圆）
     */
    private String operateFeeContractUpper;

    /*
     * 费用支付方式(0-按月支付,1-一次性支付,2-其他支付方式)
     */
    private Integer feePricePayType;

    /*
     * 其他支付方式说明
     */
    private String otherRemark;

    /*
     * 电费收取方式 广告位-(0-运营管理费已包含使用期限内广告位电费，乙方无需另行支付,1-乙方按照目前商业用电的收费标准向甲方交纳电费,2-该场地无电表) 多经点-（0-乙方自行安装独立水、电表并承担相应费用，水费、电费、基础设施服务费等公共事业收费标准按照甲方收费政策执行。1-场地无水电表。2-乙方缴纳的营运管理费已包含水电费，无需另行缴纳。）
     */
    private Integer electricityPayType;

    /*
     * 电费标准（元/月）
     */
    private BigDecimal electricityPrice;

    /*
     * 首次电费支付日期
     */
    private LocalDate electricityPayDate;

    /*
     * 首次电费周期开始日期
     */
    private LocalDate electricityStartDate;

    /*
     * 首次电费周期结束日期
     */
    private LocalDate electricityEndDate;

    /*
     * 首次水电费（元）
     */
    private BigDecimal electricityFee;

    /*
     * 运营管理费保证金月数（个）
     */
    private Integer bailMonth;

    /*
     * 保证金收取方式 广告位-(0-合同签订之日起三日内由乙方一次性支付给甲方。,1-乙方在上期《广告位使用协议》（或《场地租赁合同》）签订时已缴纳保证金)多经点位-（0-乙方应当于签订本协议当日内一次性缴清本协议约定的营运管理费保证金、公共事业保证。1-乙方在上期《临促协议》签订时已缴纳营运管理费保证金、公共事业保证金，自动转为本期《临促协议》的营运管理费保证金）
     */
    private String bailPayType;

    /*
     * 上期已缴纳运营管理费保证金（元）
     */
    private BigDecimal operateManageBailFeeLast;

    /*
     * 租期（天）
     */
    private Integer rentPeriodDay;

    /*
     * 租赁类型：1 纯租、2-纯扣
     */
    private String rentType;

    /*
     * 一次性支付日期
     */
    private LocalDate firstPayDate;

    /*
     * 上期已缴纳公共事业保证金（元）
     */
    private BigDecimal commonBailFeeLast;

    /*
     * 运营管理费保证金、公共事业保证金总额（元）
     */
    private BigDecimal bailTotalFee;

    /*
     * 身份证号码/统一信用代码
     */
    private String supplierCertificateCode;

    /*
     * 合同签署方式  0- 单方签署（乙方商户签署） 1-平台自身与个人用户签署  2-平台自身与企业用户签署  3-企业用户与个人用户签署
     */
    private Integer signType;

    /*
     * 甲方签章关键词
     */
    private String lessorKeyword;


    /*
     * 乙方签章关键词
     */
    private String renterKeyword;


    /*
     * 签署流程ID
     */
    private String signFlowId;


    /*
     * 签署状态  0-未签署  1-签署中  2-已签署  3-拒签
     */
    private Integer signStatus;


    /*
     * 用章公司
     */
    private Integer signCompanyCode;

    /*
     * 印章类别
     */
    private Integer sealTypeCode;


    /*
     * 是否乙方盖章
     */
    private Integer isRenterSeal;

    /*
     * 是否乙方先盖章
     */
    private Integer isRenterSealFirst;

    /*
     * 是否盖骑缝章
     */
    private Integer isCrossPageSeal;


    /*
     * 乙方企业名称
     */
    private String renterCompanyName;


    /*
     * 乙方企业统一社会信用代码
     */
    private String renterCompanyCode;


}

package com.seewin.som;

import cn.hutool.core.thread.ThreadUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.resp.EntProjectListVo;
import com.seewin.som.report.provider.ReportFeeProvider;
import com.seewin.som.report.provider.ReportMasterSaleDataProvider;

import com.seewin.som.report.provider.ReportOperationReportProvider;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.ReportFeeListVo;
import com.seewin.som.report.resp.ReportSaleAndFlowListVo;
import com.seewin.som.report.utils.BigDecimalUtil;
import jakarta.servlet.http.HttpServletRequest;


import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

import java.lang.StringBuilder;
import java.io.IOException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CountDownLatch;


@SpringBootTest
@RunWith(SpringRunner.class)
public class OperationReportTest {
    @Mock
    private HttpServletRequest request;
    @Autowired
    private ReportMasterSaleDataProvider reportMasterSaleDataProvider;
    @Autowired
    private ReportFeeProvider reportFeeProvider;

    @Autowired
    private ReportOperationReportProvider operationReportProvider;
    private static final String authUser = "bab8ebce-de3a-4614-9306-3db5d1386d5b";
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private ChartHttpClient chartHttpClient;

    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;

    @Test
    public void createOperationReport() {
        //获取任务参数  参数格式为时间    todo  修改定时任务获取数据逻辑。 取前一天数据插入。
        String command = "2025-04-07";
        LocalDate localDate = LocalDate.now();
        try {
            if (StringUtils.isNotBlank(command)) {
                localDate = LocalDate.parse(command);
            }
        } catch (DateTimeParseException ex) {
            localDate = LocalDate.now();
        }

        // 判断是否是周一
        if(localDate.getDayOfWeek() == DayOfWeek.MONDAY){
            // 获取上周的日期范围
            LocalDate[] weekDateRange = DateUtil.getLastWeekDateRange(localDate);
            init(weekDateRange[0], weekDateRange[1], 1);
        }

        // 判断是否是1号
        if(localDate.getDayOfMonth() == 1){
            // 获取上个月的日期范围
            LocalDate[] monthDateRange = DateUtil.getLastMonthDateRange(localDate);
            init(monthDateRange[0], monthDateRange[1], 0);
        }

        // 如果既不是周一也不是1号，则返回
        if(localDate.getDayOfWeek() != DayOfWeek.MONDAY && localDate.getDayOfMonth() != 1){
            return;
        }
    }

    /**
     * 根据入参日期生成数据  可以按照周、月维度生成。
     */
    private void init (LocalDate startDate  ,LocalDate endDate,int dateType){

        //先删除年月对应的数据。
        ReportOperationReportListDto delDto = new ReportOperationReportListDto();
        delDto.setStartDate(startDate);
        delDto.setEndDate(endDate);
        operationReportProvider.delete(delDto);
        //同比日期
        LocalDate[] yearOnYearDates = DateUtil.getYearOnYearDateRange(startDate, endDate);
        LocalDate yearOnYearStartDate = yearOnYearDates[0];
        LocalDate yearOnYearEndDate =yearOnYearDates[1];
        //环比日期
        LocalDate[] monthOnMonthDates =DateUtil.getMonthOnMonthDateRange(startDate, endDate);
        LocalDate monthOnMonthStartDate = monthOnMonthDates[0];
        LocalDate monthOnMonthEndDate = monthOnMonthDates[1];
        //当前数据
        List<ReportSaleAndFlowListVo> operationReport = getOperationReport(startDate, endDate, dateType);
        //同比数据
        List<ReportSaleAndFlowListVo> yearOnYearReport = getOperationReport(yearOnYearStartDate, yearOnYearEndDate, dateType);
        //环比数据
        List<ReportSaleAndFlowListVo> monthOnMonthReport = getOperationReport(monthOnMonthStartDate, monthOnMonthEndDate, dateType);
        // 按项目分组处理数据
        Map<Long, List<ReportSaleAndFlowListVo>> projectGroupMap = operationReport.stream()
                .collect(Collectors.groupingBy(ReportSaleAndFlowListVo::getTenantId));

        // 创建结果列表
        List<ReportOperationReportAddDto> resultList = new ArrayList<>();

        List tenantIds = new ArrayList(projectGroupMap.keySet());
        Map<Long, EntProjectListVo> projectInfoByIds = entProjectProvider.getProjectInfoByIds(tenantIds);

        // 处理每个项目的数据
        for (Map.Entry<Long, List<ReportSaleAndFlowListVo>> entry : projectGroupMap.entrySet()) {
            Long tenantId = entry.getKey();
            List<ReportSaleAndFlowListVo> projectReports = entry.getValue();

            // 获取项目名称
            String tenantName = projectReports.isEmpty() ? "" : projectReports.get(0).getTenantName();

            // 计算项目级别的数据
            BigDecimal totalFrontCount = BigDecimal.ZERO;
            BigDecimal totalInShopCount = BigDecimal.ZERO;
            BigDecimal totalSalesAmount = BigDecimal.ZERO;
            BigDecimal totalRentFee = BigDecimal.ZERO;

            // 收集各指标的数据用于统计计算
            List<BigDecimal> frontCountList = new ArrayList<>();
            List<BigDecimal> inShopCountList = new ArrayList<>();
            List<BigDecimal> salesAmountList = new ArrayList<>();
            List<BigDecimal> rentFeeList = new ArrayList<>();

            for (ReportSaleAndFlowListVo report : projectReports) {
                // 门前客流
                if (report.getFrontCount() != null) {
                    BigDecimal frontCount = BigDecimal.valueOf(report.getFrontCount());
                    totalFrontCount = totalFrontCount.add(frontCount);
                    frontCountList.add(frontCount);
                }

                // 进店客流
                if (report.getInShopCount() != null) {
                    BigDecimal inShopCount = BigDecimal.valueOf(report.getInShopCount());
                    totalInShopCount = totalInShopCount.add(inShopCount);
                    inShopCountList.add(inShopCount);
                }

                // 销售额
                if (report.getTotalAmount() != null) {
                    totalSalesAmount = totalSalesAmount.add(report.getTotalAmount());
                    salesAmountList.add(report.getTotalAmount());
                }

                // 租管费
                if (report.getTheoryRent() != null) {
                    totalRentFee = totalRentFee.add(report.getTheoryRent());
                    rentFeeList.add(report.getTheoryRent());
                }
            }

            // 计算租售比
            BigDecimal rentSaleRatio = BigDecimal.ZERO;
            if (totalSalesAmount.compareTo(BigDecimal.ZERO) > 0) {
                rentSaleRatio = totalRentFee.divide(totalSalesAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            }

            // 计算租售比列表
            List<BigDecimal> rentSaleRatioList = new ArrayList<>();
            for (int i = 0; i < Math.min(salesAmountList.size(), rentFeeList.size()); i++) {
                if (salesAmountList.get(i).compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal ratio = rentFeeList.get(i).divide(salesAmountList.get(i), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                    rentSaleRatioList.add(ratio);
                }
            }

            // 获取同比数据
            Map<Long, List<ReportSaleAndFlowListVo>> yearOnYearProjectGroupMap = yearOnYearReport.stream()
                    .collect(Collectors.groupingBy(ReportSaleAndFlowListVo::getTenantId));
            List<ReportSaleAndFlowListVo> yearOnYearProjectReports = yearOnYearProjectGroupMap.getOrDefault(tenantId, new ArrayList<>());

            // 计算同比数据
            BigDecimal yearOnYearFrontCount = calculateTotalFrontCount(yearOnYearProjectReports);
            BigDecimal yearOnYearInShopCount = calculateTotalInShopCount(yearOnYearProjectReports);
            BigDecimal yearOnYearSalesAmount = calculateTotalSalesAmount(yearOnYearProjectReports);
            BigDecimal yearOnYearRentFee = calculateTotalRentFee(yearOnYearProjectReports);
            BigDecimal yearOnYearRentSaleRatio = calculateRentSaleRatio(yearOnYearRentFee, yearOnYearSalesAmount);

            // 获取环比数据
            Map<Long, List<ReportSaleAndFlowListVo>> monthOnMonthProjectGroupMap = monthOnMonthReport.stream()
                    .collect(Collectors.groupingBy(ReportSaleAndFlowListVo::getTenantId));
            List<ReportSaleAndFlowListVo> monthOnMonthProjectReports = monthOnMonthProjectGroupMap.getOrDefault(tenantId, new ArrayList<>());

            // 计算环比数据
            BigDecimal monthOnMonthFrontCount = calculateTotalFrontCount(monthOnMonthProjectReports);
            BigDecimal monthOnMonthInShopCount = calculateTotalInShopCount(monthOnMonthProjectReports);
            BigDecimal monthOnMonthSalesAmount = calculateTotalSalesAmount(monthOnMonthProjectReports);
            BigDecimal monthOnMonthRentFee = calculateTotalRentFee(monthOnMonthProjectReports);
            BigDecimal monthOnMonthRentSaleRatio = calculateRentSaleRatio(monthOnMonthRentFee, monthOnMonthSalesAmount);

            // 创建5个分析指标
            // 1. 门前客流
            ReportOperationReportAddDto frontCountDto = new ReportOperationReportAddDto();
            frontCountDto.setTenantId(tenantId);
            frontCountDto.setTenantName(tenantName);
            frontCountDto.setYear(startDate.getYear());
            frontCountDto.setMonth(startDate.getMonthValue());
            frontCountDto.setStartDate(startDate);
            frontCountDto.setEndDate(endDate);
            frontCountDto.setIndicator("门前客流");
            frontCountDto.setIndicatorType(0);
            frontCountDto.setCurrentPeriodData(totalFrontCount);

            // 设置同比数据
            frontCountDto.setYearOnYearData(yearOnYearFrontCount);
            if (yearOnYearFrontCount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal yearOnYearRate = totalFrontCount.subtract(yearOnYearFrontCount)
                        .divide(yearOnYearFrontCount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                frontCountDto.setYearOnYearRate(yearOnYearRate);
            } else {
                frontCountDto.setYearOnYearRate(BigDecimal.ZERO);
            }

            // 设置环比数据
            frontCountDto.setMonthOnMonthData(monthOnMonthFrontCount);
            if (monthOnMonthFrontCount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal monthOnMonthRate = totalFrontCount.subtract(monthOnMonthFrontCount)
                        .divide(monthOnMonthFrontCount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                frontCountDto.setMonthOnMonthRate(monthOnMonthRate);
            } else {
                frontCountDto.setMonthOnMonthRate(BigDecimal.ZERO);
            }

            // 设置统计数据
            if (!frontCountList.isEmpty()) {
                frontCountDto.setMeanValue(calculateMean(frontCountList));
                frontCountDto.setTrimmedMean(calculateTrimmedMean(frontCountList));
                frontCountDto.setMedian(calculateMedian(frontCountList));
                frontCountDto.setMostFrequentValue(calculateMostFrequentValue(frontCountList));
                frontCountDto.setStandardDeviation(calculateStandardDeviation(frontCountList));
                frontCountDto.setVariance(calculateVariance(frontCountList));
                frontCountDto.setMedianAbsoluteDeviation(calculateMedianAbsoluteDeviation(frontCountList));
                frontCountDto.setDataRange(calculateDataRange(frontCountList));
                frontCountDto.setInterquartileRange(calculateInterquartileRange(frontCountList));
                frontCountDto.setFirstQuartile(calculateFirstQuartile(frontCountList));
                frontCountDto.setThirdQuartile(calculateThirdQuartile(frontCountList));
            }

            // 设置排名信息
            frontCountDto.setTopFiveRank(calculateTopFiveRank(projectReports, "frontCount"));
            frontCountDto.setBottomFiveRank(calculateBottomFiveRank(projectReports, "frontCount"));


            // 2. 进店客流
            ReportOperationReportAddDto inShopCountDto = new ReportOperationReportAddDto();
            inShopCountDto.setTenantId(tenantId);
            inShopCountDto.setTenantName(tenantName);
            inShopCountDto.setYear(startDate.getYear());
            inShopCountDto.setMonth(startDate.getMonthValue());
            inShopCountDto.setStartDate(startDate);
            inShopCountDto.setEndDate(endDate);
            inShopCountDto.setIndicator("进店客流");
            inShopCountDto.setIndicatorType(1);
            inShopCountDto.setCurrentPeriodData(totalInShopCount);

            // 设置同比数据
            inShopCountDto.setYearOnYearData(yearOnYearInShopCount);
            if (yearOnYearInShopCount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal yearOnYearRate = totalInShopCount.subtract(yearOnYearInShopCount)
                        .divide(yearOnYearInShopCount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                inShopCountDto.setYearOnYearRate(yearOnYearRate);
            } else {
                inShopCountDto.setYearOnYearRate(BigDecimal.ZERO);
            }

            // 设置环比数据
            inShopCountDto.setMonthOnMonthData(monthOnMonthInShopCount);
            if (monthOnMonthInShopCount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal monthOnMonthRate = totalInShopCount.subtract(monthOnMonthInShopCount)
                        .divide(monthOnMonthInShopCount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                inShopCountDto.setMonthOnMonthRate(monthOnMonthRate);
            } else {
                inShopCountDto.setMonthOnMonthRate(BigDecimal.ZERO);
            }

            // 设置统计数据
            if (!inShopCountList.isEmpty()) {
                inShopCountDto.setMeanValue(calculateMean(inShopCountList));
                inShopCountDto.setTrimmedMean(calculateTrimmedMean(inShopCountList));
                inShopCountDto.setMedian(calculateMedian(inShopCountList));
                inShopCountDto.setMostFrequentValue(calculateMostFrequentValue(inShopCountList));
                inShopCountDto.setStandardDeviation(calculateStandardDeviation(inShopCountList));
                inShopCountDto.setVariance(calculateVariance(inShopCountList));
                inShopCountDto.setMedianAbsoluteDeviation(calculateMedianAbsoluteDeviation(inShopCountList));
                inShopCountDto.setDataRange(calculateDataRange(inShopCountList));
                inShopCountDto.setInterquartileRange(calculateInterquartileRange(inShopCountList));
                inShopCountDto.setFirstQuartile(calculateFirstQuartile(inShopCountList));
                inShopCountDto.setThirdQuartile(calculateThirdQuartile(inShopCountList));
            }

            // 设置排名信息
            inShopCountDto.setTopFiveRank(calculateTopFiveRank(projectReports, "inShopCount"));
            inShopCountDto.setBottomFiveRank(calculateBottomFiveRank(projectReports, "inShopCount"));

            resultList.add(inShopCountDto);

            // 3. 销售额
            ReportOperationReportAddDto salesAmountDto = new ReportOperationReportAddDto();
            salesAmountDto.setTenantId(tenantId);
            salesAmountDto.setTenantName(tenantName);
            salesAmountDto.setYear(startDate.getYear());
            salesAmountDto.setMonth(startDate.getMonthValue());
            salesAmountDto.setStartDate(startDate);
            salesAmountDto.setEndDate(endDate);
            salesAmountDto.setIndicator("销售额");
            salesAmountDto.setIndicatorType(2);
            salesAmountDto.setCurrentPeriodData(totalSalesAmount);

            // 设置同比数据
            salesAmountDto.setYearOnYearData(yearOnYearSalesAmount);
            if (yearOnYearSalesAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal yearOnYearRate = totalSalesAmount.subtract(yearOnYearSalesAmount)
                        .divide(yearOnYearSalesAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                salesAmountDto.setYearOnYearRate(yearOnYearRate);
            } else {
                salesAmountDto.setYearOnYearRate(BigDecimal.ZERO);
            }

            // 设置环比数据
            salesAmountDto.setMonthOnMonthData(monthOnMonthSalesAmount);
            if (monthOnMonthSalesAmount.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal monthOnMonthRate = totalSalesAmount.subtract(monthOnMonthSalesAmount)
                        .divide(monthOnMonthSalesAmount, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                salesAmountDto.setMonthOnMonthRate(monthOnMonthRate);
            } else {
                salesAmountDto.setMonthOnMonthRate(BigDecimal.ZERO);
            }

            // 设置统计数据
            if (!salesAmountList.isEmpty()) {
                salesAmountDto.setMeanValue(calculateMean(salesAmountList));
                salesAmountDto.setTrimmedMean(calculateTrimmedMean(salesAmountList));
                salesAmountDto.setMedian(calculateMedian(salesAmountList));
                salesAmountDto.setMostFrequentValue(calculateMostFrequentValue(salesAmountList));
                salesAmountDto.setStandardDeviation(calculateStandardDeviation(salesAmountList));
                salesAmountDto.setVariance(calculateVariance(salesAmountList));
                salesAmountDto.setMedianAbsoluteDeviation(calculateMedianAbsoluteDeviation(salesAmountList));
                salesAmountDto.setDataRange(calculateDataRange(salesAmountList));
                salesAmountDto.setInterquartileRange(calculateInterquartileRange(salesAmountList));
                salesAmountDto.setFirstQuartile(calculateFirstQuartile(salesAmountList));
                salesAmountDto.setThirdQuartile(calculateThirdQuartile(salesAmountList));
            }

            // 设置排名信息
            salesAmountDto.setTopFiveRank(calculateTopFiveRank(projectReports, "totalAmount"));
            salesAmountDto.setBottomFiveRank(calculateBottomFiveRank(projectReports, "totalAmount"));

            resultList.add(salesAmountDto);

            // 4. 租管费
            ReportOperationReportAddDto rentFeeDto = new ReportOperationReportAddDto();
            rentFeeDto.setTenantId(tenantId);
            rentFeeDto.setTenantName(tenantName);
            rentFeeDto.setYear(startDate.getYear());
            rentFeeDto.setMonth(startDate.getMonthValue());
            rentFeeDto.setStartDate(startDate);
            rentFeeDto.setEndDate(endDate);
            rentFeeDto.setIndicator("租管费");
            rentFeeDto.setIndicatorType(3);
            rentFeeDto.setCurrentPeriodData(totalRentFee);

            // 设置同比数据
            rentFeeDto.setYearOnYearData(yearOnYearRentFee);
            if (yearOnYearRentFee.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal yearOnYearRate = totalRentFee.subtract(yearOnYearRentFee)
                        .divide(yearOnYearRentFee, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                rentFeeDto.setYearOnYearRate(yearOnYearRate);
            } else {
                rentFeeDto.setYearOnYearRate(BigDecimal.ZERO);
            }

            // 设置环比数据
            rentFeeDto.setMonthOnMonthData(monthOnMonthRentFee);
            if (monthOnMonthRentFee.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal monthOnMonthRate = totalRentFee.subtract(monthOnMonthRentFee)
                        .divide(monthOnMonthRentFee, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                rentFeeDto.setMonthOnMonthRate(monthOnMonthRate);
            } else {
                rentFeeDto.setMonthOnMonthRate(BigDecimal.ZERO);
            }

            // 设置统计数据
            if (!rentFeeList.isEmpty()) {
                rentFeeDto.setMeanValue(calculateMean(rentFeeList));
                rentFeeDto.setTrimmedMean(calculateTrimmedMean(rentFeeList));
                rentFeeDto.setMedian(calculateMedian(rentFeeList));
                rentFeeDto.setMostFrequentValue(calculateMostFrequentValue(rentFeeList));
                rentFeeDto.setStandardDeviation(calculateStandardDeviation(rentFeeList));
                rentFeeDto.setVariance(calculateVariance(rentFeeList));
                rentFeeDto.setMedianAbsoluteDeviation(calculateMedianAbsoluteDeviation(rentFeeList));
                rentFeeDto.setDataRange(calculateDataRange(rentFeeList));
                rentFeeDto.setInterquartileRange(calculateInterquartileRange(rentFeeList));
                rentFeeDto.setFirstQuartile(calculateFirstQuartile(rentFeeList));
                rentFeeDto.setThirdQuartile(calculateThirdQuartile(rentFeeList));
            }

            // 设置排名信息
            rentFeeDto.setTopFiveRank(calculateTopFiveRank(projectReports, "theoryRent"));
            rentFeeDto.setBottomFiveRank(calculateBottomFiveRank(projectReports, "theoryRent"));

            resultList.add(rentFeeDto);

            // 5. 租售比
            ReportOperationReportAddDto rentSaleRatioDto = new ReportOperationReportAddDto();
            rentSaleRatioDto.setTenantId(tenantId);
            rentSaleRatioDto.setTenantName(tenantName);
            rentSaleRatioDto.setYear(startDate.getYear());
            rentSaleRatioDto.setMonth(startDate.getMonthValue());
            rentSaleRatioDto.setStartDate(startDate);
            rentSaleRatioDto.setEndDate(endDate);
            rentSaleRatioDto.setIndicator("租售比");
            rentSaleRatioDto.setIndicatorType(4);
            rentSaleRatioDto.setCurrentPeriodData(rentSaleRatio);

            // 设置同比数据
            rentSaleRatioDto.setYearOnYearData(yearOnYearRentSaleRatio);
            if (yearOnYearRentSaleRatio.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal yearOnYearRate = rentSaleRatio.subtract(yearOnYearRentSaleRatio)
                        .divide(yearOnYearRentSaleRatio, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                rentSaleRatioDto.setYearOnYearRate(yearOnYearRate);
            } else {
                rentSaleRatioDto.setYearOnYearRate(BigDecimal.ZERO);
            }

            // 设置环比数据
            rentSaleRatioDto.setMonthOnMonthData(monthOnMonthRentSaleRatio);
            if (monthOnMonthRentSaleRatio.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal monthOnMonthRate = rentSaleRatio.subtract(monthOnMonthRentSaleRatio)
                        .divide(monthOnMonthRentSaleRatio, 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                rentSaleRatioDto.setMonthOnMonthRate(monthOnMonthRate);
            } else {
                rentSaleRatioDto.setMonthOnMonthRate(BigDecimal.ZERO);
            }

            // 设置统计数据
            if (!rentSaleRatioList.isEmpty()) {
                rentSaleRatioDto.setMeanValue(calculateMean(rentSaleRatioList));
                rentSaleRatioDto.setTrimmedMean(calculateTrimmedMean(rentSaleRatioList));
                rentSaleRatioDto.setMedian(calculateMedian(rentSaleRatioList));
                rentSaleRatioDto.setMostFrequentValue(calculateMostFrequentValue(rentSaleRatioList));
                rentSaleRatioDto.setStandardDeviation(calculateStandardDeviation(rentSaleRatioList));
                rentSaleRatioDto.setVariance(calculateVariance(rentSaleRatioList));
                rentSaleRatioDto.setMedianAbsoluteDeviation(calculateMedianAbsoluteDeviation(rentSaleRatioList));
                rentSaleRatioDto.setDataRange(calculateDataRange(rentSaleRatioList));
                rentSaleRatioDto.setInterquartileRange(calculateInterquartileRange(rentSaleRatioList));
                rentSaleRatioDto.setFirstQuartile(calculateFirstQuartile(rentSaleRatioList));
                rentSaleRatioDto.setThirdQuartile(calculateThirdQuartile(rentSaleRatioList));
            }

            // 设置排名信息
            rentSaleRatioDto.setTopFiveRank(calculateTopFiveRank(projectReports, "rentSaleRatio"));
            rentSaleRatioDto.setBottomFiveRank(calculateBottomFiveRank(projectReports, "rentSaleRatio"));

            resultList.add(rentSaleRatioDto);

            //门前客流可视化。
            ChartDataDto frontCountChartDto = convertToChartData(projectReports, 0);
            ChartHttpClient.ChartData frontCountChartData = chartHttpClient.generateChart(frontCountChartDto);
            //进店客流可视化。
            ChartDataDto inShopCountChartDto = convertToChartData(projectReports, 1);
            ChartHttpClient.ChartData inShopCountChartData = chartHttpClient.generateChart(inShopCountChartDto);
            //销售额可视化。
            ChartDataDto totalAmountChartDto = convertToChartData(projectReports, 2);
            ChartHttpClient.ChartData totalAmountChartData = chartHttpClient.generateChart(totalAmountChartDto);
            //租管费可视化。
            ChartDataDto theoryRentChartDto = convertToChartData(projectReports, 3);
            ChartHttpClient.ChartData theoryRentChartData = chartHttpClient.generateChart(theoryRentChartDto);
            //租售比可视化。
            ChartDataDto rentSaleRatioChartDto = convertToChartData(projectReports, 4);
            ChartHttpClient.ChartData rentSaleRatioChartData = chartHttpClient.generateChart(rentSaleRatioChartDto);
            try {
                frontCountDto.setChartData(objectMapper.writeValueAsString(frontCountChartData));
                inShopCountDto.setChartData(objectMapper.writeValueAsString(inShopCountChartData));
                salesAmountDto.setChartData(objectMapper.writeValueAsString(totalAmountChartData));
                rentFeeDto.setChartData(objectMapper.writeValueAsString(theoryRentChartData));
                rentSaleRatioDto.setChartData(objectMapper.writeValueAsString(rentSaleRatioChartData));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            resultList.add(frontCountDto);
        }
        printStatisticsInfo(resultList);
        // 批量添加运营报告数据
        for (ReportOperationReportAddDto addDto : resultList) {
            EntProjectListVo entProjectListVo = projectInfoByIds.get(addDto.getTenantId());
            if(entProjectListVo != null){
                addDto.setEntId(entProjectListVo.getEntId());
                addDto.setOrgFid(entProjectListVo.getOrgFid());
                addDto.setOrgFname(entProjectListVo.getOrgFname());
            }
            operationReportProvider.add(addDto);
        }
    }

    /**
     *
     * @param startDate
     * @param endDate
     * @param dateType 日期类型 0 月份  1周
     * @return
     */
    private List<ReportSaleAndFlowListVo> getOperationReport(LocalDate startDate, LocalDate endDate,int dateType){
        OperationReportListDto dto = new OperationReportListDto();
        dto.setStartDate(startDate);
        dto.setEndDate(endDate);
        List<ReportSaleAndFlowListVo> operationReport = reportMasterSaleDataProvider.getOperationReport(dto);
        ReportFeeListDto feeDto = new ReportFeeListDto();
        feeDto.setMonth(startDate.getMonthValue());
        feeDto.setYear(startDate.getYear());
        List<ReportFeeListVo> feeList = reportFeeProvider.list(feeDto);
        // 将租管费数据根据项目+门店ID匹配到operationReport中
        if (feeList != null && !feeList.isEmpty() && operationReport != null && !operationReport.isEmpty()) {
            // 创建项目+门店ID到租管费的映射
            Map<String, ReportFeeListVo> feeMap = feeList.stream()
                    .filter(fee -> fee.getTenantId() != null && fee.getRoomId() != null)
                    .collect(Collectors.toMap(
                            fee -> fee.getTenantId() + "_" + fee.getStoreId(),
                            fee -> fee,
                            (existing, replacement) -> existing
                    ));

            // 遍历operationReport，填充租管费数据
            for (ReportSaleAndFlowListVo report : operationReport) {
                if (report.getTenantId() != null && report.getStoreId() != null) {
                    String key = report.getTenantId() + "_" + report.getStoreId();
                    ReportFeeListVo fee = feeMap.get(key);
                    if (fee != null) {
                        // 设置租管费数据
                        if(dateType==1){
                            //周维度的直接除以4.
                            BigDecimal theoryRent = BigDecimalUtil.divide(fee.getTheoryRent(), BigDecimal.valueOf(4), 2);
                            report.setTheoryRent(theoryRent);
                        }else{
                            report.setTheoryRent(fee.getTheoryRent());
                        }
                    }
                }
            }
        }
        return operationReport;
    }
    private void printStatisticsInfo(List<ReportOperationReportAddDto> resultList) {
        // 按项目分组
        Map<Long, List<ReportOperationReportAddDto>> projectGroupMap = resultList.stream()
                .collect(Collectors.groupingBy(ReportOperationReportAddDto::getTenantId));

        // 创建OllamaHttpClient实例
        OllamaHttpClient ollamaClient = new OllamaHttpClient();

        // 将项目列表转换为数组，以便进行分割
        List<Map.Entry<Long, List<ReportOperationReportAddDto>>> projectEntries =
                new ArrayList<>(projectGroupMap.entrySet());

        // 计算每个线程处理的项目数量
        int totalProjects = projectEntries.size();
        int projectsPerThread = (int) Math.ceil(totalProjects / 5.0);

        // 创建CountDownLatch，数量为线程数
        CountDownLatch countDownLatch = ThreadUtil.newCountDownLatch(5);

        // 创建5个线程，每个线程处理一部分数据
        for (int i = 0; i < 5; i++) {
            final int threadIndex = i;
            final int startIndex = i * projectsPerThread;
            final int endIndex = Math.min((i + 1) * projectsPerThread, totalProjects);

            // 如果起始索引超出了项目数量，则跳过这个线程
            if (startIndex >= totalProjects) {
                countDownLatch.countDown();
                continue;
            }

            // 获取当前线程需要处理的项目
            final List<Map.Entry<Long, List<ReportOperationReportAddDto>>> threadProjects =
                    projectEntries.subList(startIndex, endIndex);

            // 使用ThreadUtil创建线程处理数据
            ThreadUtil.execAsync(() -> {
                try {
                    // 处理分配给当前线程的项目
                    for (Map.Entry<Long, List<ReportOperationReportAddDto>> entry : threadProjects) {
                        Long tenantId = entry.getKey();
                        List<ReportOperationReportAddDto> projectReports = entry.getValue();

                        // 获取项目名称
                        String tenantName = projectReports.isEmpty() ? "" : projectReports.get(0).getTenantName();

                        StringBuilder sb = new StringBuilder();

                        // 遍历每个指标，构建完整的分析文本
                        for (ReportOperationReportAddDto report : projectReports) {
                            String indicator = report.getIndicator();
                            sb.append(indicator).append("维度：\n");
                            sb.append("  本期数据为: ").append(report.getCurrentPeriodData()).append("\n");
                            sb.append("  同比数据为: ").append(report.getYearOnYearData()).append("\n");
                            sb.append("  同比率为: ").append(report.getYearOnYearRate()).append("\n");
                            sb.append("  环比数据为: ").append(report.getMonthOnMonthData()).append("\n");
                            sb.append("  环比率为: ").append(report.getMonthOnMonthRate()).append("\n");
                            sb.append("  均值为: ").append(report.getMeanValue()).append("\n");
                            sb.append("  切尾均值为: ").append(report.getTrimmedMean()).append("\n");
                            sb.append("  中位数为: ").append(report.getMedian()).append("\n");
                            sb.append("  众数为: ").append(report.getMostFrequentValue()).append("\n");
                            sb.append("  标准差为: ").append(report.getStandardDeviation()).append("\n");
                            sb.append("  方差为: ").append(report.getVariance()).append("\n");
                            sb.append("  中位数绝对偏差为: ").append(report.getMedianAbsoluteDeviation()).append("\n");
                            sb.append("  极差为: ").append(report.getDataRange()).append("\n");
                            sb.append("  四分位差为: ").append(report.getInterquartileRange()).append("\n");
                            sb.append("  第一四分位为: ").append(report.getFirstQuartile()).append("\n");
                            sb.append("  第三四分位为: ").append(report.getThirdQuartile()).append("\n");
                            sb.append("  前五名排名: ").append(report.getTopFiveRank()).append("\n");
                            sb.append("  后五名排名: ").append(report.getBottomFiveRank()).append("\n");
                            sb.append("\n");
                        }

                        sb.append("----------------------------------------\n");

                        // 打印拼接好的字符串
                        System.out.println(sb.toString());

                        // 使用OllamaHttpClient处理统计指标信息
                        final String prompt = sb.toString() + "请直接给出分析结果，不要包含思考过程。分析应该包括：1.整体趋势 2.具体维度分析 3.核心问题 4.改进建议";

                        try {
                            String interpretation = ollamaClient.chat(prompt);
                            // 清理返回结果中的思考过程
                            // 新增清理逻辑
                            if (interpretation.contains("</think>")) {
                                interpretation = interpretation.split("</think>", 2)[1].trim();
                            }

                            // 将同一个解读结果赋值给项目的所有指标
                            for (ReportOperationReportAddDto report : projectReports) {
                                report.setResultInterpretation(interpretation);
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                            String errorMessage = "无法生成解读结果: " + e.getMessage();
                            for (ReportOperationReportAddDto report : projectReports) {
                                report.setResultInterpretation(errorMessage);
                            }
                        }
                    }
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        // 等待所有线程完成
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 计算均值
     */
    private BigDecimal calculateMean(List<BigDecimal> data) {
        if (data == null || data.isEmpty()) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = data.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        return sum.divide(new BigDecimal(data.size()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算切尾均值（去除最高和最低值后的均值）
     */
    private BigDecimal calculateTrimmedMean(List<BigDecimal> data) {
        if (data == null || data.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // 如果数据量太少，直接返回均值
        if (data.size() < 20) {
            return calculateMean(data);
        }

        // 排序数据
        List<BigDecimal> sortedData = new ArrayList<>(data);
        sortedData.sort(Comparator.naturalOrder());

        // 计算需要去除的数据量（首尾各5%）
        int trimCount = (int) Math.round(data.size() * 0.05);

        // 使用subList截取中间90%的数据，避免循环移除
        int startIndex = trimCount;
        int endIndex = sortedData.size() - trimCount;

        // 确保索引在有效范围内
        if (startIndex >= endIndex) {
            return calculateMean(sortedData);
        }
        List<BigDecimal> trimmedData = sortedData.subList(startIndex, endIndex);
        return calculateMean(trimmedData);
    }

    /**
     * 计算中位数
     */
    private BigDecimal calculateMedian(List<BigDecimal> data) {
        if (data == null || data.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<BigDecimal> sortedData = new ArrayList<>(data);
        sortedData.sort(Comparator.naturalOrder());

        int size = sortedData.size();
        if (size % 2 == 0) {
            // 偶数个元素，取中间两个元素的平均值
            BigDecimal sum = sortedData.get(size / 2 - 1).add(sortedData.get(size / 2));
            return sum.divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
        } else {
            // 奇数个元素，取中间元素
            return sortedData.get(size / 2);
        }
    }

    /**
     * 计算众数
     */
    private BigDecimal calculateMostFrequentValue(List<BigDecimal> data) {
        if (data == null || data.isEmpty()) {
            return BigDecimal.ZERO;
        }

        Map<BigDecimal, Long> frequencyMap = data.stream()
                .collect(Collectors.groupingBy(value -> value, Collectors.counting()));

        return frequencyMap.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 计算标准差
     */
    private BigDecimal calculateStandardDeviation(List<BigDecimal> data) {
        if (data == null || data.size() < 2) {
            return BigDecimal.ZERO;
        }

        BigDecimal variance = calculateVariance(data);
        return new BigDecimal(Math.sqrt(variance.doubleValue())).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 计算方差
     */
    private BigDecimal calculateVariance(List<BigDecimal> data) {
        if (data == null || data.size() < 2) {
            return BigDecimal.ZERO;
        }

        BigDecimal mean = calculateMean(data);
        BigDecimal sumSquaredDiff = data.stream()
                .map(value -> value.subtract(mean).pow(2))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return sumSquaredDiff.divide(new BigDecimal(data.size() - 1), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算中位数绝对偏差
     */
    private BigDecimal calculateMedianAbsoluteDeviation(List<BigDecimal> data) {
        if (data == null || data.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal median = calculateMedian(data);
        List<BigDecimal> absoluteDeviations = data.stream()
                .map(value -> value.subtract(median).abs())
                .collect(Collectors.toList());

        return calculateMedian(absoluteDeviations);
    }

    /**
     * 计算极差
     */
    private BigDecimal calculateDataRange(List<BigDecimal> data) {
        if (data == null || data.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal min = data.stream().min(Comparator.naturalOrder()).orElse(BigDecimal.ZERO);
        BigDecimal max = data.stream().max(Comparator.naturalOrder()).orElse(BigDecimal.ZERO);

        return max.subtract(min);
    }

    /**
     * 计算四分位差
     */
    private BigDecimal calculateInterquartileRange(List<BigDecimal> data) {
        if (data == null || data.size() < 4) {
            return BigDecimal.ZERO;
        }

        BigDecimal firstQuartile = calculateFirstQuartile(data);
        BigDecimal thirdQuartile = calculateThirdQuartile(data);

        return thirdQuartile.subtract(firstQuartile);
    }

    /**
     * 计算第一四分位
     */
    private BigDecimal calculateFirstQuartile(List<BigDecimal> data) {
        if (data == null || data.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<BigDecimal> sortedData = new ArrayList<>(data);
        sortedData.sort(Comparator.naturalOrder());

        int size = sortedData.size();
        int q1Index = (int) Math.ceil(size * 0.25) - 1;

        if (q1Index < 0) {
            return sortedData.get(0);
        }

        return sortedData.get(q1Index);
    }

    /**
     * 计算第三四分位
     */
    private BigDecimal calculateThirdQuartile(List<BigDecimal> data) {
        if (data == null || data.isEmpty()) {
            return BigDecimal.ZERO;
        }

        List<BigDecimal> sortedData = new ArrayList<>(data);
        sortedData.sort(Comparator.naturalOrder());

        int size = sortedData.size();
        int q3Index = (int) Math.ceil(size * 0.75) - 1;

        if (q3Index >= size) {
            return sortedData.get(size - 1);
        }

        return sortedData.get(q3Index);
    }

    /**
     * 计算门前客流总和
     */
    private BigDecimal calculateTotalFrontCount(List<ReportSaleAndFlowListVo> reports) {
        if (reports == null || reports.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return reports.stream()
                .filter(report -> report.getFrontCount() != null)
                .map(report -> BigDecimal.valueOf(report.getFrontCount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算进店客流总和
     */
    private BigDecimal calculateTotalInShopCount(List<ReportSaleAndFlowListVo> reports) {
        if (reports == null || reports.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return reports.stream()
                .filter(report -> report.getInShopCount() != null)
                .map(report -> BigDecimal.valueOf(report.getInShopCount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算销售额总和
     */
    private BigDecimal calculateTotalSalesAmount(List<ReportSaleAndFlowListVo> reports) {
        if (reports == null || reports.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return reports.stream()
                .filter(report -> report.getTotalAmount() != null)
                .map(ReportSaleAndFlowListVo::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算租管费总和
     */
    private BigDecimal calculateTotalRentFee(List<ReportSaleAndFlowListVo> reports) {
        if (reports == null || reports.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return reports.stream()
                .filter(report -> report.getTheoryRent() != null)
                .map(ReportSaleAndFlowListVo::getTheoryRent)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算租售比
     */
    private BigDecimal calculateRentSaleRatio(BigDecimal rentFee, BigDecimal salesAmount) {
        if (salesAmount == null || salesAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        return rentFee.divide(salesAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 计算前五名排名
     * @param reports 报告列表
     * @param fieldName 字段名称
     * @return JSON格式的排名信息
     */
    private String calculateTopFiveRank(List<ReportSaleAndFlowListVo> reports, String fieldName) {
        try {
            if (reports == null || reports.isEmpty()) {
                return "[]";
            }

            // 根据字段名称排序
            List<ReportSaleAndFlowListVo> sortedReports;
            switch (fieldName) {
                case "frontCount":
                    sortedReports = reports.stream()
                            .filter(report -> report.getFrontCount() != null)
                            .sorted(Comparator.comparing(ReportSaleAndFlowListVo::getFrontCount).reversed())
                            .collect(Collectors.toList());
                    break;
                case "inShopCount":
                    sortedReports = reports.stream()
                            .filter(report -> report.getInShopCount() != null)
                            .sorted(Comparator.comparing(ReportSaleAndFlowListVo::getInShopCount).reversed())
                            .collect(Collectors.toList());
                    break;
                case "totalAmount":
                    sortedReports = reports.stream()
                            .filter(report -> report.getTotalAmount() != null)
                            .sorted(Comparator.comparing(ReportSaleAndFlowListVo::getTotalAmount).reversed())
                            .collect(Collectors.toList());
                    break;
                case "theoryRent":
                    sortedReports = reports.stream()
                            .filter(report -> report.getTheoryRent() != null)
                            .sorted(Comparator.comparing(ReportSaleAndFlowListVo::getTheoryRent).reversed())
                            .collect(Collectors.toList());
                    break;
                case "rentSaleRatio":
                    // 租售比需要特殊处理，因为它是计算得出的
                    List<Map<String, Object>> rentSaleRatioList = new ArrayList<>();
                    for (ReportSaleAndFlowListVo report : reports) {
                        if (report.getTheoryRent() != null && report.getTotalAmount() != null
                                && report.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal ratio = report.getTheoryRent()
                                    .divide(report.getTotalAmount(), 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));

                            Map<String, Object> item = new HashMap<>();
                            item.put("storeId", report.getStoreId());
                            item.put("brandName", report.getBrandName());
                            item.put("value", ratio);
                            rentSaleRatioList.add(item);
                        }
                    }

                    // 按租售比排序
                    rentSaleRatioList.sort((a, b) -> {
                        BigDecimal valueA = (BigDecimal) a.get("value");
                        BigDecimal valueB = (BigDecimal) b.get("value");
                        return valueB.compareTo(valueA);
                    });

                    // 取前5名
                    List<Map<String, Object>> topFive = rentSaleRatioList.stream()
                            .limit(5)
                            .collect(Collectors.toList());

                    // 转换为JSON
                    return objectMapper.writeValueAsString(topFive);
                default:
                    return "[]";
            }

            // 取前5名
            List<ReportSaleAndFlowListVo> topFive = sortedReports.stream()
                    .limit(5)
                    .collect(Collectors.toList());

            // 转换为JSON格式
            ArrayNode arrayNode = objectMapper.createArrayNode();
            for (ReportSaleAndFlowListVo report : topFive) {
                ObjectNode objectNode = objectMapper.createObjectNode();
                objectNode.put("storeId", report.getStoreId());
                objectNode.put("brandName", report.getBrandName());

                // 根据字段名称设置值
                switch (fieldName) {
                    case "frontCount":
                        objectNode.put("value", report.getFrontCount());
                        break;
                    case "inShopCount":
                        objectNode.put("value", report.getInShopCount());
                        break;
                    case "totalAmount":
                        objectNode.put("value", report.getTotalAmount().toString());
                        break;
                    case "theoryRent":
                        objectNode.put("value", report.getTheoryRent().toString());
                        break;
                }

                arrayNode.add(objectNode);
            }

            return arrayNode.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "[]";
        }
    }

    /**
     * 计算后五名排名
     * @param reports 报告列表
     * @param fieldName 字段名称
     * @return JSON格式的排名信息
     */
    private String calculateBottomFiveRank(List<ReportSaleAndFlowListVo> reports, String fieldName) {
        try {
            if (reports == null || reports.isEmpty()) {
                return "[]";
            }

            // 根据字段名称排序
            List<ReportSaleAndFlowListVo> sortedReports;
            switch (fieldName) {
                case "frontCount":
                    sortedReports = reports.stream()
                            .filter(report -> report.getFrontCount() != null)
                            .sorted(Comparator.comparing(ReportSaleAndFlowListVo::getFrontCount))
                            .collect(Collectors.toList());
                    break;
                case "inShopCount":
                    sortedReports = reports.stream()
                            .filter(report -> report.getInShopCount() != null)
                            .sorted(Comparator.comparing(ReportSaleAndFlowListVo::getInShopCount))
                            .collect(Collectors.toList());
                    break;
                case "totalAmount":
                    sortedReports = reports.stream()
                            .filter(report -> report.getTotalAmount() != null)
                            .sorted(Comparator.comparing(ReportSaleAndFlowListVo::getTotalAmount))
                            .collect(Collectors.toList());
                    break;
                case "theoryRent":
                    sortedReports = reports.stream()
                            .filter(report -> report.getTheoryRent() != null)
                            .sorted(Comparator.comparing(ReportSaleAndFlowListVo::getTheoryRent))
                            .collect(Collectors.toList());
                    break;
                case "rentSaleRatio":
                    // 租售比需要特殊处理，因为它是计算得出的
                    List<Map<String, Object>> rentSaleRatioList = new ArrayList<>();
                    for (ReportSaleAndFlowListVo report : reports) {
                        if (report.getTheoryRent() != null && report.getTotalAmount() != null
                                && report.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal ratio = report.getTheoryRent()
                                    .divide(report.getTotalAmount(), 4, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));

                            Map<String, Object> item = new HashMap<>();
                            item.put("storeId", report.getStoreId());
                            item.put("brandName", report.getBrandName());
                            item.put("value", ratio);
                            rentSaleRatioList.add(item);
                        }
                    }

                    // 按租售比排序
                    rentSaleRatioList.sort((a, b) -> {
                        BigDecimal valueA = (BigDecimal) a.get("value");
                        BigDecimal valueB = (BigDecimal) b.get("value");
                        return valueA.compareTo(valueB);
                    });

                    // 取后5名
                    List<Map<String, Object>> bottomFive = rentSaleRatioList.stream()
                            .limit(5)
                            .collect(Collectors.toList());

                    // 转换为JSON
                    return objectMapper.writeValueAsString(bottomFive);
                default:
                    return "[]";
            }

            // 取后5名
            List<ReportSaleAndFlowListVo> bottomFive = sortedReports.stream()
                    .limit(5)
                    .collect(Collectors.toList());

            // 转换为JSON格式
            ArrayNode arrayNode = objectMapper.createArrayNode();
            for (ReportSaleAndFlowListVo report : bottomFive) {
                ObjectNode objectNode = objectMapper.createObjectNode();
                objectNode.put("storeId", report.getStoreId());
                objectNode.put("brandName", report.getBrandName());

                // 根据字段名称设置值
                switch (fieldName) {
                    case "frontCount":
                        objectNode.put("value", report.getFrontCount());
                        break;
                    case "inShopCount":
                        objectNode.put("value", report.getInShopCount());
                        break;
                    case "totalAmount":
                        objectNode.put("value", report.getTotalAmount().toString());
                        break;
                    case "theoryRent":
                        objectNode.put("value", report.getTheoryRent().toString());
                        break;
                }

                arrayNode.add(objectNode);
            }

            return arrayNode.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return "[]";
        }
    }

    /**
     * 将销售和客流数据转换为图表数据格式
     * @param projectReports 项目报告数据
     * @param indicatorType 指标类型（0:门前客流, 1:进店客流, 2:销售额, 3:租管费, 4:租售比）
     * @return 图表数据DTO
     */
    private ChartDataDto convertToChartData(List<ReportSaleAndFlowListVo> projectReports, Integer indicatorType) {
        ChartDataDto chartData = new ChartDataDto();
        List<ChartDataDto.ChartDataItem> dataItems = new ArrayList<>();
        ChartDataDto.ChartDataItem dataItem = new ChartDataDto.ChartDataItem();

        // 设置指标标签
        String label;
        Integer width=1200;
        Integer height=500;
        String keyTitle;
        String valueTitle;
        String countTitle;
        switch (indicatorType) {
            case 0:
                label = "门前客流";
                keyTitle="门店id";
                valueTitle="门前客流（人）";
                countTitle="门店数量（个）";
                break;
            case 1:
                label = "进店客流";
                keyTitle="门店id";
                valueTitle="进店客流（人）";
                countTitle="门店数量（个）";
                break;
            case 2:
                label = "销售额";
                keyTitle="门店id";
                valueTitle="销售额（元）";
                countTitle="门店数量（个）";
                break;
            case 3:
                label = "租管费";
                keyTitle="门店id";
                valueTitle="租管费（元）";
                countTitle="门店数量（个）";
                break;
            case 4:
                label = "租售比";
                keyTitle="门店id";
                valueTitle="租售比";
                countTitle="门店数量（个）";
                break;
            default:
                label = "未知指标";
                keyTitle="";
                valueTitle="";
                countTitle="";
        }
        dataItem.setLabel(label);
        chartData.setWidth(width);
        chartData.setHeight(height);
        chartData.setKeyTitle(keyTitle);
        chartData.setValueTitle(valueTitle);
        chartData.setCountTitle(countTitle);

        List<ChartDataDto.StatItem> stats = new ArrayList<>();

        // 根据指标类型获取对应的数据
        for (ReportSaleAndFlowListVo report : projectReports) {
            ChartDataDto.StatItem statItem = new ChartDataDto.StatItem();
            statItem.setKey(report.getStoreId()); // 使用品牌名称作为key

            // 根据指标类型设置对应的值
            switch (indicatorType) {
                case 0: // 门前客流
                    if (report.getFrontCount() != null) {
                        statItem.setValue(report.getFrontCount().doubleValue());
                        stats.add(statItem);
                    }
                    break;
                case 1: // 进店客流
                    if (report.getInShopCount() != null) {
                        statItem.setValue(report.getInShopCount().doubleValue());
                        stats.add(statItem);
                    }
                    break;
                case 2: // 销售额
                    if (report.getTotalAmount() != null) {
                        statItem.setValue(report.getTotalAmount().doubleValue());
                        stats.add(statItem);
                    }
                    break;
                case 3: // 租管费
                    if (report.getTheoryRent() != null) {
                        statItem.setValue(report.getTheoryRent().doubleValue());
                        stats.add(statItem);
                    }
                    break;
                case 4: // 租售比
                    if (report.getTheoryRent() != null && report.getTotalAmount() != null
                            && report.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal ratio = report.getTheoryRent()
                                .divide(report.getTotalAmount(), 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                        statItem.setValue(ratio.doubleValue());
                        stats.add(statItem);
                    }
                    break;
            }
        }

        dataItem.setStats(stats);
        dataItems.add(dataItem);
        chartData.setData(dataItems);

        // 设置图表类型
        chartData.setType(Arrays.asList("box", "density", "barDensity", "scatter"));

        return chartData;
    }
}



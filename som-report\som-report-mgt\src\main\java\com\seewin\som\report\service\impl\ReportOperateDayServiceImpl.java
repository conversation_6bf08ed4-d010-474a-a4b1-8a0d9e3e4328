package com.seewin.som.report.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.seewin.som.report.entity.ReportOperateDay;
import com.seewin.som.report.mapper.ReportOperateDayMapper;
import com.seewin.som.report.req.*;
import com.seewin.som.report.resp.*;
import com.seewin.som.report.service.ReportOperateDayService;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 项目面积数据统计到天 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Service
public class ReportOperateDayServiceImpl extends ServiceImpl<ReportOperateDayMapper, ReportOperateDay> implements ReportOperateDayService {

    @Autowired
    private ReportOperateDayMapper reportOperateDayMapper;

    @Override
    public List<ReportMultiDataVo> rentRate(ReportMultiDataDto dto) {
        return reportOperateDayMapper.rentRate(dto);
    }

    @Override
    public List<ReportMultiDataVo> openRate(ReportMultiDataDto dto) {
        return reportOperateDayMapper.openRate(dto);
    }

    @Override
    public List<ReportMultiDataVo> openArea(ReportMultiDataDto dto) {
        return reportOperateDayMapper.openArea(dto);
    }

    @Override
    public List<ReportMultiDataDetailVo> saleSquareStoreContrast(ReportMultiDataDto dto) {
        return reportOperateDayMapper.saleSquareStoreContrast(dto);
    }

    @Override
    public Double saleSquareArea(ReportMultiDataDto dto) {
        return reportOperateDayMapper.saleSquareArea(dto);
    }

    @Override
    public List<ReportMultiDataDetailVo> saleSquareTenantDetail(ReportMultiDataDto dto) {
        return reportOperateDayMapper.saleSquareTenantDetail(dto);
    }

    @Override
    public List<ReportMultiDataVo> getRoomAreaByData(MobileSaleDataAnalyseDto dto) {
        return reportOperateDayMapper.getRoomAreaByData(dto);
    }

    @Override
    public List<ReportOperateDayStatisticVo> getOperatingRooms(CategoryAnalyseDto dto) {
        return reportOperateDayMapper.getOperatingRooms(dto);
    }

    @Override
    public List<ReportOperateDayStatisticVo> getOperatingRoomsDetail(CategoryAnalyseDto dto) {
        return reportOperateDayMapper.getOperatingRoomsDetail(dto);
    }

    @Override
    public List<ReportMultiAnalyseVo> multiAnalyseList(ReportMultiAnalyseListDto dto) {
        return reportOperateDayMapper.multiAnalyseList(dto);
    }
    @Override
    public List<ReportOperateDayListVo> getOperateDayAndFlowList( ReportOperateDayListDto dto) {
        return reportOperateDayMapper.getOperateDayAndFlowList(dto);
    }

}

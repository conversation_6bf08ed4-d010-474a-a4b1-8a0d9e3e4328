1、帮我写一个规则，就是在写代码之前先梳理逻辑，业务流程，然后先不用写代码。在开始写代码之前先复述一遍需求，然后一次最多写1-2个controller接口
2、写之前规划好步骤确保分步执行
3、模块包括 生产者：som-commerce 消费者：som-consumer-commerce、som-commerce-report
4、服务会涉及Resp、Req 、 DTO、VO、Entity、service、mapper等 就是以上规则帮我完善一下，确保ai能够基于这个规则执行
入参：通过方法签名和注解（如@RequestBody、@PathVariable）分析请求参数。
出参：根据方法返回类型和可能的@ResponseBody注解推断响应格式。
Service 调用：追踪 Controller 中对 Service 方法的调用，甚至可以导航到对应的 Service 实现。
Mapper 关联：如果 Service 中调用了 Mapper（如 MyBatis 的 DAO 接口），Cursor 也能识别这种跨文件的依赖。
以及中途添加新的类和方法，如：DTO、VO、Entity、service、mapper等
5、不得修改原有逻辑代码，不得影响原有代码其他的功能逻辑
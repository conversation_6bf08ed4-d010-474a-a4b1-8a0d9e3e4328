package com.seewin.som.report.provider;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.report.req.ReportProfitBusinessOverviewAddDto;
import com.seewin.som.report.req.ReportProfitBusinessOverviewEditDto;
import com.seewin.som.report.req.ReportProfitBusinessOverviewListDto;
import com.seewin.som.report.resp.ReportProfitBusinessOverviewAddVo;
import com.seewin.som.report.resp.ReportProfitBusinessOverviewGetVo;
import com.seewin.som.report.resp.ReportProfitBusinessOverviewListVo;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 盈亏报告-经营概况 API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
public interface ReportProfitBusinessOverviewProvider {

	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    PageResult<ReportProfitBusinessOverviewListVo> page(PageQuery<ReportProfitBusinessOverviewListDto> pageQuery) throws ServiceException;

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    List<ReportProfitBusinessOverviewListVo> list(ReportProfitBusinessOverviewListDto dto) throws ServiceException;

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    int count(ReportProfitBusinessOverviewListDto dto) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportProfitBusinessOverviewGetVo get(Long id) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportProfitBusinessOverviewGetVo get(ReportProfitBusinessOverviewListDto dto) throws ServiceException;


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    ReportProfitBusinessOverviewAddVo add(ReportProfitBusinessOverviewAddDto dto) throws ServiceException;


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    void edit(ReportProfitBusinessOverviewEditDto dto) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    void delete(Long id) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    void delete(ReportProfitBusinessOverviewListDto dto) throws ServiceException;

    void addBatch(List<ReportProfitBusinessOverviewAddDto> overviewAddDtoList);

    /**
     *
     * @param tenantId
     * @param entId
     * @param storeId
     * @param ai
     */
    void setAi(Long tenantId, Long entId, String storeId, Integer year, Integer month, String ai, LocalDate startDate, LocalDate endDate);
}

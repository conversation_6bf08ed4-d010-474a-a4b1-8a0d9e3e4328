package com.seewin.som.rent.service;

import com.seewin.consumer.data.ApiResponse;
import com.seewin.consumer.vo.PageResp;
import com.seewin.som.rent.vo.req.*;
import com.seewin.som.rent.vo.resp.*;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <p>
 * 收费标准关联门店表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
public interface RentChargingStandardStoreService {
    
    /**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    PageResp<RentChargingStandardStoreListItem> page(RentChargingStandardStoreListReq listReq);

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    RentChargingStandardStoreGetResp get(RentChargingStandardStoreGetReq getReq);

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    RentChargingStandardStoreAddResp add(RentChargingStandardStoreAddReq addReq);

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    void edit(RentChargingStandardStoreEditReq editReq);

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    void del(RentChargingStandardStoreDelReq delReq);

    boolean bind(RentChargingStandardStoreBindReq bindReq);

    PageResp<RentChargingStandardStoreRoomViewPageResp> roomViewPage(RentChargingStandardStoreRoomViewPageReq roomViewPageReq);

    boolean  roomViewEdit(RentChargingStandardStoreRoomViewEditReq roomViewEditReq);

    RentChargingStandardStoreRoomViewGetResp roomViewGet(RentChargingStandardStoreRoomViewGetReq roomViewGet);

    /**
     * <p>下载费用项目导入模板<br>
     */
    void downloadTemplate();

    /**
     * <p>费用项目导入<br>
     */
    void imp(RentFeeImportReq req);
}

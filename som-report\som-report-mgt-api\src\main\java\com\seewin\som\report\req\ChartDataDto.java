package com.seewin.som.report.req;

import lombok.Data;
import java.util.List;

@Data
public class ChartDataDto {
    private List<ChartDataItem> data;
    private List<String> type;
    private Integer width;
    private Integer height;
    private String keyTitle;
    private String valueTitle;
    private String countTitle;
    @Data
    public static class ChartDataItem {
        private String label;
        private List<StatItem> stats;
    }

    @Data
    public static class StatItem {
        private String key;
        private Double value;
    }
} 
package com.seewin.som.report.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 盈亏报告-收入分析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@TableName("som_report_profit_revenue_analysis")
public class ReportProfitRevenueAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 租户名称
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 房间id
     */
    @TableField("store_id")
    private String storeId;

    /**
     * 年份
     */
    @TableField("year")
    private Integer year;

    /**
     * 月份
     */
    @TableField("month")
    private Integer month;

    /**
     * 总销售额-本期数据
     */
    @TableField("current_period")
    private BigDecimal currentPeriod;

    /**
     * 总销售额-同比率
     */
    @TableField("current_period_same_ratio")
    private BigDecimal currentPeriodSameRatio;

    /**
     * 总销售额-环比率
     */
    @TableField("current_period_ring_ratio")
    private BigDecimal currentPeriodRingRatio;

    /**
     * 总销售笔数（单）-本期数据
     */
    @TableField("total_number")
    private BigDecimal totalNumber;

    /**
     * 总销售笔数（单）-同比率
     */
    @TableField("total_number_same_ratio")
    private BigDecimal totalNumberSameRatio;

    /**
     * 总销售笔数（单）-环比率
     */
    @TableField("total_number_ring_ratio")
    private BigDecimal totalNumberRingRatio;

    /**
     * 总客单价（元/单）-本期数据
     */
    @TableField("total_price")
    private BigDecimal totalPrice;

    /**
     * 总客单价（元/单）-同比率
     */
    @TableField("total_price_same_ratio")
    private BigDecimal totalPriceSameRatio;

    /**
     * 总客单价（元/单）-环比率
     */
    @TableField("total_price_ring_ratio")
    private BigDecimal totalPriceRingRatio;

    /**
     * 线上销售额（元）-本期数据
     */
    @TableField("online_volume")
    private BigDecimal onlineVolume;

    /**
     * 线上销售额（元）-同比率
     */
    @TableField("online_volume_same_ratio")
    private BigDecimal onlineVolumeSameRatio;

    /**
     * 线上销售额（元）-环比率
     */
    @TableField("online_volume_ring_ratio")
    private BigDecimal onlineVolumeRingRatio;

    /**
     * 线上销售笔数（单）-本期数据
     */
    @TableField("online_number")
    private BigDecimal onlineNumber;

    /**
     * 线上销售笔数（单）-同比率
     */
    @TableField("online_number_same_ratio")
    private BigDecimal onlineNumberSameRatio;

    /**
     * 线上销售笔数（单）-环比率
     */
    @TableField("online_number_ring_ratio")
    private BigDecimal onlineNumberRingRatio;

    /**
     * 线上客单价（元/单）-本期数据
     */
    @TableField("online_price")
    private BigDecimal onlinePrice;

    /**
     * 线上客单价（元/单）-同比率
     */
    @TableField("online_price_same_ratio")
    private BigDecimal onlinePriceSameRatio;

    /**
     * 线上客单价（元/单）-环比率
     */
    @TableField("online_price_ring_ratio")
    private BigDecimal onlinePriceRingRatio;

    /**
     * 线下销售额（元）-本期数据
     */
    @TableField("offline_volume")
    private BigDecimal offlineVolume;

    /**
     * 线下销售额（元）-同比率
     */
    @TableField("offline_volume_same_ratio")
    private BigDecimal offlineVolumeSameRatio;

    /**
     * 线下销售额（元）-环比率
     */
    @TableField("offline_volume_ring_ratio")
    private BigDecimal offlineVolumeRingRatio;

    /**
     * 线下客单价（元/单）-本期数据
     */
    @TableField("offline_price")
    private BigDecimal offlinePrice;

    /**
     * 线下客单价（元/单）-同比率
     */
    @TableField("offline_price_same_ratio")
    private BigDecimal offlinePriceSameRatio;

    /**
     * 线下客单价（元/单）-环比率
     */
    @TableField("offline_price_ring_ratio")
    private BigDecimal offlinePriceRingRatio;

    /**
     * 线下销售笔数（单）-本期数据
     */
    @TableField("store_number")
    private BigDecimal storeNumber;

    /**
     * 线下销售笔数（单）-同比率
     */
    @TableField("store_number_same_ratio")
    private BigDecimal storeNumberSameRatio;

    /**
     * 线下销售笔数（单）-环比率
     */
    @TableField("store_number_ring_ratio")
    private BigDecimal storeNumberRingRatio;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 开始时间
     */
    @TableField("start_date")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @TableField("end_date")
    private LocalDate endDate;
}

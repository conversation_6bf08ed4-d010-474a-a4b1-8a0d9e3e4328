package com.xxl.job.executor.utils;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DateUtil {

    private static final String DATE_FORMAT = "yyyy-MM-dd"; // 日期格式
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);

    private static final  DateTimeFormatter formatter2 = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    /**
     * 判断日期是否合法
     * @param dateString
     * @return
     */
    public static boolean isValidDate(String dateString) {
        List<DateTimeFormatter> formatters = Arrays.asList(
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("yyyy/MM/dd"),
                DateTimeFormatter.ofPattern("yyyy/M/d"),
                DateTimeFormatter.ofPattern("yyyyMMdd")
        );
        for (DateTimeFormatter formatter : formatters) {
            try {
                LocalDate.parse(dateString, formatter);
                return true;  // 成功解析，立即返回 true
            } catch (DateTimeParseException ignored) {
                // 忽略异常，继续尝试下一个格式
            }
        }
        return false;  // 所有格式都未能成功解析
    }

    public static boolean isEndDateAfterOrEqualStartDate(String startDateStr, String endDateStr) {
        LocalDate startDate = parseDateString(startDateStr);
        LocalDate endDate =parseDateString(endDateStr);
        if (endDate == null) {
            return false;
        }
        return !endDate.isBefore(startDate); // 结束日期大于等于开始日期

    }
    public static boolean isValidYMDFormat(String dateStr) {
        // 正则表达式匹配 yyyy/m 或 yyyy/mm 的格式
        String regex = "^\\d{4}/(0[1-9]|1[012])$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(dateStr);
        return matcher.matches();
    }
    public static LocalDate parseDateString(String dateString) {
        DateTimeFormatter[] formatters = {
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("yyyy/MM/dd"),
                DateTimeFormatter.ofPattern("yyyy/M/d"),
                DateTimeFormatter.ofPattern("yyyy/M/d")
        };

        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDate.parse(dateString, formatter);
            } catch (DateTimeParseException e) {
                // 继续尝试下一个格式
            }
        }

        return null; // 所有格式都不匹配
    }
    /**
     * 判断两个 LocalDate 对象的年份和月份是否相同
     * @param date1 第一个 LocalDate 对象
     * @param date2 第二个 LocalDate 对象
     * @return 如果年份和月份相同则返回 true，否则返回 false
     */
    public static boolean isSameYearMonth(LocalDate date1, LocalDate date2) {
        if (date1 == null || date2 == null) {
            return false; // 如果任一日期为null，则认为它们不相同
        }
        return date1.getYear() == date2.getYear() && date1.getMonth() == date2.getMonth();
    }

    /**
     * 根据传入的日期生成上周一到周日的日期范围
     * @param date 参考日期
     * @return 包含开始日期和结束日期的数组，[0]为开始日期，[1]为结束日期
     */
    public static LocalDate[] getLastWeekDateRange(LocalDate date) {
        // 获取上周一
        LocalDate lastMonday = date.minusWeeks(1).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 获取上周日
        LocalDate lastSunday = lastMonday.plusDays(6);
        
        return new LocalDate[]{lastMonday, lastSunday};
    }

    /**
     * 根据传入的日期生成上个月1号到月末的日期范围
     * @param date 参考日期
     * @return 包含开始日期和结束日期的数组，[0]为开始日期，[1]为结束日期
     */
    public static LocalDate[] getLastMonthDateRange(LocalDate date) {
        // 获取上个月的第一天
        LocalDate firstDayOfLastMonth = date.minusMonths(1).withDayOfMonth(1);
        // 获取上个月的最后一天
        LocalDate lastDayOfLastMonth = date.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        
        return new LocalDate[]{firstDayOfLastMonth, lastDayOfLastMonth};
    }

    /**
     * 获取同比日期范围（去年同期）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 返回同比日期范围数组，[0]为开始日期，[1]为结束日期
     */
    public static LocalDate[] getYearOnYearDateRange(LocalDate startDate, LocalDate endDate) {
        // 计算同比日期范围（去年同期）
        LocalDate yearOnYearStartDate = startDate.minusYears(1);
        LocalDate yearOnYearEndDate = endDate.minusYears(1);
        
        return new LocalDate[]{yearOnYearStartDate, yearOnYearEndDate};
    }

    /**
     * 获取环比日期范围
     * 如果输入日期范围是7天（一周），则返回上周的周一到周日
     * 如果输入日期范围是一个月，则返回上个月1号到月末
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 返回环比日期范围数组，[0]为开始日期，[1]为结束日期
     */
    public static LocalDate[] getMonthOnMonthDateRange(LocalDate startDate, LocalDate endDate) {

        // 计算日期范围的天数
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
        
        if (daysBetween == 7) {
            // 如果是一周，返回上周的周一到周日
            LocalDate lastWeekMonday = startDate.minusWeeks(1).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            LocalDate lastWeekSunday = lastWeekMonday.plusDays(6);
            return new LocalDate[]{lastWeekMonday, lastWeekSunday};
        } else {
            // 如果是一个月，返回上个月1号到月末
            LocalDate firstDayOfLastMonth = startDate.minusMonths(1).withDayOfMonth(1);
            LocalDate lastDayOfLastMonth = startDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
            return new LocalDate[]{firstDayOfLastMonth, lastDayOfLastMonth};
        }
    }
    public static List<LocalDate> getDateRangeList(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dateList = new java.util.ArrayList<>();
        if (startDate == null || endDate == null || endDate.isBefore(startDate)) {
            return dateList;
        }
        LocalDate current = startDate;
        while (!current.isAfter(endDate)) {
            dateList.add(current);
            current = current.plusDays(1);
        }
        return dateList;
    }
    /**
     * 判断某个日期是否在两个日期范围内（包含边界）
     * @param date 需要判断的日期
     * @param startDate 起始日期
     * @param endDate 结束日期
     * @return 如果在范围内（包含边界）返回true，否则返回false
     */
    public static boolean isDateInRange(LocalDate date, LocalDate startDate, LocalDate endDate) {
        if (date == null || startDate == null || endDate == null) {
            return false;
        }
        return !date.isBefore(startDate) && !date.isAfter(endDate);
    }
    public static void main(String[] args) {
        // 测试周环比
        LocalDate startDate = LocalDate.of(2025, 6, 16);
        LocalDate endDate = LocalDate.of(2025, 6, 22);
        System.out.println("日期范围："+getDateRangeList(startDate, endDate));
        System.out.println("周环比日期范围：");
        LocalDate[] weekDates = getMonthOnMonthDateRange(startDate, endDate);
        System.out.println("开始日期：" + weekDates[0] + "，结束日期：" + weekDates[1]);

        // 测试月环比
        LocalDate monthStartDate = LocalDate.of(2025, 6, 1);
        LocalDate monthEndDate = LocalDate.of(2025, 6, 30);
        System.out.println("\n月环比日期范围：");
        LocalDate[] monthDates = getMonthOnMonthDateRange(monthStartDate, monthEndDate);
        System.out.println("开始日期：" + monthDates[0] + "，结束日期：" + monthDates[1]);

        //测试周同比
        System.out.println("\n周同比日期范围：");
        LocalDate[] weekYearOnYearDates = getYearOnYearDateRange(startDate, endDate);
        System.out.println("开始日期：" + weekYearOnYearDates[0] + "，结束日期：" + weekYearOnYearDates[1]);

        //测试月同比
        System.out.println("\n月同比日期范围：");
        LocalDate[] monthYearOnYearDates = getYearOnYearDateRange(monthStartDate, monthEndDate);
        System.out.println("开始日期：" + monthYearOnYearDates[0] + "，结束日期：" + monthYearOnYearDates[1]);
    }
}

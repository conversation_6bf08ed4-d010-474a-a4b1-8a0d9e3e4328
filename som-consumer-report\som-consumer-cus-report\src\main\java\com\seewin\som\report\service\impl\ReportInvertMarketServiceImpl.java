package com.seewin.som.report.service.impl;


import com.seewin.som.report.provider.ReportInvertMarketProvider;

import com.seewin.som.report.req.ReportInvertMarketListDto;

import com.seewin.som.report.service.ReportInvertMarketService;
import com.seewin.som.report.vo.req.*;

import com.seewin.som.report.vo.resp.ReportInvertMarketListItem;
import com.seewin.util.bean.BeanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 招商市场报告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
public class ReportInvertMarketServiceImpl implements ReportInvertMarketService {

	/**
     * providedBy：兼容Mesh服务
     */
	@DubboReference(providedBy = "som-report-mgt")
	private ReportInvertMarketProvider reportInvertMarketProvider;
	

    @Override
    public List<ReportInvertMarketListItem> list(ReportInvertMarketListReq listReq) {
        ReportInvertMarketListDto queryDto= BeanUtils.copyProperties(listReq, ReportInvertMarketListDto.class);
        return BeanUtils.copyProperties(reportInvertMarketProvider.list(queryDto), ReportInvertMarketListItem.class);
    }
}

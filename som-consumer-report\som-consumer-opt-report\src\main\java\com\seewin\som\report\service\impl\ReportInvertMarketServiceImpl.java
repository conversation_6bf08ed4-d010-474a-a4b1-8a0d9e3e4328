package com.seewin.som.report.service.impl;

import com.seewin.som.report.req.ReportInvertMarketListDto;
import com.seewin.som.report.service.ReportInvertMarketService;
import com.seewin.som.report.vo.resp.ReportInvertMarketListItem;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import com.seewin.som.report.provider.ReportInvertMarketProvider;
import com.seewin.som.report.req.ReportInvertMarketAddDto;
import com.seewin.som.report.req.ReportInvertMarketEditDto;
import com.seewin.som.report.req.ReportInvertMarketListDto;
import com.seewin.som.report.resp.ReportInvertMarketAddVo;
import com.seewin.som.report.resp.ReportInvertMarketGetVo;
import com.seewin.som.report.resp.ReportInvertMarketListVo;

import com.seewin.som.report.vo.req.ReportInvertMarketListReq;
import com.seewin.som.report.vo.req.ReportInvertMarketGetReq;
import com.seewin.som.report.vo.req.ReportInvertMarketAddReq;
import com.seewin.som.report.vo.req.ReportInvertMarketEditReq;
import com.seewin.som.report.vo.req.ReportInvertMarketDelReq;
import com.seewin.som.report.vo.resp.ReportInvertMarketListItem;
import com.seewin.som.report.vo.resp.ReportInvertMarketGetResp;
import com.seewin.som.report.vo.resp.ReportInvertMarketAddResp;

import java.util.List;

/**
 * <p>
 * 招商市场报告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
public class ReportInvertMarketServiceImpl implements ReportInvertMarketService {

	/**
     * providedBy：兼容Mesh服务
     */
	@DubboReference(providedBy = "som-report-mgt")
	private ReportInvertMarketProvider reportInvertMarketProvider;
	
	/**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<ReportInvertMarketListItem> page(ReportInvertMarketListReq listReq) {
        PageResp<ReportInvertMarketListItem> pageResp = new PageResp<>();
		User curUser = ApiUtils.getUser(User.class);
		
        ReportInvertMarketListDto queryDto = BeanUtils.copyProperties(listReq, ReportInvertMarketListDto.class);
				queryDto.setTenantId(curUser.getTenantId());
        
        PageQuery<ReportInvertMarketListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<ReportInvertMarketListVo> pageResult = reportInvertMarketProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), ReportInvertMarketListItem.class));

        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public ReportInvertMarketGetResp get(ReportInvertMarketGetReq getReq) {
        ReportInvertMarketGetVo getVo = reportInvertMarketProvider.get(getReq.getId());

        ReportInvertMarketGetResp getResp = BeanUtils.copyProperties(getVo, ReportInvertMarketGetResp.class);

        return getResp;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public ReportInvertMarketAddResp add(ReportInvertMarketAddReq addReq) {
        ReportInvertMarketAddDto dto = BeanUtils.copyProperties(addReq, ReportInvertMarketAddDto.class);

		//设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());
       
		dto.setTenantId(curUser.getTenantId());
		
        ReportInvertMarketAddVo addVo = reportInvertMarketProvider.add(dto);

        ReportInvertMarketAddResp addResp = BeanUtils.copyProperties(addVo, ReportInvertMarketAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(ReportInvertMarketEditReq editReq) {
        ReportInvertMarketEditDto dto = BeanUtils.copyProperties(editReq, ReportInvertMarketEditDto.class);

        //设置修改人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        reportInvertMarketProvider.edit(dto);
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(ReportInvertMarketDelReq delReq) {
        reportInvertMarketProvider.delete(delReq.getId());
    }

    @Override
    public List<ReportInvertMarketListItem> list(ReportInvertMarketListReq listReq) {
        ReportInvertMarketListDto queryDto= BeanUtils.copyProperties(listReq, ReportInvertMarketListDto.class);
        return BeanUtils.copyProperties(reportInvertMarketProvider.list(queryDto), ReportInvertMarketListItem.class);
    }
}

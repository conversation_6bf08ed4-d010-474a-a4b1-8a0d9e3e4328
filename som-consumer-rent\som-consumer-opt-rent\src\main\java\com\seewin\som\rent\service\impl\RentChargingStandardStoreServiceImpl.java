package com.seewin.som.rent.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.seewin.som.commerce.provider.CommerceAdvertContractProvider;
import com.seewin.som.commerce.provider.CommerceContractProvider;
import com.seewin.som.commerce.req.CommerceAdvertContractListDto;
import com.seewin.som.commerce.req.CommerceContractInfoListDto;
import com.seewin.som.commerce.req.CommerceContractListDto;
import com.seewin.som.commerce.resp.*;
import com.seewin.som.rent.enums.StandardTypeEnum;
import com.seewin.som.rent.provider.RentChargingItemProvider;
import com.seewin.som.rent.provider.RentChargingStandardProvider;
import com.seewin.som.rent.req.*;
import com.seewin.som.rent.resp.*;
import com.seewin.som.rent.service.RentChargingStandardStoreService;
import com.seewin.som.rent.vo.req.*;
import com.seewin.som.rent.vo.resp.*;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsListVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import com.seewin.som.rent.provider.RentChargingStandardStoreProvider;
import com.seewin.som.commerce.provider.CommerceContractInfoProvider;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 收费标准关联门店表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Slf4j
@Service
public class RentChargingStandardStoreServiceImpl implements RentChargingStandardStoreService {

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingStandardStoreProvider rentChargingStandardStoreProvider;

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingStandardProvider rentChargingStandardProvider;

    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomsProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractProvider commerceContractProvider;

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractInfoProvider commerceContractInfoProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceAdvertContractProvider commerceAdvertContractProvider;

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingItemProvider rentChargingItemProvider;

    /**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<RentChargingStandardStoreListItem> page(RentChargingStandardStoreListReq listReq) {
        PageResp<RentChargingStandardStoreListItem> pageResp = new PageResp<>();
        User curUser = ApiUtils.getUser(User.class);

        RentChargingStandardStoreListDto queryDto = BeanUtils.copyProperties(listReq, RentChargingStandardStoreListDto.class);
        queryDto.setTenantId(curUser.getTenantId());


        PageQuery<RentChargingStandardStoreListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<RentChargingStandardStoreListVo> pageResult = rentChargingStandardStoreProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), RentChargingStandardStoreListItem.class));

        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public RentChargingStandardStoreGetResp get(RentChargingStandardStoreGetReq getReq) {
        RentChargingStandardStoreGetVo getVo = rentChargingStandardStoreProvider.get(getReq.getId());

        RentChargingStandardStoreGetResp getResp = BeanUtils.copyProperties(getVo, RentChargingStandardStoreGetResp.class);

        return getResp;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public RentChargingStandardStoreAddResp add(RentChargingStandardStoreAddReq addReq) {
        RentChargingStandardStoreAddDto dto = BeanUtils.copyProperties(addReq, RentChargingStandardStoreAddDto.class);

        //设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());

        dto.setTenantId(curUser.getTenantId());

        RentChargingStandardStoreAddVo addVo = rentChargingStandardStoreProvider.add(dto);

        RentChargingStandardStoreAddResp addResp = BeanUtils.copyProperties(addVo, RentChargingStandardStoreAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(RentChargingStandardStoreEditReq editReq) {
        RentChargingStandardStoreEditDto dto = BeanUtils.copyProperties(editReq, RentChargingStandardStoreEditDto.class);

        //设置修改人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        rentChargingStandardStoreProvider.edit(dto);
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(RentChargingStandardStoreDelReq delReq) {
        rentChargingStandardStoreProvider.delete(delReq.getId());
    }

    @Override
    public boolean bind(RentChargingStandardStoreBindReq bindReq) {
        //RentChargingStandardStoreBindDto dto = BeanUtils.copyProperties(bindReq, RentChargingStandardStoreBindDto.class);
        //dto.setStoreIdList(bindReq.getStoreIdList());

        //设置创建人信息
        User optUser = ApiUtils.getUser(User.class);
        // 要删除的标准关联表的id
        List<Long> removePrincipalIdList = new ArrayList<>();

        Set<Long> principalStandardIdSet = new HashSet<>();
        principalStandardIdSet.add(bindReq.getPrincipalChargingStandardId());
        Set<Long> latePaymentStandardIdSet = new HashSet<>();
        latePaymentStandardIdSet.add(bindReq.getLatePaymentChargingStandardId());

        // 如果原来的合同在同一个收费项目下绑定了其他收费标准，也需要删除
        RentChargingStandardGetVo principalChargingStandard = rentChargingStandardProvider.get(bindReq.getPrincipalChargingStandardId());
        if (principalChargingStandard != null) {
            RentChargingStandardListDto principalChargingStandardListDto = new RentChargingStandardListDto();
            // 找出同一个项目下的所有标准id
            principalChargingStandardListDto.setChargingItemId(principalChargingStandard.getChargingItemId());
            List<RentChargingStandardListVo> principalChargingStandardList = rentChargingStandardProvider.list(principalChargingStandardListDto);
            if (CollectionUtil.isNotEmpty(principalChargingStandardList)) {
                for (RentChargingStandardListVo vo : principalChargingStandardList) {
                    principalStandardIdSet.add(vo.getId());
                }
            }

        }
        RentChargingStandardGetVo latePaymentChargingStandard = rentChargingStandardProvider.get(bindReq.getLatePaymentChargingStandardId());
        if (latePaymentChargingStandard != null) {
            RentChargingStandardListDto latePaymentChargingStandardListDto = new RentChargingStandardListDto();
            // 找出同一个项目下的所有标准id
            latePaymentChargingStandardListDto.setChargingItemId(latePaymentChargingStandard.getChargingItemId());
            List<RentChargingStandardListVo> latePaymentChargingStandardList = rentChargingStandardProvider.list(latePaymentChargingStandardListDto);
            if (CollectionUtil.isNotEmpty(latePaymentChargingStandardList)) {
                for (RentChargingStandardListVo vo : latePaymentChargingStandardList) {
                    latePaymentStandardIdSet.add(vo.getId());
                }
            }
        }

        RentChargingStandardStoreListDto standardStoreListDto = new RentChargingStandardStoreListDto();
        standardStoreListDto.setChargingStandardIdSet(principalStandardIdSet);
        // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
        standardStoreListDto.setCommonContractType(bindReq.getCommonContractType());
        // 必须指定tenantId 如果仅仅根据标准ID查询会重复
        standardStoreListDto.setTenantId(optUser.getTenantId());

        // 根据本金标准id，查询老的绑定关系
        List<RentChargingStandardStoreListVo> oldList = rentChargingStandardStoreProvider.list(standardStoreListDto);

        // 如果传的合同id列表为空 则删除原有的
        if (CollectionUtil.isEmpty(bindReq.getContractCodeList())) {
            // 删除标准id关联的所有的合同
            RentChargingStandardStoreListDto listDto = new RentChargingStandardStoreListDto();
            listDto.setChargingStandardId(bindReq.getPrincipalChargingStandardId());
            // 必须指定tenantId 如果仅仅根据门店ID查询会重复
            listDto.setTenantId(optUser.getTenantId());
            // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
            listDto.setCommonContractType(bindReq.getCommonContractType());
            // 根据本金标准id，查询老的绑定关系
            List<RentChargingStandardStoreListVo> oldPrincipalList = rentChargingStandardStoreProvider.list(listDto);
            if (CollectionUtil.isNotEmpty(oldPrincipalList)) {
                for (RentChargingStandardStoreListVo old : oldPrincipalList) {
                    removePrincipalIdList.add(old.getId());
                }
            }
            // 批量删除本金绑定的
            rentChargingStandardStoreProvider.removeBatchByIds(removePrincipalIdList);
        }

        // 如果传的合同列表不为空 先删后增
        if (CollectionUtil.isNotEmpty(bindReq.getContractCodeList())) {
            // 从合同维度，一个合同只能应用一个标准  应用的集合
            Set<String> contractCodeSet = new HashSet<>(bindReq.getContractCodeList());
            if (CollectionUtil.isNotEmpty(oldList)) {
                for (RentChargingStandardStoreListVo old : oldList) {
                    if (contractCodeSet.contains(old.getContractCode())) {
                        removePrincipalIdList.add(old.getId());
                    }
                }
            }

            List<RentChargingStandardStoreAddDto> principalStandardStoreAddDtoList = new ArrayList<>();
            List<RentChargingStandardStoreAddDto> latePaymentStandardStoreAddDtoList = new ArrayList<>();


            // 批量查询合同主表
            CommerceContractListDto commerceContractListDto = new CommerceContractListDto();
            commerceContractListDto.setContractCodeList(bindReq.getContractCodeList());
            // 必须指定tenantId 如果仅仅根据门店ID查询会重复
            commerceContractListDto.setTenantId(optUser.getTenantId());
            // 从合同主表查询
            Map<String, CommerceContractListVo> contractCodeVoMap = new HashMap<>();

            // 批量查询合同信息
            CommerceContractInfoListDto commerceContractInfoListDto = new CommerceContractInfoListDto();
            commerceContractInfoListDto.setContractCodeList(bindReq.getContractCodeList());
            commerceContractInfoListDto.setTenantId(optUser.getTenantId());
            // 从合同信息查询
            Map<String, CommerceContractInfoListVo> contractCodeInfoVoMap = new HashMap<>();

            // 批量查询多经广告合同
            CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
            commerceAdvertContractListDto.setContractCodeList(bindReq.getContractCodeList());
            commerceAdvertContractListDto.setTenantId(optUser.getTenantId());
            // 从多经广告合同查询
            Map<String, CommerceAdvertContractListVo> advertContractListVoMap = new HashMap<>();

            // 如果是正铺/正铺临促合同
            if (bindReq.getCommonContractType() == 100) {

                List<CommerceContractListVo> contractVoList = commerceContractProvider.list(commerceContractListDto);
                List<CommerceContractInfoListVo> contracInfotVoList = commerceContractInfoProvider.list(commerceContractInfoListDto);

                if (CollectionUtil.isNotEmpty(contractVoList)) {
                    for (CommerceContractListVo contractVo : contractVoList) {
                        contractCodeVoMap.put(contractVo.getContractCode(), contractVo);
                    }
                }

                if (CollectionUtil.isNotEmpty(contracInfotVoList)) {
                    for (CommerceContractInfoListVo contractInfoVo : contracInfotVoList) {
                        contractCodeInfoVoMap.put(contractInfoVo.getContractCode(), contractInfoVo);
                    }
                }
            }

            // 如果是多经/广告位合同
            if (bindReq.getCommonContractType() == 200) {
                List<CommerceAdvertContractListVo> commerceAdvertContractVoList = commerceAdvertContractProvider.list(commerceAdvertContractListDto);
                if (CollectionUtil.isNotEmpty(commerceAdvertContractVoList)) {
                    for (CommerceAdvertContractListVo advertContractListVo : commerceAdvertContractVoList) {
                        advertContractListVoMap.put(advertContractListVo.getContractCode(), advertContractListVo);
                    }
                }
            }

            for (String contractCode : bindReq.getContractCodeList()) {
                LocalDateTime nowTime = LocalDateTime.now();
                // 通过门店id获取门店信息
                CommerceContractListVo contractVo = contractCodeVoMap.get(contractCode);

                CommerceContractInfoListVo contractInfoVo = contractCodeInfoVoMap.get(contractCode);

                CommerceAdvertContractListVo advertContractListVo = advertContractListVoMap.get(contractCode);

                // 本金标准绑定门店处理 （需要注意一个门店多个合同的情况，根据合同截止时间判断绑定哪个合同）
                RentChargingStandardStoreAddDto principal = new RentChargingStandardStoreAddDto();

                principal.setChargingStandardId(bindReq.getPrincipalChargingStandardId());
                principal.setCommonContractType(bindReq.getCommonContractType());
                if (contractVo != null) {
                    principal.setStoreId(contractVo.getRoomShopId());
                    principal.setEntId(contractVo.getEntId());
                    principal.setFid(null);
                    principal.setFcode(contractVo.getFcode());
                    principal.setFname(contractVo.getFname());
                    principal.setOrgFid(contractVo.getOrgFid());
                    principal.setOrgFname(contractVo.getOrgFname());
                    principal.setRoomId(contractVo.getId());
                    principal.setRoomName(contractVo.getName());
                    principal.setTenantId(contractVo.getTenantId());
                    principal.setTenantName(contractVo.getTenantName());
                    principal.setContractCode(contractVo.getContractCode());
                }
                if (contractInfoVo != null) {
                    principal.setShopType(contractInfoVo.getShopType());
                }
                if (advertContractListVo != null) {
                    principal.setAdvertContractType(advertContractListVo.getAdvertContractType());
                    principal.setAdvertName(advertContractListVo.getAdvertName());

                    principal.setEntId(advertContractListVo.getEntId());
                    principal.setOrgFid(advertContractListVo.getOrgFid());
                    principal.setOrgFname(advertContractListVo.getOrgFname());
                    principal.setTenantId(advertContractListVo.getTenantId());
                    principal.setTenantName(advertContractListVo.getTenantName());
                    principal.setContractCode(advertContractListVo.getContractCode());
                }
                principal.setApplyPaymentTermSrart(bindReq.getApplyPaymentTermSrart());
                principal.setApplyPaymentTermEnd(bindReq.getApplyPaymentTermEnd());

                principal.setCreateTime(nowTime);
                principal.setCreateBy(optUser.getUserId());
                principal.setCreateUser(optUser.getUserName());
                principal.setCreateUserName(optUser.getRealName());

                principalStandardStoreAddDtoList.add(principal);
                //standardStoreAddDtoList.add(latePayment);

            }
            // 合同维度 本金批量删除 先删后增
            rentChargingStandardStoreProvider.removeBatchByIds(removePrincipalIdList);
            // 合同维度 本金批量保存
            rentChargingStandardStoreProvider.saveBatch(principalStandardStoreAddDtoList);

            // 滞纳金  合同维度 对滞纳金先删后增
            RentChargingStandardStoreListDto standardStoreListDto2 = new RentChargingStandardStoreListDto();
            standardStoreListDto2.setContractCodeList(bindReq.getContractCodeList());
            // 必须指定tenantId 如果仅仅根据门店ID查询会重复
            standardStoreListDto2.setTenantId(optUser.getTenantId());
            // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
            standardStoreListDto2.setCommonContractType(bindReq.getCommonContractType());

            // 根据本金标准id，查询老的绑定关系
            List<RentChargingStandardStoreListVo> oldBindContractViewList = rentChargingStandardStoreProvider.list(standardStoreListDto2);
            List<Long> standardIdList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(oldBindContractViewList)) {
                for (RentChargingStandardStoreListVo oldBindContractView : oldBindContractViewList) {
                    standardIdList.add(oldBindContractView.getChargingStandardId());
                }
            }
            Map<Long, RentChargingStandardListVo> standardIdVoMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(standardIdList)) {
                RentChargingStandardListDto standardListDto = new RentChargingStandardListDto();
                standardListDto.setIdList(standardIdList);
                List<RentChargingStandardListVo> standardList = rentChargingStandardProvider.list(standardListDto);
                if (CollectionUtil.isNotEmpty(standardList)) {
                    for (RentChargingStandardListVo standard : standardList) {
                        standardIdVoMap.put(standard.getId(), standard);
                    }
                }
            }
            // 要删除的合同视角的绑定的滞纳金的id
            List<Long> removeLatePaymentIdList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(oldBindContractViewList)) {
                for (RentChargingStandardStoreListVo oldBindContractView : oldBindContractViewList) {
                    RentChargingStandardListVo standard = standardIdVoMap.get(oldBindContractView.getChargingStandardId());
                    if (standard != null && StandardTypeEnum.LatePayment.getCode().equals(standard.getType())) {
                        removeLatePaymentIdList.add(oldBindContractView.getId());
                    }
                }
                // 滞纳金批量删除 先删后增
                rentChargingStandardStoreProvider.removeBatchByIds(removeLatePaymentIdList);
            }
            for (String contractCode : bindReq.getContractCodeList()) {
                LocalDateTime nowTime = LocalDateTime.now();
                // 通过门店id获取门店信息
                CommerceContractListVo contractVo = contractCodeVoMap.get(contractCode);

                CommerceContractInfoListVo contractInfoVo = contractCodeInfoVoMap.get(contractCode);

                CommerceAdvertContractListVo advertContractListVo = advertContractListVoMap.get(contractCode);

                // 本金标准绑定门店处理 （需要注意一个门店多个合同的情况，根据合同截止时间判断绑定哪个合同）
                RentChargingStandardStoreAddDto latePayment = new RentChargingStandardStoreAddDto();

                latePayment.setChargingStandardId(bindReq.getLatePaymentChargingStandardId());
                latePayment.setCommonContractType(bindReq.getCommonContractType());
                if (contractVo != null) {
                    latePayment.setStoreId(contractVo.getRoomShopId());
                    latePayment.setEntId(contractVo.getEntId());
                    latePayment.setFid(null);
                    latePayment.setFcode(contractVo.getFcode());
                    latePayment.setFname(contractVo.getFname());
                    latePayment.setOrgFid(contractVo.getOrgFid());
                    latePayment.setOrgFname(contractVo.getOrgFname());
                    latePayment.setRoomId(contractVo.getId());
                    latePayment.setRoomName(contractVo.getName());
                    latePayment.setTenantId(contractVo.getTenantId());
                    latePayment.setTenantName(contractVo.getTenantName());
                    latePayment.setContractCode(contractVo.getContractCode());
                }
                if (contractInfoVo != null) {
                    latePayment.setShopType(contractInfoVo.getShopType());
                }
                if (advertContractListVo != null) {
                    latePayment.setAdvertContractType(advertContractListVo.getAdvertContractType());
                    latePayment.setAdvertName(advertContractListVo.getAdvertName());

                    latePayment.setEntId(advertContractListVo.getEntId());
                    latePayment.setOrgFid(advertContractListVo.getOrgFid());
                    latePayment.setOrgFname(advertContractListVo.getOrgFname());
                    latePayment.setTenantId(advertContractListVo.getTenantId());
                    latePayment.setTenantName(advertContractListVo.getTenantName());
                    latePayment.setContractCode(advertContractListVo.getContractCode());
                }
                latePayment.setApplyPaymentTermSrart(bindReq.getApplyPaymentTermSrart());
                latePayment.setApplyPaymentTermEnd(bindReq.getApplyPaymentTermEnd());

                latePayment.setCreateTime(nowTime);
                latePayment.setCreateBy(optUser.getUserId());
                latePayment.setCreateUser(optUser.getUserName());
                latePayment.setCreateUserName(optUser.getRealName());

                latePaymentStandardStoreAddDtoList.add(latePayment);
            }
            // 滞纳金批量保存
            rentChargingStandardStoreProvider.saveBatch(latePaymentStandardStoreAddDtoList);
        }
        return true;

    }

    @Override
    public PageResp<RentChargingStandardStoreRoomViewPageResp> roomViewPage(RentChargingStandardStoreRoomViewPageReq roomViewPageReq) {
        PageResp<RentChargingStandardStoreRoomViewPageResp> pageResp = new PageResp<>();
        User curUser = ApiUtils.getUser(User.class);

        // 搜索条件得到的合同号
        List<List<String>> inputLists = new ArrayList<>();
        Map<Long, RentChargingItemListVo> itemIdItemMap = new HashMap<>();
        Map<Long, RentChargingStandardListVo> standardIdIStandardMap = new HashMap<>();
        Map<String, CommerceContractInfoListVo> contractCodeInfoMap = new HashMap<>();
        Map<String, CommerceAdvertContractListVo> advertContractListVoMap = new HashMap<>();

        // 搜索条件1  搜索标准关联表
        RentChargingStandardStoreListDto standardStoreQueryDto = new RentChargingStandardStoreListDto();
        standardStoreQueryDto.setTenantId(curUser.getTenantId());

        if (CollectionUtil.isNotEmpty(roomViewPageReq.getRoomNameList())) {
            standardStoreQueryDto.setRoomNameList(roomViewPageReq.getRoomNameList());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getStoreIdLike())) {
            standardStoreQueryDto.setStoreIdLike(roomViewPageReq.getStoreIdLike());
        }
        if (roomViewPageReq.getMergeType() != null) {
            // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
            if (roomViewPageReq.getMergeType() == 0) {
                // 商铺类型：0：正铺；1：临促
                standardStoreQueryDto.setShopType(0);
            }
            if (roomViewPageReq.getMergeType() == 1) {
                // 商铺类型：0：正铺；1：临促
                standardStoreQueryDto.setShopType(1);
            }
            if (roomViewPageReq.getMergeType() == 2) {
                // 细分合同类型（0-广告位、1-多经点位）
                standardStoreQueryDto.setAdvertContractType(0);
            }
            if (roomViewPageReq.getMergeType() == 3) {
                // 细分合同类型（0-广告位、1-多经点位）
                standardStoreQueryDto.setAdvertContractType(1);
            }
        }
        // 广告多经场地编号
        if (StringUtils.isNotEmpty(roomViewPageReq.getAdvertName())) {
            standardStoreQueryDto.setAdvertName(roomViewPageReq.getAdvertName());
        }

        RentChargingItemListDto dto = new RentChargingItemListDto();
        // 只返回本金的
        dto.setType(StandardTypeEnum.Principal.getCode());
        if (StringUtils.isNotEmpty(roomViewPageReq.getPrincipalChargingItemNameLike())) {
            dto.setFeeNameLike(roomViewPageReq.getPrincipalChargingItemNameLike());
        }
        List<RentChargingItemListVo> rentChargingItemListVoList = rentChargingItemProvider.list(dto);

        if (CollectionUtil.isEmpty(rentChargingItemListVoList)) {
            pageResp.setPageNum(roomViewPageReq.getPageNum());
            pageResp.setPageSize(roomViewPageReq.getPageSize());
            pageResp.setPages(0);
            pageResp.setTotal(0);
            List<RentChargingStandardStoreRoomViewPageResp> retItems = new ArrayList<>();
            pageResp.setItems(retItems);
            return pageResp;
        }

        RentChargingStandardListDto standardDto = new RentChargingStandardListDto();
        // 只返回本金的
        standardDto.setType(StandardTypeEnum.Principal.getCode());
        if (CollectionUtil.isNotEmpty(rentChargingItemListVoList)) {
            List<Long> chargingItemIdList = new ArrayList<>();
            for (RentChargingItemListVo rentChargingItemListVo : rentChargingItemListVoList) {
                itemIdItemMap.put(rentChargingItemListVo.getId(), rentChargingItemListVo);
                chargingItemIdList.add(rentChargingItemListVo.getId());
            }
            standardDto.setChargingItemIdList(chargingItemIdList);
        }
        List<RentChargingStandardListVo> rentChargingStandardListVoList = rentChargingStandardProvider.list(standardDto);
        if (CollectionUtil.isNotEmpty(rentChargingStandardListVoList)) {
            Set<Long> chargingStandardIdSet = new HashSet<>();
            for (RentChargingStandardListVo rentChargingStandardListVo : rentChargingStandardListVoList) {
                Long standardId = rentChargingStandardListVo.getId();
                if (StringUtils.isNotEmpty(rentChargingStandardListVo.getName())) {
                    standardIdIStandardMap.put(standardId, rentChargingStandardListVo);
                }
                chargingStandardIdSet.add(rentChargingStandardListVo.getId());
            }
            standardStoreQueryDto.setChargingStandardIdSet(chargingStandardIdSet);
        }

        List<RentChargingStandardStoreListVo> standardStoreListVoList = rentChargingStandardStoreProvider.list(standardStoreQueryDto);
        List<String> contractCodeList1 = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(standardStoreListVoList)) {
            for (RentChargingStandardStoreListVo rentChargingStandardStoreListVo : standardStoreListVoList) {
                if (StringUtils.isNotEmpty(rentChargingStandardStoreListVo.getContractCode())) {
                    contractCodeList1.add(rentChargingStandardStoreListVo.getContractCode());
                }
            }
            // 去重
            HashSet<String> set1 = new HashSet<>(contractCodeList1);
            List<String> list1 = new ArrayList<>(set1);
            inputLists.add(list1);
        }

        // 搜索条件2  搜索合同信息表 为了得到合同编号
        CommerceContractInfoListDto infoQueryDto = new CommerceContractInfoListDto();
        infoQueryDto.setTenantId(curUser.getTenantId());
        if (StringUtils.isNotEmpty(roomViewPageReq.getBrandNameLike())) {
            infoQueryDto.setBrandNameLike(roomViewPageReq.getBrandNameLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getSupplierNameLike())) {
            infoQueryDto.setSupplierNameLike(roomViewPageReq.getSupplierNameLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getContractCodeLike())) {
            infoQueryDto.setContractCodeLike(roomViewPageReq.getContractCodeLike());
        }
        if (roomViewPageReq.getMergeType() != null) {
            // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
            if (roomViewPageReq.getMergeType() == 0) {
                infoQueryDto.setShopType(0);
            }
            if (roomViewPageReq.getMergeType() == 1) {
                infoQueryDto.setShopType(1);
            }
        }
        List<CommerceContractInfoListVo> infoVoList = commerceContractInfoProvider.list(infoQueryDto);
        List<String> contractCodeList2 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(infoVoList)) {

            for (CommerceContractInfoListVo commerceContractInfoListVo : infoVoList) {
                if (StringUtils.isNotEmpty(commerceContractInfoListVo.getContractCode())) {
                    contractCodeList2.add(commerceContractInfoListVo.getContractCode());
                    contractCodeInfoMap.put(commerceContractInfoListVo.getContractCode(), commerceContractInfoListVo);
                }
            }
            // 去重
            HashSet<String> set2 = new HashSet<>(contractCodeList2);
            list2 = new ArrayList<>(set2);
            //inputLists.add(list2);
        }

        // 搜索条件3  搜索多经广告合同表 为了得到合同编号
        // 批量查询多经广告合同
        CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
        commerceAdvertContractListDto.setTenantId(curUser.getTenantId());
        if (StringUtils.isNotEmpty(roomViewPageReq.getBrandNameLike())) {
            commerceAdvertContractListDto.setBrandNameLike(roomViewPageReq.getBrandNameLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getSupplierNameLike())) {
            commerceAdvertContractListDto.setSupplierNameLike(roomViewPageReq.getSupplierNameLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getContractCodeLike())) {
            commerceAdvertContractListDto.setContractCodeLike(roomViewPageReq.getContractCodeLike());
        }
        if (StringUtils.isNotEmpty(roomViewPageReq.getAdvertName())) {
            commerceAdvertContractListDto.setAdvertName(roomViewPageReq.getAdvertName());
        }
        if (roomViewPageReq.getMergeType() != null) {
            // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
            if (roomViewPageReq.getMergeType() == 2) {
                // 细分合同类型（0-广告位、1-多经点位）
                commerceAdvertContractListDto.setAdvertContractType(0);
            }
            if (roomViewPageReq.getMergeType() == 3) {
                // 细分合同类型（0-广告位、1-多经点位）
                commerceAdvertContractListDto.setAdvertContractType(1);
            }
        }
        List<CommerceAdvertContractListVo> commerceAdvertContractVoList = commerceAdvertContractProvider.list(commerceAdvertContractListDto);
        List<String> contractCodeList3 = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(commerceAdvertContractVoList)) {

            for (CommerceAdvertContractListVo commerceAdvertContractListVo : commerceAdvertContractVoList) {
                if (StringUtils.isNotEmpty(commerceAdvertContractListVo.getContractCode())) {
                    contractCodeList3.add(commerceAdvertContractListVo.getContractCode());
                    advertContractListVoMap.put(commerceAdvertContractListVo.getContractCode(), commerceAdvertContractListVo);
                }
            }
            // 去重
            HashSet<String> set3 = new HashSet<>(contractCodeList3);
            List<String> list3 = new ArrayList<>(set3);
            if (CollectionUtil.isNotEmpty(list2)) {
                // list3和list2合并
                list3.addAll(list2);
            }
            inputLists.add(list3);
        }

        // 如果inputLists.size()为1，且list2不为空，则添加list2
        if (inputLists.size() == 1 && CollectionUtil.isNotEmpty(list2)) {
            inputLists.add(list2);
        }

        // 满足选择条件交集的合同编号
        log.info("inputLists = {}", JSON.toJSON(inputLists));
        List<String> selectContractCodeList = new ArrayList<>();
        // 没有符合条件的，返回
        if (inputLists.size() == 0 || inputLists.size() == 1) {
            pageResp.setPageNum(roomViewPageReq.getPageNum());
            pageResp.setPageSize(roomViewPageReq.getPageSize());
            pageResp.setPages(0);
            pageResp.setTotal(0);
            List<RentChargingStandardStoreRoomViewPageResp> retItems = new ArrayList<>();
            pageResp.setItems(retItems);
            return pageResp;
        }
        if (inputLists.size() > 1) {
            // 使用Stream API求交集
            selectContractCodeList = (inputLists.get(0).stream()
                    .filter(inputLists.get(1)::contains)
                    .collect(Collectors.toList()));
        }

        log.info("selectContractCodeList = {}", JSON.toJSON(selectContractCodeList));

        // 根据合同号列表去查询标准关联表
        RentChargingStandardStoreListDto standardStoreEffectiveQueryDto = new RentChargingStandardStoreListDto();
        standardStoreEffectiveQueryDto.setTenantId(curUser.getTenantId());

        standardStoreEffectiveQueryDto.setContractCodeList(selectContractCodeList);
        // 过滤本金标准id 重要
        List<RentChargingStandardStoreListVo> toFilterList = rentChargingStandardStoreProvider.list(standardStoreEffectiveQueryDto);
        if (CollectionUtil.isNotEmpty(toFilterList)) {
            Set<Long> principalStandardIdList = new HashSet<>();
            for (RentChargingStandardStoreListVo rentChargingStandardStoreListVo : toFilterList) {
                Long standardId = rentChargingStandardStoreListVo.getChargingStandardId();
                RentChargingStandardListVo rentChargingStandardListVo = standardIdIStandardMap.get(standardId);
                if (rentChargingStandardListVo != null) {
                    // 只有本金的才添加
                    if (StandardTypeEnum.Principal.getCode().equals(rentChargingStandardListVo.getType())) {
                        principalStandardIdList.add(standardId);
                    }
                }
            }
            // 限制只有本金的标准id才查询
            standardStoreEffectiveQueryDto.setChargingStandardIdSet(principalStandardIdList);
        }

        PageQuery<RentChargingStandardStoreListDto> pageQuery = new PageQuery<>(roomViewPageReq.getPageNum(), roomViewPageReq.getPageSize(), standardStoreEffectiveQueryDto);
        PageResult<RentChargingStandardStoreListVo> pageResult = rentChargingStandardStoreProvider.page(pageQuery);
        log.info("pageResult = {}", JSON.toJSON(pageResult));

        pageResp.setPageNum(roomViewPageReq.getPageNum());
        pageResp.setPageSize(roomViewPageReq.getPageSize());

        // 组装返回items
        List<RentChargingStandardStoreRoomViewPageResp> retItems = new ArrayList<>();
        if (pageResult != null) {

            pageResp.setPages(pageResult.getPages());
            pageResp.setTotal(pageResult.getTotal());

            List<RentChargingStandardStoreListVo> standardStores = pageResult.getItems();
            if (CollectionUtil.isNotEmpty(standardStores)) {
                for (RentChargingStandardStoreListVo standardStore : standardStores) {
                    RentChargingStandardStoreRoomViewPageResp resp = new RentChargingStandardStoreRoomViewPageResp();
                    resp = BeanUtils.copyProperties(standardStore, RentChargingStandardStoreRoomViewPageResp.class);


                    Long standardId = standardStore.getChargingStandardId();
                    if (standardId != null) {
                        resp.setChargingStandardId(standardId);
                        RentChargingStandardListVo standard = standardIdIStandardMap.get(standardId);


                        if (standard != null) {
                            Long itemId = standard.getChargingItemId();
                            if (itemId != null) {
                                resp.setChargingItemId(itemId);
                                // 收费项目
                                RentChargingItemListVo rentChargingItemListVo = itemIdItemMap.get(itemId);
                                if (rentChargingItemListVo != null && StringUtils.isNotEmpty(rentChargingItemListVo.getFeeName())) {
                                    resp.setChargingItemName(rentChargingItemListVo.getFeeName());
                                }
                            }


                            if (StringUtils.isNotEmpty(standard.getName())) {
                                resp.setChargingStandardName(standard.getName());
                            }
                            if (StringUtils.isNotEmpty(standard.getExpressionDesc())) {
                                resp.setExpressionDesc(standard.getExpressionDesc());
                            }
                        }
                    }

                    String contractCode = standardStore.getContractCode();
                    if (StringUtils.isNotEmpty(contractCode)) {
                        CommerceContractInfoListVo infoVo = contractCodeInfoMap.get(contractCode);
                        if (infoVo != null) {
                            if (infoVo.getBrandId() != null) {
                                resp.setBrandId(infoVo.getBrandId());
                            }
                            if (StringUtils.isNotEmpty(infoVo.getBrandName())) {
                                resp.setBrandName(infoVo.getBrandName());
                            }
                            if (infoVo.getSupplierId() != null) {
                                resp.setSupplierId(infoVo.getSupplierId());
                            }
                            if (StringUtils.isNotEmpty(infoVo.getSupplierName())) {
                                resp.setSupplierName(infoVo.getSupplierName());
                            }
                            if (infoVo.getRentStartDate() != null) {
                                resp.setRentStartDate(infoVo.getRentStartDate());
                            }
                            if (infoVo.getRentEndDate() != null) {
                                resp.setRentEndDate(infoVo.getRentEndDate());
                            }
                            if (infoVo.getShopType() != null) {
                                // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
                                resp.setMergeType(infoVo.getShopType());
                            }
                        }
                        CommerceAdvertContractListVo advertContractVo = advertContractListVoMap.get(contractCode);
                        if (advertContractVo != null) {
                            if (advertContractVo.getBrandId() != null) {
                                resp.setBrandId(advertContractVo.getBrandId());
                            }
                            if (StringUtils.isNotEmpty(advertContractVo.getBrandName())) {
                                resp.setBrandName(advertContractVo.getBrandName());
                            }
                            if (advertContractVo.getSupplierId() != null) {
                                resp.setSupplierId(advertContractVo.getSupplierId());
                            }
                            if (StringUtils.isNotEmpty(advertContractVo.getSupplierName())) {
                                resp.setSupplierName(advertContractVo.getSupplierName());
                            }
                            if (advertContractVo.getRentStartDate() != null) {
                                resp.setRentStartDate(advertContractVo.getRentStartDate());
                            }
                            if (advertContractVo.getRentEndDate() != null) {
                                resp.setRentEndDate(advertContractVo.getRentEndDate());
                            }
                            // 多经/广告的场地编号
                            if (advertContractVo.getAdvertName() != null) {
                                resp.setAdvertName(advertContractVo.getAdvertName());
                            }
                            if (advertContractVo.getAdvertContractType() != null) {
                                // 合同类型（0-广告位、1-多经点位）
                                if (advertContractVo.getAdvertContractType() == 0) {
                                    // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
                                    resp.setMergeType(2);
                                }
                                // 合同类型（0-广告位、1-多经点位）
                                if (advertContractVo.getAdvertContractType() == 1) {
                                    // 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
                                    resp.setMergeType(3);
                                }
                            }
                        }
                    }
                    retItems.add(resp);
                }
            }

        }
        pageResp.setItems(retItems);
        log.info("pageResp = {}", JSON.toJSON(pageResp));

        return pageResp;

    }

    @Override
    public boolean roomViewEdit(RentChargingStandardStoreRoomViewEditReq roomViewEditReq) {

        //设置创建人信息
        User optUser = ApiUtils.getUser(User.class);
        LocalDateTime nowTime = LocalDateTime.now();

        // 获取合同主表和合同信息
        String contractCode = roomViewEditReq.getContractCode();
        CommerceContractGetVo contractVo = null;
        CommerceContractInfoGetVo contractInfoGetVo = null;
        // 如果不是多经也不是广告合同，即正铺或者正铺临促
        if (!contractCode.startsWith("D") && !contractCode.startsWith("G")) {
            CommerceContractListDto commerceContractListDto = new CommerceContractListDto();
            commerceContractListDto.setContractCode(contractCode);
            contractVo = commerceContractProvider.get(commerceContractListDto);

            contractInfoGetVo = commerceContractInfoProvider.getByContractCode(contractCode);

        }
        // CommerceAdvertContractGetVo get(CommerceAdvertContractListDto dto)
        CommerceAdvertContractGetVo advertContractGetVo = null;
        // 如果是多经或者广告合同
        if (contractCode.startsWith("D") || contractCode.startsWith("G")) {
            CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
            commerceAdvertContractListDto.setContractCode(contractCode);
            advertContractGetVo = commerceAdvertContractProvider.get(commerceAdvertContractListDto);
        }
        // 根据合同号先删后增
        RentChargingStandardStoreListDto standardStoreListDto = new RentChargingStandardStoreListDto();
        standardStoreListDto.setContractCode(contractCode);
        // 必须指定tenantId 如果仅仅根据门店ID查询会重复
        standardStoreListDto.setTenantId(optUser.getTenantId());

        // 根据本金标准id和滞纳金标准id，查询老的绑定关系
        List<RentChargingStandardStoreListVo> oldList = rentChargingStandardStoreProvider.list(standardStoreListDto);
        List<Long> removeIdList = new ArrayList<>();
        // 过滤出要绑定的门店
        if (CollectionUtil.isNotEmpty(oldList)) {
            for (RentChargingStandardStoreListVo old : oldList) {
                removeIdList.add(old.getId());
            }
        }
        // 批量删除 先删后增
        rentChargingStandardStoreProvider.removeBatchByIds(removeIdList);

        List<RentChargingStandardStoreAddDto> standardStoreAddDtoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(roomViewEditReq.getPrincipalChargingStandardDetailList())) {
            for (RentChargingStandardStoreRoomViewEditDetailReq detailReq : roomViewEditReq.getPrincipalChargingStandardDetailList()) {
                // 本金
                RentChargingStandardStoreAddDto principal = new RentChargingStandardStoreAddDto();
                principal.setChargingStandardId(detailReq.getPrincipalChargingStandardId());
                principal.setApplyPaymentTermSrart(detailReq.getApplyPaymentTermSrart());
                principal.setApplyPaymentTermEnd(detailReq.getApplyPaymentTermEnd());
                principal.setContractCode(contractCode);

                if (contractVo != null) {
                    principal.setStoreId(contractVo.getRoomShopId());
                    principal.setEntId(contractVo.getEntId());
                    principal.setFid(contractVo.getOrgFid());
                    principal.setFcode(contractVo.getFcode());
                    principal.setFname(contractVo.getFname());
                    principal.setOrgFid(contractVo.getOrgFid());
                    principal.setOrgFname(contractVo.getOrgFname());
                    principal.setRoomId(contractVo.getRoomId());
                    principal.setRoomName(contractVo.getName());
                    principal.setTenantId(contractVo.getTenantId());
                    principal.setTenantName(contractVo.getTenantName());
                    principal.setContractCode(contractVo.getContractCode());
                    // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
                    principal.setCommonContractType(100);
                }
                if (contractInfoGetVo != null) {
                    principal.setShopType(contractInfoGetVo.getShopType());
                }

                if (advertContractGetVo != null) {
                    principal.setEntId(advertContractGetVo.getEntId());
                    principal.setFid(advertContractGetVo.getOrgFid());
                    principal.setOrgFid(advertContractGetVo.getOrgFid());
                    principal.setOrgFname(advertContractGetVo.getOrgFname());
                    principal.setTenantId(advertContractGetVo.getTenantId());
                    principal.setTenantName(advertContractGetVo.getTenantName());
                    principal.setContractCode(advertContractGetVo.getContractCode());
                    // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
                    principal.setCommonContractType(200);
                    principal.setAdvertName(advertContractGetVo.getAdvertName());
                    principal.setAdvertContractType(advertContractGetVo.getAdvertContractType());
                }

                principal.setCreateTime(nowTime);
                principal.setCreateBy(optUser.getUserId());
                principal.setCreateUser(optUser.getUserName());
                principal.setCreateUserName(optUser.getRealName());
                standardStoreAddDtoList.add(principal);
            }
        }
        // 滞纳金 一个合同一个滞纳金标准
        if (roomViewEditReq.getLatePaymentChargingStandardId() != null) {
            // 滞纳金
            RentChargingStandardStoreAddDto latePayment = new RentChargingStandardStoreAddDto();
            latePayment.setChargingStandardId(roomViewEditReq.getLatePaymentChargingStandardId());
            latePayment.setContractCode(contractCode);
            if (contractVo != null) {
                latePayment.setStoreId(contractVo.getRoomShopId());
                latePayment.setEntId(contractVo.getEntId());
                latePayment.setFid(contractVo.getOrgFid());
                latePayment.setFcode(contractVo.getFcode());
                latePayment.setFname(contractVo.getFname());
                latePayment.setOrgFid(contractVo.getOrgFid());
                latePayment.setOrgFname(contractVo.getOrgFname());
                latePayment.setRoomId(contractVo.getRoomId());
                latePayment.setRoomName(contractVo.getName());
                latePayment.setTenantId(contractVo.getTenantId());
                latePayment.setTenantName(contractVo.getTenantName());
                latePayment.setContractCode(contractVo.getContractCode());
                // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
                latePayment.setCommonContractType(100);
            }

            if (contractInfoGetVo != null) {
                latePayment.setShopType(contractInfoGetVo.getShopType());
            }

            if (advertContractGetVo != null) {
                latePayment.setEntId(advertContractGetVo.getEntId());
                latePayment.setFid(advertContractGetVo.getOrgFid());
                latePayment.setOrgFid(advertContractGetVo.getOrgFid());
                latePayment.setOrgFname(advertContractGetVo.getOrgFname());
                latePayment.setTenantId(advertContractGetVo.getTenantId());
                latePayment.setTenantName(advertContractGetVo.getTenantName());
                latePayment.setContractCode(advertContractGetVo.getContractCode());
                // 通用大类合同类型（100-正铺/正铺临促、200-多经/广告位）
                latePayment.setCommonContractType(200);
                latePayment.setAdvertName(advertContractGetVo.getAdvertName());
                latePayment.setAdvertContractType(advertContractGetVo.getAdvertContractType());
            }

            // 滞纳金的应用账期默认2000.1.1-2099.1.1 不受界面应用账期限制
            LocalDate latePaymentTermSrart = LocalDate.of(2000, 1, 1);
            LocalDate latePaymentTermEnd = LocalDate.of(2099, 1, 1);

            latePayment.setApplyPaymentTermSrart(latePaymentTermSrart);
            latePayment.setApplyPaymentTermEnd(latePaymentTermEnd);

            latePayment.setCreateTime(nowTime);
            latePayment.setCreateBy(optUser.getUserId());
            latePayment.setCreateUser(optUser.getUserName());
            latePayment.setCreateUserName(optUser.getRealName());

            standardStoreAddDtoList.add(latePayment);
        }

        // 批量保存
        return rentChargingStandardStoreProvider.saveBatch(standardStoreAddDtoList);

    }

    @Override
    public RentChargingStandardStoreRoomViewGetResp roomViewGet(RentChargingStandardStoreRoomViewGetReq roomViewGet) {
        //设置创建人信息
        User optUser = ApiUtils.getUser(User.class);

        // 获取合同主表和合同信息
        String contractCode = roomViewGet.getContractCode();


        CommerceContractGetVo contractVo = null;
        CommerceContractInfoGetVo infoVo = null;
        // 如果不是多经也不是广告合同，即正铺或者正铺临促
        if (!contractCode.startsWith("D") && !contractCode.startsWith("G")) {
            CommerceContractListDto commerceContractListDto = new CommerceContractListDto();
            commerceContractListDto.setContractCode(contractCode);
            contractVo = commerceContractProvider.get(commerceContractListDto);
            infoVo = commerceContractInfoProvider.getByContractCode(contractCode);
        }
        // CommerceAdvertContractGetVo get(CommerceAdvertContractListDto dto)
        CommerceAdvertContractGetVo advertContractGetVo = null;
        // 如果是多经或者广告合同
        if (contractCode.startsWith("D") || contractCode.startsWith("G")) {
            CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
            commerceAdvertContractListDto.setContractCode(contractCode);
            advertContractGetVo = commerceAdvertContractProvider.get(commerceAdvertContractListDto);
        }

        RentChargingStandardStoreRoomViewGetResp resp = new RentChargingStandardStoreRoomViewGetResp();

        resp.setContractCode(contractCode);
        if (contractVo != null) {
            resp.setStoreId(contractVo.getRoomShopId());
            resp.setEntId(contractVo.getEntId());
            resp.setFid(contractVo.getOrgFid());
            resp.setFcode(contractVo.getFcode());
            resp.setFname(contractVo.getFname());
            resp.setOrgFid(contractVo.getOrgFid());
            resp.setOrgFname(contractVo.getOrgFname());
            resp.setRoomId(contractVo.getRoomId());
            resp.setRoomName(contractVo.getName());
            resp.setTenantName(contractVo.getTenantName());
        }
        if (infoVo != null) {
            resp.setBrandId(infoVo.getBrandId());
            resp.setBrandName(infoVo.getBrandName());
            resp.setSupplierId(infoVo.getSupplierId());
            resp.setSupplierName(infoVo.getSupplierName());
            resp.setRentStartDate(infoVo.getRentStartDate());
            resp.setRentEndDate(infoVo.getRentEndDate());
        }
        if (advertContractGetVo != null) {
            resp.setEntId(advertContractGetVo.getEntId());
            resp.setFid(advertContractGetVo.getOrgFid());
            resp.setOrgFid(advertContractGetVo.getOrgFid());
            resp.setOrgFname(advertContractGetVo.getOrgFname());
            resp.setTenantName(advertContractGetVo.getTenantName());
            resp.setBrandId(advertContractGetVo.getBrandId());
            resp.setBrandName(advertContractGetVo.getBrandName());
            resp.setSupplierId(advertContractGetVo.getSupplierId());
            resp.setSupplierName(advertContractGetVo.getSupplierName());
            resp.setRentStartDate(advertContractGetVo.getRentStartDate());
            resp.setRentEndDate(advertContractGetVo.getRentEndDate());
        }
        // 收费项目-收费标准信息
        List<RentChargingStandardStoreRoomViewGetItem> items = new ArrayList<>();
        // 根据合同号先删后增
        RentChargingStandardStoreListDto standardStoreListDto = new RentChargingStandardStoreListDto();
        standardStoreListDto.setContractCode(contractCode);
        // 必须指定tenantId 如果仅仅根据门店ID查询会重复
        standardStoreListDto.setTenantId(optUser.getTenantId());

        // 查询老的绑定关系
        List<RentChargingStandardStoreListVo> standardStoreList = rentChargingStandardStoreProvider.list(standardStoreListDto);
        List<Long> standardIdList = new ArrayList<>();
        Map<Long, RentChargingStandardStoreListVo> standardIdStandardStoreVoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(standardStoreList)) {
            for (RentChargingStandardStoreListVo standardStore : standardStoreList) {
                standardIdList.add(standardStore.getChargingStandardId());
                standardIdStandardStoreVoMap.put(standardStore.getChargingStandardId(), standardStore);
            }
        }

        // 标准
        RentChargingStandardListDto rentChargingStandardListDto = new RentChargingStandardListDto();
        rentChargingStandardListDto.setIdList(standardIdList);
        List<RentChargingStandardListVo> standardList = rentChargingStandardProvider.list(rentChargingStandardListDto);
        List<Long> chargingItemIdList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(standardList)) {
            for (RentChargingStandardListVo vo : standardList) {
                chargingItemIdList.add(vo.getChargingItemId());
            }
        }
        // 项目
        RentChargingItemListDto rentChargingItemListDto = new RentChargingItemListDto();
        rentChargingItemListDto.setIdList(chargingItemIdList);
        List<RentChargingItemListVo> chargingItemList = rentChargingItemProvider.list(rentChargingItemListDto);
        Map<Long, RentChargingItemListVo> chargingItemIdVoMap = new HashMap<>();

        if (CollectionUtil.isNotEmpty(chargingItemList)) {
            for (RentChargingItemListVo itemVo : chargingItemList) {
                chargingItemIdVoMap.put(itemVo.getId(), itemVo);
            }
        }

        log.info("chargingItemIdVoMap = {}", JSON.toJSON(chargingItemIdVoMap));

        if (CollectionUtil.isNotEmpty(standardList)) {
            for (RentChargingStandardListVo vo : standardList) {
                RentChargingStandardStoreRoomViewGetItem item = new RentChargingStandardStoreRoomViewGetItem();
                item = BeanUtils.copyProperties(vo, RentChargingStandardStoreRoomViewGetItem.class);

                // 项目信息
                RentChargingItemListVo rentChargingItemListVo = chargingItemIdVoMap.get(vo.getChargingItemId());
                if (rentChargingItemListVo != null) {
                    item.setChargingItemName(rentChargingItemListVo.getFeeName());
                }
                // 标准信息
                item.setChargingItemId(vo.getChargingItemId());
                item.setChargingStandardName(vo.getName());
                item.setChargingStandardId(vo.getId());

                RentChargingStandardStoreListVo standardStoreVo = standardIdStandardStoreVoMap.get(vo.getId());
                if (standardStoreVo != null) {
                    // 标准应用信息
                    item.setApplyPaymentTermSrart(standardStoreVo.getApplyPaymentTermSrart());
                    item.setApplyPaymentTermEnd(standardStoreVo.getApplyPaymentTermEnd());
                    item.setCreateBy(standardStoreVo.getCreateBy());
                    item.setCreateTime(standardStoreVo.getCreateTime());
                    item.setCreateUser(standardStoreVo.getCreateUser());
                    item.setCreateUserName(standardStoreVo.getCreateUserName());
                    item.setUpdateBy(standardStoreVo.getUpdateBy());
                    item.setUpdateTime(standardStoreVo.getUpdateTime());
                    item.setUpdateUser(standardStoreVo.getUpdateUser());
                    item.setUpdateUserName(standardStoreVo.getUpdateUserName());
                    items.add(item);
                }
            }
            List<RentChargingStandardStoreRoomViewGetItemResp> itemRespList = new ArrayList<>();
            RentChargingStandardStoreRoomViewGetItem latePaymentItem = new RentChargingStandardStoreRoomViewGetItem();
            List<RentChargingStandardStoreRoomViewGetItem> principalItemList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(items)) {
                for (RentChargingStandardStoreRoomViewGetItem item : items) {
                    // 滞纳金
                    if (StandardTypeEnum.LatePayment.getCode().equals(item.getType())) {
                        latePaymentItem = BeanUtils.copyProperties(item, RentChargingStandardStoreRoomViewGetItem.class);
                        break;
                    }
                }
                for (RentChargingStandardStoreRoomViewGetItem item : items) {
                    if (StandardTypeEnum.Principal.getCode().equals(item.getType())) {
                        principalItemList.add(item);
                    }
                }
            }
            // 对Items进行处理成本金和滞纳金在同一行
            if (CollectionUtil.isNotEmpty(principalItemList)) {
                for (RentChargingStandardStoreRoomViewGetItem principalItem : principalItemList) {
                    RentChargingStandardStoreRoomViewGetItemResp itemResp = new RentChargingStandardStoreRoomViewGetItemResp();
                    // 本金
                    itemResp.setId(principalItem.getId());
                    itemResp.setApplyPaymentTermSrart(principalItem.getApplyPaymentTermSrart());
                    itemResp.setApplyPaymentTermEnd(principalItem.getApplyPaymentTermEnd());
                    itemResp.setChargingItemName(principalItem.getChargingItemName());
                    itemResp.setChargingItemId(principalItem.getChargingItemId());
                    itemResp.setPrincipalStandardName(principalItem.getChargingStandardName());
                    itemResp.setPrincipalStandardId(principalItem.getChargingStandardId());
                    itemResp.setPrincipalExpressionDesc(principalItem.getExpressionDesc());
                    itemResp.setPrincipalCalType(principalItem.getCalType());
                    // 滞纳金
                    itemResp.setLatePaymentStandardId(latePaymentItem.getChargingStandardId());
                    itemResp.setLatePaymentStandardName(latePaymentItem.getChargingStandardName());
                    itemResp.setLatePaymentExpressionDesc(latePaymentItem.getExpressionDesc());
                    itemResp.setLatePaymentStartDate(latePaymentItem.getLatePaymentStartDate());
                    itemResp.setLatePaymentRate(latePaymentItem.getLatePaymentRate());
                    itemRespList.add(itemResp);

                }
            }
            resp.setItems(itemRespList);
        }
        return resp;
    }

    /**
     * <p>下载费用项目导入模板<br>
     */
    @Override
    public void downloadTemplate() {
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            response.setContentType("application/octet-stream");
            String endCodeFileName = URLEncoder.encode("费用项目导入模板.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
            
            // 创建空的模板数据列表
            List<RentChargingStandardStoreExcelTemplateItem> templateItems = new ArrayList<>();
            
            try (OutputStream outputStream = response.getOutputStream()) {
                EasyExcel.write(outputStream, RentChargingStandardStoreExcelTemplateItem.class)
                        .sheet("费用项目导入模板")
                        .doWrite(templateItems);
            }
        } catch (Exception e) {
            log.error("导出模板异常: {}", e.getMessage(), e);
            throw new ServiceException("导出失败，请稍后再试。", e);
        }
    }
}

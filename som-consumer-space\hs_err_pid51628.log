#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa0d8b1bd1, pid=51628, tid=7396
#
# JRE version:  (21.0.7+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130, mixed mode, sharing, tiered, compressed class ptrs, unknown gc, windows-amd64)
# Problematic frame:
# C  [jimage.dll+0x1bd1]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
#

---------------  S U M M A R Y ------------

Command Line: 

Host: Intel(R) Core(TM) i5-9400 CPU @ 2.90GHz, 6 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Mon Jul  7 09:57:50 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 0.050263 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread is native thread

Stack: [0x000000d2ea300000,0x000000d2ea400000],  sp=0x000000d2ea3fd880,  free space=1014k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [jimage.dll+0x1bd1]
C  [jimage.dll+0x1d5f]
C  [jimage.dll+0x2641]
V  [jvm.dll+0x225470]
V  [jvm.dll+0xcaee7]
V  [jvm.dll+0x87462c]
V  [jvm.dll+0x45f3ce]
V  [jvm.dll+0x4610b1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x000000000000c10c


Registers:
RAX=0x000000000000303c, RBX=0x000000d2ea3fd971, RCX=0x00007ffa0d8b6000, RDX=0x000000000000303c
RSP=0x000000d2ea3fd880, RBP=0x000000d2ea3fd970, RSI=0x0000000000008740, RDI=0x000000d2ea3fd970
R8 =0x00007ffa0d8b4288, R9 =0x000000000000001c, R10=0x00007ffa0dfd0000, R11=0x00007ffa0d8b6000
R12=0x0000000000000000, R13=0x000000d2ea3ffe00, R14=0x000000d2ea3fe9e0, R15=0x00007ff995695610
RIP=0x00007ffa0d8b1bd1, EFLAGS=0x0000000000010246

XMM[0]=0x0000000000000000 0x0000000000000000
XMM[1]=0x2f6d762f6c616e72 0x65746e692f6b646a
XMM[2]=0x736e6f6974706f2f 0x6d762f6c616e7265
XMM[3]=0x0000000000000000 0x0000000000000000
XMM[4]=0x0000000000000000 0x0000000000000000
XMM[5]=0x0000000000000000 0x0000000000000000
XMM[6]=0x0000000000000000 0x0000000000000000
XMM[7]=0x0000000000000000 0x0000000000000000
XMM[8]=0x0000000000000000 0x0000000000000000
XMM[9]=0x0000000000000000 0x0000000000000000
XMM[10]=0x0000000000000000 0x0000000000000000
XMM[11]=0x0000000000000000 0x0000000000000000
XMM[12]=0x0000000000000000 0x0000000000000000
XMM[13]=0x0000000000000000 0x0000000000000000
XMM[14]=0x0000000000000000 0x0000000000000000
XMM[15]=0x0000000000000000 0x0000000000000000
  MXCSR=0x00001f80


Top of Stack: (sp=0x000000d2ea3fd880)
0x000000d2ea3fd880:   0000028a0000003f 000000d2ea3fd8ec
0x000000d2ea3fd890:   000000d2ea3fd8b0 0000028ad9590db2
0x000000d2ea3fd8a0:   0000028ad9572ea0 00007ffa0d8b1d5f
0x000000d2ea3fd8b0:   000000000000000b 0000028ad9572ea0
0x000000d2ea3fd8c0:   0000000000000200 0000028ad9590e81
0x000000d2ea3fd8d0:   0000028ad95668c0 00000000000005ec
0x000000d2ea3fd8e0:   0000000000000000 0000000000000000
0x000000d2ea3fd8f0:   0000002a00000000 0000028ad9599530
0x000000d2ea3fd900:   0000028ad9599530 00007ffa15427dad
0x000000d2ea3fd910:   000057f62cf4851a 0000028adb070fc0
0x000000d2ea3fd920:   00007ff995672c50 0000000000000017
0x000000d2ea3fd930:   0000028ad9572ea0 000000d2ea3fe9e0
0x000000d2ea3fd940:   000000000000000b 00007ffa0d8b2641
0x000000d2ea3fd950:   0000000000000680 0000000000000008
0x000000d2ea3fd960:   0000028ad95735e0 000000d2ea3fd9d0
0x000000d2ea3fd970:   61622e6176616a2f 692f6b646a2f6573
0x000000d2ea3fd980:   2f6c616e7265746e 6f6974706f2f6d76
0x000000d2ea3fd990:   000000000000736e 00007ffa15425fc4
0x000000d2ea3fd9a0:   0000028ad964d3b0 00000000000005ec
0x000000d2ea3fd9b0:   0000028ad95735e0 0000028ad9550000
0x000000d2ea3fd9c0:   00000000000003ff 0000000000000680
0x000000d2ea3fd9d0:   0000028a00000000 0000028ad9550150
0x000000d2ea3fd9e0:   00650074006e0000 0000000000000000
0x000000d2ea3fd9f0:   0000028adb06a7d0 0000000000000001
0x000000d2ea3fda00:   0000028adb06a7c0 0000000000000001
0x000000d2ea3fda10:   0000000000000094 000001421308bcec
0x000000d2ea3fda20:   0000000000000000 0000000000000000
0x000000d2ea3fda30:   0000028ad94e0000 0000000000000094
0x000000d2ea3fda40:   0000000000000000 00007ffa12670000
0x000000d2ea3fda50:   0000000000000094 0000028ad9550150
0x000000d2ea3fda60:   0000028ad964d550 000000000000000c
0x000000d2ea3fda70:   00000000000001b0 00000000000001a2 

Instructions: (pc=0x00007ffa0d8b1bd1)
0x00007ffa0d8b1ad1:   48 85 c9 74 1d 80 3d 33 45 00 00 00 74 06 48 8b
0x00007ffa0d8b1ae1:   53 18 eb 04 48 8b 53 40 e8 b2 0e 00 00 48 83 63
0x00007ffa0d8b1af1:   48 00 8b 4b 0c 83 f9 ff 74 09 e8 e8 0c 00 00 83
0x00007ffa0d8b1b01:   4b 0c ff 48 8b 4b 70 48 85 c9 74 0f ba 10 00 00
0x00007ffa0d8b1b11:   00 e8 09 0f 00 00 48 83 63 70 00 48 83 c4 20 5b
0x00007ffa0d8b1b21:   c3 cc cc 40 53 48 83 ec 20 48 8b d9 48 8d 0d ec
0x00007ffa0d8b1b31:   46 00 00 e8 b7 0c 00 00 83 6b 08 01 75 24 48 8b
0x00007ffa0d8b1b41:   d3 48 8d 0d cf 44 00 00 e8 22 08 00 00 48 8b cb
0x00007ffa0d8b1b51:   e8 de fe ff ff ba 78 00 00 00 48 8b cb e8 bd 0e
0x00007ffa0d8b1b61:   00 00 48 8d 0d b6 46 00 00 48 83 c4 20 5b e9 84
0x00007ffa0d8b1b71:   0c 00 00 48 89 5c 24 08 48 89 74 24 10 57 48 83
0x00007ffa0d8b1b81:   ec 20 41 8b f1 4d 8b c8 48 8b fa 4c 8b d9 4d 85
0x00007ffa0d8b1b91:   c0 74 78 85 f6 74 74 44 8a 12 48 8d 5a 01 48 8b
0x00007ffa0d8b1ba1:   d3 b8 93 01 00 01 eb 12 69 c0 93 01 00 01 41 0f
0x00007ffa0d8b1bb1:   b6 ca 44 8a 12 33 c1 48 ff c2 45 84 d2 75 e9 4d
0x00007ffa0d8b1bc1:   8b 03 0f ba f0 1f 33 d2 49 8b cb f7 f6 48 63 c2
0x00007ffa0d8b1bd1:   41 8b 14 81 41 ff 50 08 85 c0 7e 24 8a 0f eb 10
0x00007ffa0d8b1be1:   69 c0 93 01 00 01 0f b6 c9 33 c1 8a 0b 48 ff c3
0x00007ffa0d8b1bf1:   84 c9 75 ec 0f ba f0 1f 33 d2 f7 f6 8b c2 eb 0e
0x00007ffa0d8b1c01:   79 09 83 c9 ff 2b c8 8b c1 eb 03 83 c8 ff 48 8b
0x00007ffa0d8b1c11:   5c 24 30 48 8b 74 24 38 48 83 c4 20 5f c3 cc 48
0x00007ffa0d8b1c21:   89 5c 24 08 48 89 74 24 10 57 48 83 ec 20 48 8b
0x00007ffa0d8b1c31:   f1 48 8d 0d e7 45 00 00 e8 b2 0b 00 00 33 ff 39
0x00007ffa0d8b1c41:   3d d2 43 00 00 76 24 48 8b 05 d1 43 00 00 48 8b
0x00007ffa0d8b1c51:   d6 48 8b 1c f8 48 8b 0b e8 7c 1e 00 00 85 c0 74
0x00007ffa0d8b1c61:   2b ff c7 3b 3d ae 43 00 00 72 dc 33 db 48 8d 0d
0x00007ffa0d8b1c71:   ab 45 00 00 e8 7e 0b 00 00 48 8b 74 24 38 48 8b
0x00007ffa0d8b1c81:   c3 48 8b 5c 24 30 48 83 c4 20 5f c3 ff 43 08 eb
0x00007ffa0d8b1c91:   dc cc cc 48 89 5c 24 08 48 89 74 24 10 57 48 83
0x00007ffa0d8b1ca1:   ec 20 48 8b d9 48 8b f2 48 8b 49 10 49 8b f8 8b
0x00007ffa0d8b1cb1:   53 30 48 8b 01 ff 50 20 4c 8b 43 50 48 8b d6 48
0x00007ffa0d8b1cc1:   8b 4b 10 44 8b c8 e8 a8 fe ff ff 83 f8 ff 74 38 



---------------  P R O C E S S  ---------------

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread: None

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Not initialized>


Dynamic libraries:
0x00007ff6226a0000 - 0x00007ff6226aa000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.exe
0x00007ffa153e0000 - 0x00007ffa15645000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa143f0000 - 0x00007ffa144b9000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa12910000 - 0x00007ffa12cf8000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa13030000 - 0x00007ffa1317b000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa0aa20000 - 0x00007ffa0aa38000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jli.dll
0x00007ffa13d70000 - 0x00007ffa13f3a000 	C:\Windows\System32\USER32.dll
0x00007ffa12e80000 - 0x00007ffa12ea7000 	C:\Windows\System32\win32u.dll
0x00007ffa144c0000 - 0x00007ffa144eb000 	C:\Windows\System32\GDI32.dll
0x00007ffa12530000 - 0x00007ffa12667000 	C:\Windows\System32\gdi32full.dll
0x00007ffa12860000 - 0x00007ffa12903000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa0dfd0000 - 0x00007ffa0dfeb000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\VCRUNTIME140.dll
0x00007ff9e2090000 - 0x00007ff9e232a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffa146b0000 - 0x00007ffa14759000 	C:\Windows\System32\msvcrt.dll
0x00007ffa148f0000 - 0x00007ffa14920000 	C:\Windows\System32\IMM32.DLL
0x00007ffa0d970000 - 0x00007ffa0d97c000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\vcruntime140_1.dll
0x00007ff9efac0000 - 0x00007ff9efb4d000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\msvcp140.dll
0x00007ff994c70000 - 0x00007ff995a31000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\server\jvm.dll
0x00007ffa14760000 - 0x00007ffa14813000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa13330000 - 0x00007ffa133d6000 	C:\Windows\System32\sechost.dll
0x00007ffa14ab0000 - 0x00007ffa14bc5000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa13560000 - 0x00007ffa135d4000 	C:\Windows\System32\WS2_32.dll
0x00007ffa122d0000 - 0x00007ffa1232e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa04c10000 - 0x00007ffa04c45000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa0e190000 - 0x00007ffa0e19b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa122b0000 - 0x00007ffa122c4000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa112f0000 - 0x00007ffa1130b000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa0d8b0000 - 0x00007ffa0d8ba000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jimage.dll
0x00007ffa06230000 - 0x00007ffa06471000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa13980000 - 0x00007ffa13d05000 	C:\Windows\System32\combase.dll
0x00007ffa14930000 - 0x00007ffa14a11000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa03bf0000 - 0x00007ffa03c29000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa12670000 - 0x00007ffa12709000 	C:\Windows\System32\bcryptPrimitives.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\IntelliJ IDEA 2025.1.3\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;D:\IntelliJ IDEA 2025.1.3\jbr\bin\server

VM Arguments:
java_command: <unknown>
java_class_path (initial): <not set>
Launcher Type: SUN_STANDARD

[Global flags]
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\jdk-17.0.10
CLASSPATH=.;D:\jdk-17.0.10\lib;D:\jdk-17.0.10\lib\tools.jar
PATH=D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;D:\ShadowBot;D:\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\jdk-17.0.10\bin;E:\JavaSpace\peizhi\apache-maven-3.5.4\apache-maven-3.5.4\bin;D:\Git\cmd;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;D:\cursor\resources\app\bin;E:\node;C:\Program Files\dotnet;D:\ShadowBot;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Microsoft VS Code\bin;D:\IntelliJ IDEA 2025.1.3\bin
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 11500K (0% of 16609336K total physical memory with 2392564K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Not available (crashed in non-Java thread)
---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 25 days 14:26 hours

CPU: total 6 (initial active 0) 
Processor Information for the first 6 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 16220M (2336M free)
TotalPageFile size 32693M (AvailPageFile size 75M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 3M, peak: 3M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130) for windows-amd64 JRE (21.0.7+9-b895.130), built on 2025-05-13 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.

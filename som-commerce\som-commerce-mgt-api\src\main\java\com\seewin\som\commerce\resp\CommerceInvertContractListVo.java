package com.seewin.som.commerce.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
@Setter
public class CommerceInvertContractListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 门店id
     */
    private String storeId;

    /**
     * 租赁起始时间
     */
    private LocalDate rentStartDate;

    /**
     * 带看日期
     */
    private LocalDate visitDate;
    /**
     * 招商周期
     */
    private int investCycle;
}

package com.seewin.som.report.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 主数据库销售数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
@Data
@TableName("som_report_master_sale_data")
public class ReportMasterSaleData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id(项目id)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 铺位号
     */
    @TableField("shop_no")
    private String shopNo;

    /**
     * 应用对象(铺位所属位置)
     */
    @TableField("use_obj")
    private String useObj;

    /**
     * 所属位置(铺位所属位置空间全编码) fcode
     */
    @TableField("obj_code")
    private String objCode;

    /**
     * 门店ID
     */
    @TableField("store_id")
    private String storeId;

    /**
     * 门店品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 适配业态字典id
     */
    @TableField("commercial_type_code")
    private Long commercialTypeCode;

    /**
     * 适配业态名称
     */
    @TableField("commercial_type_name")
    private String commercialTypeName;

    /**
     * 所属一级品类
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 所属一级品类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 销售总额（元）
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 修改前销售总额（元）
     */
    @TableField("before_total_amount")
    private BigDecimal beforeTotalAmount;

    /**
     * 销售总笔数（单）
     */
    @TableField("total_order")
    private Integer totalOrder;

    /**
     * 修改前销售总笔数（单）
     */
    @TableField("before_total_order")
    private Integer beforeTotalOrder;

    /**
     * 门店销售额（元）
     */
    @TableField("store_amount")
    private BigDecimal storeAmount;

    /**
     * 修改前门店销售额（元）
     */
    @TableField("before_store_amount")
    private BigDecimal beforeStoreAmount;

    /**
     * 门店销售笔数（单）
     */
    @TableField("store_order")
    private Integer storeOrder;

    /**
     * 修改前门店销售笔数（单）
     */
    @TableField("before_store_order")
    private Integer beforeStoreOrder;

    /**
     * 外卖销售额（元）
     */
    @TableField("takeaway_amount")
    private BigDecimal takeawayAmount;

    /**
     * 修改前外卖销售额（元）
     */
    @TableField("before_takeaway_amount")
    private BigDecimal beforeTakeawayAmount;

    /**
     * 外卖销售笔数（单）
     */
    @TableField("takeaway_order")
    private Integer takeawayOrder;

    /**
     * 修改前外卖销售笔数（单）
     */
    @TableField("before_takeaway_order")
    private Integer beforeTakeawayOrder;

    /**
     * 数据来源(0-自动采集  1-手动录入)
     */
    @TableField("data_sources")
    private Integer dataSources;

    /**
     * 数据状态(0-未修改  1-已修改)
     */
    @TableField("data_status")
    private Integer dataStatus;

    /**
     * 销售日期
     */
    @TableField("sale_time")
    private LocalDate saleTime;

    /**
     * 修改前销售单的销售数据
     */
    @TableField("before_sale_data")
    private String beforeSaleData;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 城市
     */
    @TableField("city")
    private String city;

    /**
     * 二级类品id
     */
    @TableField("commercial_two_id")
    private Long commercialTwoId;

    /**
     * 二级类品名称
     */
    @TableField("commercial_two")
    private String commercialTwo;
}

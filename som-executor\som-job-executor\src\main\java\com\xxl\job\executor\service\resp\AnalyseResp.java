package com.xxl.job.executor.service.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class AnalyseResp implements Serializable {
    /**
     * 指标名称Id
     */
    private Integer indicatorId;

    /**
     * 指标名称
     */
    private String indicator;
    /**
     * 分析指标描述
     */
    private String indicatorRemark;
    /**
     * 适配业态字典id
     */
    private Long commercialTypeCode;

    /**
     * 适配业态名称
     */
    private String commercialTypeName;
    /**
     * 所属一级品类
     */
    private Long categoryId;

    /**
     * 所属一级品类名称
     */
    private String categoryName;
    /**
     * 当前数据
     */
    private BigDecimal currentValue;
    /**
     * 同比数据
     */
    private BigDecimal yearOnYearData;

    /**
     * 环比数据
     */
    private BigDecimal monthOnMonthData;
    /**
     * 详情数据集合
     */
    private List<BigDecimal> list;

    private List<Item>objList;

    @Getter
    @Setter
    public static class Item {
        private String key;
        private BigDecimal value;
    }
}

package com.seewin.som.rent.provider.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.seewin.som.rent.req.RentChargingStandardStoreBindDto;
import com.seewin.som.rent.resp.RentChargingBaseListVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.rent.entity.RentChargingStandardStore;
import com.seewin.som.rent.req.RentChargingStandardStoreAddDto;
import com.seewin.som.rent.req.RentChargingStandardStoreEditDto;
import com.seewin.som.rent.req.RentChargingStandardStoreListDto;
import com.seewin.som.rent.resp.RentChargingStandardStoreAddVo;
import com.seewin.som.rent.resp.RentChargingStandardStoreGetVo;
import com.seewin.som.rent.resp.RentChargingStandardStoreListVo;
import com.seewin.util.bean.BeanUtils;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.seewin.som.rent.service.RentChargingStandardStoreService;
import com.seewin.som.rent.provider.RentChargingStandardStoreProvider;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <p>
 * 收费标准关联门店表 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Slf4j
@DubboService
public class RentChargingStandardStoreProviderImpl implements RentChargingStandardStoreProvider {

    @Autowired
    private RentChargingStandardStoreService rentChargingStandardStoreService;

    /**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public PageResult<RentChargingStandardStoreListVo> page(PageQuery<RentChargingStandardStoreListDto> pageQuery) throws ServiceException {
        RentChargingStandardStoreListDto dto = pageQuery.getQueryDto();

        //设置分页
        Page<RentChargingStandardStore> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        //构造查询条件
        QueryWrapper<RentChargingStandardStore> queryWrapper = queryBuild(dto);

        //查询数据
        page = rentChargingStandardStoreService.page(page, queryWrapper);
        log.info("page = {}", JSON.toJSON(page));
        List<RentChargingStandardStore> records = page.getRecords();

        //响应结果封装
        PageResult<RentChargingStandardStoreListVo> result = new PageResult<>();
        List<RentChargingStandardStoreListVo> items = BeanUtils.copyProperties(records, RentChargingStandardStoreListVo.class);

        result.setItems(items);
        result.setPages((int) page.getPages());
        result.setTotal((int) page.getTotal());
        result.setPageNum(pageQuery.getPageNum());
        result.setPageSize(pageQuery.getPageSize());

        //返回查询结果
        return result;
    }

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public List<RentChargingStandardStoreListVo> list(RentChargingStandardStoreListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<RentChargingStandardStore> queryWrapper = queryBuild(dto);
        if (CollectionUtil.isNotEmpty(dto.getChargingStandardIdSet())) {
            queryWrapper.in("charging_standard_id", dto.getChargingStandardIdSet());
        }

        //查询数据
        List<RentChargingStandardStore> records = rentChargingStandardStoreService.list(queryWrapper);

        //响应结果封装
        List<RentChargingStandardStoreListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, RentChargingStandardStoreListVo.class);

        //返回查询结果
        return result;
    }

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    @Override
    public int count(RentChargingStandardStoreListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<RentChargingStandardStore> queryWrapper = queryBuild(dto, false);

        //查询数据
        int result = (int) rentChargingStandardStoreService.count(queryWrapper);

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public RentChargingStandardStoreGetVo get(Long id) throws ServiceException {
        //查询数据
        RentChargingStandardStore item = rentChargingStandardStoreService.getById(id);

        //响应结果封装
        RentChargingStandardStoreGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, RentChargingStandardStoreGetVo.class);
        }

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public RentChargingStandardStoreGetVo get(RentChargingStandardStoreListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<RentChargingStandardStore> queryWrapper = queryBuild(dto);
        queryWrapper.last(PageQuery.LIMIT_ONE);

        //查询数据
        RentChargingStandardStore item = rentChargingStandardStoreService.getOne(queryWrapper);

        //响应结果封装
        RentChargingStandardStoreGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, RentChargingStandardStoreGetVo.class);
        }

        //返回查询结果
        return result;
    }

    @Override
    public List<RentChargingBaseListVo> getRentChargingBaseList(RentChargingStandardStoreListDto dto) throws ServiceException {
        return rentChargingStandardStoreService.getRentChargingBaseList(dto);
    }


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    @Override
    public RentChargingStandardStoreAddVo add(RentChargingStandardStoreAddDto dto) throws ServiceException {
        RentChargingStandardStore entity = BeanUtils.copyProperties(dto, RentChargingStandardStore.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setCreateBy(dto.getCreateBy());
        entity.setCreateUser(dto.getCreateUser());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setUpdateBy(dto.getCreateBy());
        entity.setUpdateUser(dto.getCreateUser());
        entity.setUpdateUserName(dto.getCreateUserName());
        entity.setUpdateTime(nowTime);
        entity.setDelStatus(0);
        // 默认版本号为0
        entity.setVersion(0);
        rentChargingStandardStoreService.save(entity);

        //响应结果封装
        RentChargingStandardStoreAddVo result = new RentChargingStandardStoreAddVo();
        result.setId(entity.getId());

        return result;
    }


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void edit(RentChargingStandardStoreEditDto dto) throws ServiceException {
        RentChargingStandardStore entity = BeanUtils.copyProperties(dto, RentChargingStandardStore.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateUser(dto.getUpdateUser());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateTime(nowTime);

        System.out.println(JSON.toJSONString(entity));

        rentChargingStandardStoreService.updateById(entity);
    }

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void delete(Long id) throws ServiceException {
        rentChargingStandardStoreService.removeById(id);
    }

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    @Override
    public void delete(RentChargingStandardStoreListDto dto) throws ServiceException {
        //构造查询条件
        QueryWrapper<RentChargingStandardStore> queryWrapper = queryBuild(dto, false);

        //删除操作
        rentChargingStandardStoreService.remove(queryWrapper);
    }

    @Override
    public boolean removeBatchByIds(List<Long> idList) throws ServiceException {
        return rentChargingStandardStoreService.removeBatchByIds(idList);
    }

    @Override
    public boolean saveBatch(List<RentChargingStandardStoreAddDto> dtoList) throws ServiceException {
        List<RentChargingStandardStore> entityList = new ArrayList<>();
        for (RentChargingStandardStoreAddDto dto : dtoList) {
            RentChargingStandardStore entity = BeanUtils.copyProperties(dto, RentChargingStandardStore.class);

            LocalDateTime nowTime = LocalDateTime.now();
            entity.setId(IdWorker.getId());
            entity.setCreateTime(nowTime);
            entity.setCreateBy(dto.getCreateBy());
            entity.setCreateUser(dto.getCreateUser());
            entity.setCreateUserName(dto.getCreateUserName());
            entity.setUpdateBy(dto.getCreateBy());
            entity.setUpdateUser(dto.getCreateUser());
            entity.setUpdateUserName(dto.getCreateUserName());
            entity.setUpdateTime(nowTime);
            entity.setDelStatus(0);
            entity.setVersion(0);

            entityList.add(entity);
        }
        return rentChargingStandardStoreService.saveBatch(entityList);
    }

    /**
     * <p>批量删除<br>
     *
     * @param contractCodes 删除条件Dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByContactCodes(List<String> contractCodes) {
        if (CollectionUtil.isEmpty(contractCodes)) return;

        UpdateWrapper<RentChargingStandardStore> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("contract_code", contractCodes)
                .eq("del_status", 0)
                .set("del_status", 1)
                .set("update_time",LocalDateTime.now());

        rentChargingStandardStoreService.update(null, updateWrapper);
    }

    /**
     * <p>构造查询条件<br>
     * <p>默认构造排序条件<br>
     *
     * @param dto 查询条件Dto
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<RentChargingStandardStore> queryBuild(RentChargingStandardStoreListDto dto) throws ServiceException {
        return queryBuild(dto, true);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<RentChargingStandardStore> queryBuild(RentChargingStandardStoreListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<RentChargingStandardStore> queryWrapper = new QueryWrapper<>();

        RentChargingStandardStore entity = BeanUtils.copyProperties(dto, RentChargingStandardStore.class);
        entity.setDelStatus(0);

        if (!CollectionUtils.isEmpty(dto.getContractCodeList())) {
            queryWrapper.in(dto.getContractCodeList() != null, "contract_code", dto.getContractCodeList());
        }

        if (!CollectionUtils.isEmpty(dto.getRoomNameList())) {
            queryWrapper.in(dto.getRoomNameList() != null, "room_name", dto.getRoomNameList());
        }

        if (!CollectionUtils.isEmpty(dto.getChargingStandardIdSet())) {
            queryWrapper.in(dto.getChargingStandardIdSet() != null, "charging_standard_id", dto.getChargingStandardIdSet());
        }

        if (StringUtils.isNotBlank(dto.getStoreIdLike())) {
            entity.setStoreId(null);
            queryWrapper.like("store_id", dto.getStoreIdLike());
        }

        if (StringUtils.isNotBlank(dto.getAdvertName())) {
            entity.setAdvertName(null);
            queryWrapper.like("advert_name", dto.getAdvertName());
        }
        // 按合同号排序
        if (orderBy) {
            queryWrapper.last("ORDER BY IF(isnull(contract_code),0,1), contract_code DESC");
        }

        /** 添加条件样例参考，不用请删除
         if (StringUtils.isNotBlank(dto.getName())) {
         entity.setName(null);
         queryWrapper.like("name", dto.getName());
         }

         queryWrapper.in(dto.getStatusIn() != null, "status", dto.getStatusIn());

         if (orderBy) {
         if (dto.getTypeOrder() != null) {
         queryWrapper.orderBy(true, dto.getTypeOrder().isAsc(), "type");
         }

         queryWrapper.orderByAsc("order_by");
         }
         */

        //按创建时间倒序排序，根据需要添加
        //queryWrapper.orderByDesc("create_time");

        queryWrapper.setEntity(entity);

        return queryWrapper;
    }
}

package com.seewin.som.rent.controller;

import com.seewin.som.rent.vo.req.*;
import com.seewin.som.rent.vo.resp.*;
import org.apache.dubbo.common.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.seewin.som.rent.service.RentChargingStandardStoreService;
import com.seewin.consumer.data.ApiMethod;
import com.seewin.consumer.data.ApiResponse;
import com.seewin.consumer.vo.PageResp;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 收费标准关联门店表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Tag(name = "收费标准关联门店表")
@RestController
@RequestMapping("rentChargingStandardStore")
public class RentChargingStandardStoreController {

    @Autowired
    private RentChargingStandardStoreService rentChargingStandardStoreService;

    @Operation(summary = "收费标准关联门店表查询", description = "权限码：opt:rent:rentchargingstandardstore:page")
    @PostMapping(ApiMethod.PAGE)
    public ApiResponse<PageResp<RentChargingStandardStoreListItem>> page(@RequestBody @Valid RentChargingStandardStoreListReq listReq) {
        ApiResponse<PageResp<RentChargingStandardStoreListItem>> result = new ApiResponse<>();

        PageResp<RentChargingStandardStoreListItem> pageResp = rentChargingStandardStoreService.page(listReq);

        result.setData(pageResp);

        return result;
    }

    @Operation(summary = "收费标准关联门店表详情", description = "权限码：opt:rent:rentchargingstandardstore:get")
    @GetMapping(ApiMethod.GET)
    public ApiResponse<RentChargingStandardStoreGetResp> get(@Valid RentChargingStandardStoreGetReq getReq) {
        ApiResponse<RentChargingStandardStoreGetResp> result = new ApiResponse<>();

        RentChargingStandardStoreGetResp getResp = rentChargingStandardStoreService.get(getReq);

        result.setData(getResp);

        return result;
    }

    @Operation(summary = "收费标准关联门店表新增", description = "权限码：opt:rent:rentchargingstandardstore:add")
    @PostMapping(ApiMethod.ADD)
    public ApiResponse<RentChargingStandardStoreAddResp> add(@RequestBody @Valid RentChargingStandardStoreAddReq addReq) {
        ApiResponse<RentChargingStandardStoreAddResp> result = new ApiResponse<>();

        RentChargingStandardStoreAddResp addResp = rentChargingStandardStoreService.add(addReq);

        result.setData(addResp);

        return result;
    }

    @Operation(summary = "收费标准关联门店表编辑", description = "权限码：opt:rent:rentchargingstandardstore:edit")
    @PostMapping(ApiMethod.EDIT)
    public ApiResponse edit(@RequestBody @Valid RentChargingStandardStoreEditReq editReq) {
        ApiResponse result = new ApiResponse<>();

        rentChargingStandardStoreService.edit(editReq);

        return result;
    }

    @Operation(summary = "收费标准关联门店表删除", description = "权限码：opt:rent:rentchargingstandardstore:del")
    @PostMapping(ApiMethod.DEL)
    public ApiResponse del(@RequestBody @Valid RentChargingStandardStoreDelReq delReq) {
        ApiResponse result = new ApiResponse<>();
        rentChargingStandardStoreService.del(delReq);

        return result;
    }

    @Operation(summary = "收费标准关联门店表绑定", description = "权限码：opt:rent:rentchargingstandardstore:bind")
    @PostMapping("bind")
    public ApiResponse bind(@RequestBody @Valid RentChargingStandardStoreBindReq bindReq) {
        ApiResponse result = new ApiResponse<>();
        boolean flag = rentChargingStandardStoreService.bind(bindReq);
        result.setData(flag);
        return result;
    }

    @Operation(summary = "计费规则映射分页列表(门店视角)", description = "权限码：opt:rent:rentchargingstandardstore:roomViewPage")
    @PostMapping("roomViewPage")
    public ApiResponse<PageResp<RentChargingStandardStoreRoomViewPageResp>> roomViewPage(@RequestBody @Valid RentChargingStandardStoreRoomViewPageReq roomViewPageReq) {
        ApiResponse<PageResp<RentChargingStandardStoreRoomViewPageResp>> result = new ApiResponse<>();
        PageResp<RentChargingStandardStoreRoomViewPageResp> pageResp = rentChargingStandardStoreService.roomViewPage(roomViewPageReq);
        result.setData(pageResp);
        return result;
    }

    @Operation(summary = "计费规则映射编辑(门店视角)", description = "权限码：opt:rent:rentchargingstandardstore:roomViewEdit")
    @PostMapping("roomViewEdit")
    public ApiResponse roomViewEdit(@RequestBody @Valid RentChargingStandardStoreRoomViewEditReq roomViewEditReq) {
        ApiResponse result = new ApiResponse<>();
        boolean flag = rentChargingStandardStoreService.roomViewEdit(roomViewEditReq);
        result.setData(flag);
        return result;
    }

    @Operation(summary = "计费规则映射查看详情(门店视角)", description = "权限码：opt:rent:rentchargingstandardstore:roomViewGet")
    @PostMapping("roomViewGet")
    public ApiResponse<RentChargingStandardStoreRoomViewGetResp> roomViewGet(@RequestBody @Valid RentChargingStandardStoreRoomViewGetReq roomViewGet) {
        ApiResponse result = new ApiResponse<>();
        RentChargingStandardStoreRoomViewGetResp resp = rentChargingStandardStoreService.roomViewGet(roomViewGet);
        result.setData(resp);
        return result;
    }

    /**
     * 下载费用项目导入模板
     */
    @Operation(summary = "下载费用项目导入模板", description = "权限码：opt:rent:rentchargingstandardstore:downloadTemplate")
    @PostMapping("/downloadTemplate")
    public void downloadTemplate() {
        rentChargingStandardStoreService.downloadTemplate();
    }

    /**
     * 费用项目导入
     */
    @Operation(summary = "费用项目导入", description = "权限码：opt:rent:rentchargingstandardstore:import")
    @PostMapping("/import")
    public ApiResponse imp(@RequestBody @Valid RentFeeImportReq req) {
        ApiResponse result = new ApiResponse<>();
        rentChargingStandardStoreService.imp(req);
        return result;
    }

//    public static void main(String[] args) {
//
//        List<String> list1 = Arrays.asList("1", "2", "3", "4", "5");
//        List<String> list2 = Arrays.asList("2", "3", "5");
//        List<List<String>> inputList = new ArrayList<>();
//        inputList.add(list1);
//        inputList.add(list2);
//        List<String> expectList = Arrays.asList("2", "3", "5");
//        //List<String> realList = CalculateUtils.getIntersection(inputList);
//        List<String> selectContractCodeList = list1.stream()
//                .filter(list2::contains)
//                .collect(Collectors.toList());
//        System.out.println(JsonUtils.toJson(selectContractCodeList));
//
//    }


}

package com.seewin.som.report.resp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 主数据库销售数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-28
 */
@Getter
@Setter
public class ReportMasterSaleDataGetVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 铺位号
     */
    private String shopNo;

    /**
     * 应用对象(铺位所属位置)
     */
    private String useObj;

    /**
     * 所属位置(铺位所属位置空间全编码) fcode
     */
    private String objCode;

    /**
     * 门店品牌名称
     */
    private String brandName;

    /**
     * 销售总额（元）
     */
    private BigDecimal totalAmount;

    /**
     * 修改前销售总额（元）
     */
    private BigDecimal beforeTotalAmount;

    /**
     * 销售总笔数（单）
     */
    private Integer totalOrder;

    /**
     * 修改前销售总笔数（单）
     */
    private Integer beforeTotalOrder;

    /**
     * 门店销售额（元）
     */
    private BigDecimal storeAmount;

    /**
     * 修改前门店销售额（元）
     */
    private BigDecimal beforeStoreAmount;

    /**
     * 门店销售笔数（单）
     */
    private Integer storeOrder;

    /**
     * 修改前门店销售笔数（单）
     */
    private Integer beforeStoreOrder;

    /**
     * 外卖销售额（元）
     */
    private BigDecimal takeawayAmount;

    /**
     * 修改前外卖销售额（元）
     */
    private BigDecimal beforeTakeawayAmount;

    /**
     * 外卖销售笔数（单）
     */
    private Integer takeawayOrder;

    /**
     * 修改前外卖销售笔数（单）
     */
    private Integer beforeTakeawayOrder;

    /**
     * 数据来源(0-自动采集  1-手动录入)
     */
    private Integer dataSources;

    /**
     * 数据状态(0-未修改  1-已修改)
     */
    private Integer dataStatus;

    /**
     * 销售日期
     */
    private LocalDate saleTime;

    /**
     * 修改前销售单的销售数据
     */
    private String beforeSaleData;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


    /**
     * 销售单类型: 0-销售单 1-日结单
     */
    private Integer saleOrderType;

    /**
     * 日结单销售总笔数（单）
     */
    private BigDecimal daySettleTotalAmount = new BigDecimal(0);

    /**
     * 日结单销售总笔数（单）
     */
    private Integer daySettleTotalOrder = 0;

    /**
     * 日结单门店销售额（元）
     */
    private BigDecimal daySettleStoreAmount = new BigDecimal(0);

    /**
     * 日结单门店销售笔数（单）
     */
    private Integer daySettleStoreOrder = 0;

    /**
     * 日结单外卖销售额（元）
     */
    private BigDecimal daySettleTakeawayAmount = new BigDecimal(0);

    /**
     * 日结单外卖销售笔数（单）
     */
    private Integer daySettleTakeawayOrder = 0;

}

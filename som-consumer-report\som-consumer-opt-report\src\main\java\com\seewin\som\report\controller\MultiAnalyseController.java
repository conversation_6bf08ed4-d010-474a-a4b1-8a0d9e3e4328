package com.seewin.som.report.controller;

import com.seewin.consumer.data.ApiResponse;
import com.seewin.som.report.service.MultiAnalyseFlowService;
import com.seewin.som.report.service.MultiAnalyseService;
import com.seewin.som.report.vo.req.MultiAnalyseReq;
import com.seewin.som.report.vo.resp.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.apache.dubbo.common.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "多维数据分析第二次迭代")
@RestController
@RequestMapping("multiAnalyse")
public class MultiAnalyseController {

    private final static Logger logger = LoggerFactory.getLogger(MultiAnalyseController.class);

    @Autowired
    private MultiAnalyseService multiAnalyseService;

    @Autowired
    private MultiAnalyseFlowService multiAnalyseFlowService;

    @Operation(summary = "销售分析", description = "权限码：opt:report:multiAnalyse:salesAnalysis")
    @PostMapping("salesAnalysis")
    public ApiResponse<MultiAnalyseResp> salesAnalysis(@RequestBody @Valid MultiAnalyseReq req) {
        logger.info("销售分析:{}", JsonUtils.toJson(req));
        MultiAnalyseResp resp = multiAnalyseService.salesAnalysis(req);
        ApiResponse<MultiAnalyseResp> result = new ApiResponse<>();
        result.setData(resp);
        return result;
    }

    @Operation(summary = "销售明细", description = "权限码：opt:report:multiAnalyse:salesDetail")
    @PostMapping("salesDetail")
    public ApiResponse<MultiAnalyseResp> salesDetail(@RequestBody @Valid MultiAnalyseReq req) {
        logger.info("销售明细:{}", JsonUtils.toJson(req));
        MultiAnalyseResp resp = multiAnalyseService.salesDetail(req);
        ApiResponse<MultiAnalyseResp> result = new ApiResponse<>();
        result.setData(resp);
        return result;
    }

    @Operation(summary = "客流分析", description = "权限码：opt:report:multiAnalyse:flowAnalysis")
    @PostMapping("flowAnalysis")
    public ApiResponse<MultiAnalyseFlowResp> flowAnalysis(@RequestBody @Valid MultiAnalyseReq req) {
        logger.info("客流分析:{}", JsonUtils.toJson(req));
        MultiAnalyseFlowResp resp = multiAnalyseFlowService.flowAnalysis(req);
        ApiResponse<MultiAnalyseFlowResp> result = new ApiResponse<>();
        result.setData(resp);
        return result;
    }

    @Operation(summary = "客流明细", description = "权限码：opt:report:multiAnalyse:flowDetail")
    @PostMapping("flowDetail")
    public ApiResponse<MultiAnalyseFlowResp> flowDetail(@RequestBody @Valid MultiAnalyseReq req) {
        logger.info("客流明细:{}", JsonUtils.toJson(req));
        MultiAnalyseFlowResp resp = multiAnalyseFlowService.flowDetail(req);
        ApiResponse<MultiAnalyseFlowResp> result = new ApiResponse<>();
        result.setData(resp);
        return result;
    }

    @Operation(summary = "导出客流明细",description = "权限码：opt:report:multiAnalyse:expFlowDetail")
    @PostMapping("expFlowDetail")
    public void expFlowDetail(@RequestBody @Valid MultiAnalyseReq req) {
        logger.info("导出客流明细:{}", JsonUtils.toJson(req));
        multiAnalyseFlowService.expFlowDetail(req);
    }

}

package com.seewin.som.commerce.controller;

import cn.hutool.http.HttpResponse;
import com.seewin.consumer.data.ApiMethod;
import com.seewin.consumer.data.ApiResponse;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.OptUser;
import com.seewin.som.ent.provider.EntUserProvider;
import com.seewin.som.ent.resp.EntUserGetVo;
import com.seewin.util.exception.ServiceException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.util.HashMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * OpenWebUI 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Tag(name = "OpenWebUI")
@RestController
@RequestMapping("commerceOpenWeb")
@Slf4j
public class CommerceOpenWebController {

	@DubboReference(providedBy = "som-ent-mgt")
	private EntUserProvider entUserProvider;

	@Value("${openwebui.regUrl}")
	private String regUrl;
	@Value("${openwebui.logUrl}")
	private String logUrl;
	@Value("${openwebui.redUrl}")
	private String redUrl;

	@Operation(summary = "OpenWebUI", description = "权限码：eip:commerce:commerceOpenWeb:get")
	@GetMapping(ApiMethod.GET)
	public ApiResponse<String> get() {
		ApiResponse<String> result = new ApiResponse<>();
		try {
			OptUser curUser = ApiUtils.getUser(OptUser.class);
			Long userId = curUser.getUserId();
			String userName = curUser.getUserName();
			// 名称显示中文姓名
			String realName = curUser.getRealName();
			log.info("userId:{}", userId);
			EntUserGetVo entUserVo = entUserProvider.get(userId);
			if (entUserVo==null){
				throw new ServiceException("当前用户不存在");
			}
			String email = userName+"@rwei.com.cn";
			log.info("email:{}", email);
			String password = "123456";

			getLoginRes(email, password, result);

			HashMap regParams = new HashMap<>();
			// 名称显示中文姓名
			regParams.put("name", realName);
			regParams.put("password", password);
			regParams.put("email", email);
			regParams.put(
					"profile_image_url",
					"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAAAXNSR0IArs4c6QAAAq9JREFUeF7tmz9rU3EYhc+9Se+lVUopXSyCIP4ZpOCQRQtOTjo4FAp+BPUrWBxdRMHZTb9Ah44OIjo4STeddGkXt6KkptcruUJoEVL4wXtzuDyZArnv+zs8T04SEpIdvFquxc2GQIYQGxdNEIR4+UCImQ+EIMSNgFke3kMQYkbALA4NQYgZAbM4NAQhZgTM4tAQhJgRMItDQxBiRsAsDg1BiBkBszg0BCFmBMzi0BCEmBEwi0NDEGJGwCwODUGIGQGzODQEIWYEzOLQEISYETCLQ0MQYkbALE5nGpLNndX8nW3lK9dV/9rX8N0DVXvvzXCfHqczQorBloq1h1JeIOR077FX9C/cVbn+XNn8SnMQDYnlPXV77/xtlTeeKl+8OLkOITMS0ju3rvLmM+VLV04kQMgMhPQvbaocbCk7s/rf6QhpUcj401QxeKz+5fsa329uf46kupJ6Je8hLbrQuBXF2iPly9fG/1f9d3Q11OjLa/VWbylfuoqQNoUsbHycQG+KcfBNo88vNPr6Rscf4yWrJSsT6NWhjr7v6PDTE9U/95rTEdKShOPHLNx7q3r4Q793X6ra/3AiAUJmIGTakQhBSAiBznyXRUNCnh/pSxGSzi5kEiEhWNOXIiSdXcgkQkKwpi9FSDq7kEmEhGBNX4qQdHYhkwgJwZq+FCHp7EImERKCNX0pQtLZhUwiJARr+lKEpLMLmURICNb0pQhJZ8fkFAKd+YGqK5YRYmYSIQgxI2AWh4YgxIyAWRwaghAzAmZxaAhCzAiYxaEhCDEjYBaHhiDEjIBZHBqCEDMCZnFoCELMCJjFoSEIMSNgFoeGIMSMgFkcGoIQMwJmcWgIQswImMWhIQgxI2AWh4YgxIyAWRwaghAzAmZxaAhCzAiYxaEhCDEjYBaHhiDEjIBZnL8EWjIDsmw/0QAAAABJRU5ErkJggg==");
			HttpResponse registerRes =
					HttpRequest.post(regUrl)
							.header("Content-Type", "application/json")
							.body(JSONUtil.toJsonStr(regParams))
							.execute();
			if (registerRes.isOk()) {
				getLoginRes(email, password, result);
			}
		} catch (Exception e) {
			log.info("操作异常: " + e.getMessage());
			result.setData("失败");
		}

		return result;
	}

	private HttpResponse getLoginRes(String email, String password, ApiResponse<String> result) {
		HashMap logParams = new HashMap<>();
		logParams.put("email", email);
		logParams.put("password", password);
		HttpResponse loginRes = HttpRequest.post(logUrl)
					.header("Content-Type", "application/json")
					.body(JSONUtil.toJsonStr(logParams))
					.execute();
		if (loginRes.isOk()) {
			JSONObject jsonResponse = JSONUtil.parseObj(loginRes.body());
			String token = jsonResponse.getStr("token");
			if (StringUtils.isNotEmpty(token)) {
				result.setData(redUrl + "token=" + token);
			}
		}
		return loginRes;
	}

}

package com.seewin.som.report.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 招商市场报告表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Getter
@Setter
public class ReportInvertMarketAddDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 0-业态维度 1 品类维度
     */
    private Integer dataType;

    /**
     * 业态ID
     */
    private Long commercialTypeCode;

    /**
     * 业态名称
     */
    private String commercialTypeName;

    /**
     * 一级品类ID
     */
    private Long categoryId;

    /**
     * 一级品类名称
     */
    private String categoryName;

    /**
     * 分析指标
     */
    private String indicator;

    /**
     * 分析指标类型（0-业态占比  1- 一级品类竞争度 2-门前客流 3-进店客流  4-门前客流流量成本  5-门前客流销售转化率  6-一级品类租售比 7-一级品类租管费单价  8-一级品类销售坪效 9-一级品类门前客流销售转化率  10一级品类门前客流  11-一级品类门前客流流量成本  12-一级品类销售笔数）
     */
    private Integer indicatorType;

    /**
     * 分析指标描述
     */
    private String indicatorRemark;

    /**
     * 本期数据
     */
    private BigDecimal currentPeriodData;

    /**
     * 同比数据
     */
    private BigDecimal yearOnYearData;

    /**
     * 同比率
     */
    private BigDecimal yearOnYearRate;

    /**
     * 环比数据
     */
    private BigDecimal monthOnMonthData;

    /**
     * 环比率
     */
    private BigDecimal monthOnMonthRate;

    /**
     * 均值
     */
    private BigDecimal meanValue;

    /**
     * 切尾均值
     */
    private BigDecimal trimmedMean;

    /**
     * 中位数
     */
    private BigDecimal median;

    /**
     * 众数
     */
    private BigDecimal mostFrequentValue;

    /**
     * 标准差
     */
    private BigDecimal standardDeviation;

    /**
     * 方差
     */
    private BigDecimal variance;

    /**
     * 中位数绝对偏差
     */
    private BigDecimal medianAbsoluteDeviation;

    /**
     * 极差
     */
    private BigDecimal dataRange;

    /**
     * 四分位差
     */
    private BigDecimal interquartileRange;

    /**
     * 第一四分位
     */
    private BigDecimal firstQuartile;

    /**
     * 第三四分位
     */
    private BigDecimal thirdQuartile;

    /**
     * 结果解读
     */
    private String resultInterpretation;
    private String chartData;
    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}

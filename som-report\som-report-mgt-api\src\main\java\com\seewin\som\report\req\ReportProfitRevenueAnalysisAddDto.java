package com.seewin.som.report.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 盈亏报告-收入分析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Getter
@Setter
public class ReportProfitRevenueAnalysisAddDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 房间id
     */
    private String storeId;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 总销售额-本期数据
     */
    private BigDecimal currentPeriod;

    /**
     * 总销售额-同比率
     */
    private BigDecimal currentPeriodSameRatio;

    /**
     * 总销售额-环比率
     */
    private BigDecimal currentPeriodRingRatio;

    /**
     * 总销售笔数（单）-本期数据
     */
    private BigDecimal totalNumber;

    /**
     * 总销售笔数（单）-同比率
     */
    private BigDecimal totalNumberSameRatio;

    /**
     * 总销售笔数（单）-环比率
     */
    private BigDecimal totalNumberRingRatio;

    /**
     * 总客单价（元/单）-本期数据
     */
    private BigDecimal totalPrice;

    /**
     * 总客单价（元/单）-同比率
     */
    private BigDecimal totalPriceSameRatio;

    /**
     * 总客单价（元/单）-环比率
     */
    private BigDecimal totalPriceRingRatio;

    /**
     * 线上销售额（元）-本期数据
     */
    private BigDecimal onlineVolume;

    /**
     * 线上销售额（元）-同比率
     */
    private BigDecimal onlineVolumeSameRatio;

    /**
     * 线上销售额（元）-环比率
     */
    private BigDecimal onlineVolumeRingRatio;

    /**
     * 线上销售笔数（单）-本期数据
     */
    private BigDecimal onlineNumber;

    /**
     * 线上销售笔数（单）-同比率
     */
    private BigDecimal onlineNumberSameRatio;

    /**
     * 线上销售笔数（单）-环比率
     */
    private BigDecimal onlineNumberRingRatio;

    /**
     * 线上客单价（元/单）-本期数据
     */
    private BigDecimal onlinePrice;

    /**
     * 线上客单价（元/单）-同比率
     */
    private BigDecimal onlinePriceSameRatio;

    /**
     * 线上客单价（元/单）-环比率
     */
    private BigDecimal onlinePriceRingRatio;

    /**
     * 线下销售额（元）-本期数据
     */
    private BigDecimal offlineVolume;

    /**
     * 线下销售额（元）-同比率
     */
    private BigDecimal offlineVolumeSameRatio;

    /**
     * 线下销售额（元）-环比率
     */
    private BigDecimal offlineVolumeRingRatio;

    /**
     * 线下客单价（元/单）-本期数据
     */
    private BigDecimal offlinePrice;

    /**
     * 线下客单价（元/单）-同比率
     */
    private BigDecimal offlinePriceSameRatio;

    /**
     * 线下客单价（元/单）-环比率
     */
    private BigDecimal offlinePriceRingRatio;
    /**
     * 线下销售笔数（单）-本期数据
     */
    private BigDecimal storeNumber;

    /**
     * 线下销售笔数（单）-同比率
     */
    private BigDecimal storeNumberSameRatio;

    /**
     * 线下销售笔数（单）-环比率
     */
    private BigDecimal storeNumberRingRatio;
    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 开始时间
     */
    private LocalDate startDate;
    /**
     * 结束时间
     */
    private LocalDate endDate;
}

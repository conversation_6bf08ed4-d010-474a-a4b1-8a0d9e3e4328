package com.seewin.som.report.service.impl;

import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.OptUser;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.resp.EntProjectGetVo;
import com.seewin.som.iot.provider.IotFlowDataProvider;
import com.seewin.som.report.provider.ReportFeeProvider;
import com.seewin.som.report.provider.ReportMasterSaleDataProvider;
import com.seewin.som.report.provider.ReportOperateProvider;
import com.seewin.som.report.provider.ReportStatisFlowDataProvider;
import com.seewin.som.report.req.ReportFeeSumGetDto;
import com.seewin.som.report.req.ReportMasterSaleDataListDto;
import com.seewin.som.report.req.ReportOperateGetDto;
import com.seewin.som.report.req.ReportStatisFlowDataListDto;
import com.seewin.som.report.resp.*;
import com.seewin.som.report.service.StoreReportService;
import com.seewin.som.report.utils.ValueUtils;
import com.seewin.som.report.vo.req.IotAnalyseReq;
import com.seewin.som.report.vo.req.IotStoreAnalyseReq;
import com.seewin.som.report.vo.req.RentAnalyseReq;
import com.seewin.som.report.vo.resp.*;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springdoc.core.providers.HateoasHalProvider;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;

@Service
public class StoreReportServiceImpl implements StoreReportService {

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-report-mgt")
    private ReportFeeProvider reportFeeProvider;

    @DubboReference(providedBy = "som-report-mgt")
    private ReportOperateProvider reportOperateProvider;

    @DubboReference(providedBy = "som-report-mgt")
    private ReportStatisFlowDataProvider reportStatisFlowDataProvider;

    @DubboReference(providedBy = "som-report-mgt")
    private ReportMasterSaleDataProvider reportMasterSaleDataProvider;

    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotFlowDataProvider iotFlowDataProvider;

    @Override
    public List<RentAnalyseResp> rentAnalyse(RentAnalyseReq req) {
        List<RentAnalyseResp> result = new ArrayList<>();
        LocalDate localDate = LocalDate.now();
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        if (Objects.isNull(optUser.getTenantId())) {
            return Collections.emptyList();
        }
        //未做企业级处理
//        if(optUser.getOrgLevel() != null && optUser.getOrgLevel() != 4)
//            return result;
        if (Objects.isNull(optUser.getTenantId())) {
            return Collections.emptyList();
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (int i = 5; i >= 0; i--) {
            String month = formatter.format(localDate.minusMonths(i));
            RentAnalyseResp resp = new RentAnalyseResp();
            resp.setMonth(month);
            if (req.getType() == 3) {
                ReportFeeSumGetDto dto = new ReportFeeSumGetDto();
                dto.setTenantId(optUser.getTenantId());
                dto.setMonth(localDate.minusMonths(i).getMonthValue());
                dto.setYear(localDate.minusMonths(i).getYear());
                ReportFeeSumGetVo getVo = reportFeeProvider.getSum(dto);
                if (getVo != null) {
                    Function<BigDecimal, BigDecimal> toNoNull = a -> a != null ? a : BigDecimal.ZERO;
                    resp.setTheoryFee(toNoNull.apply(getVo.getTheoryManage()).add(toNoNull.apply(getVo.getTheoryRent())).divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_DOWN));
                    resp.setActualFee(toNoNull.apply(getVo.getActualManage()).add(toNoNull.apply(getVo.getActualRent())).divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_DOWN));
                } else {
                    resp.setTheoryFee(new BigDecimal(0.00));
                    resp.setActualFee(new BigDecimal(0.00));
                }
            } else {
                Formatter format = new Formatter();
                EntProjectGetVo entProjectGetVo = entProjectProvider.get(optUser.getTenantId());
                Double tenantArea = Objects.isNull(entProjectGetVo) ? 1 : entProjectGetVo.getFablearea();

                ReportOperateGetDto getDto = new ReportOperateGetDto();
                getDto.setTenantId(optUser.getTenantId());
                getDto.setMonth(localDate.minusMonths(i).getMonthValue());
                getDto.setYear(localDate.minusMonths(i).getYear());
                Double area = 0.00;
                if (req.getType() == 1) {
                    getDto.setStatusList(Arrays.asList(2,3));
                    area = reportOperateProvider.getStoreAreaSum(getDto);
                    resp.setRent(new BigDecimal(Double.parseDouble(format.format("%.2f", area / tenantArea * 100).toString())));
                } else if (req.getType() == 2) {
                    getDto.setStatus(2);
                    area = reportOperateProvider.getStoreAreaSum(getDto);
                    resp.setOpening(new BigDecimal(Double.parseDouble(format.format("%.2f", area / tenantArea * 100).toString())));
                }
                format.close();
            }
            result.add(resp);
        }
        return result;
    }

    @Override
    public List<IotAnalyseResp> iotAnalyse(IotAnalyseReq req) {
        List<IotAnalyseResp> result = new ArrayList<>();
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        if (Objects.isNull(optUser.getTenantId())) {
            return Collections.emptyList();
        }
        LocalDate localDate = LocalDate.now();
        List<LocalDate> statisTimeList = new ArrayList<>();
        for (int i = 7; i >= 1; i--) {
            LocalDate statisTime = localDate.minusDays(i);
            statisTimeList.add(statisTime);
            statisTimeList.add(statisTime.minusDays(1));
            statisTimeList.add(statisTime.minusYears(1));
        }
        //客流
        Map<LocalDate, Long> entercountSumMap = new HashMap<>();
        ReportStatisFlowDataListDto flowDataListDto = new ReportStatisFlowDataListDto();
        flowDataListDto.setTenantId(optUser.getTenantId());
        flowDataListDto.setStatisTimeList(statisTimeList);
        flowDataListDto.setStatisType(0);
        List<ReportStatisFlowDataListVo> flowDataListVoList = reportStatisFlowDataProvider.list(flowDataListDto);
        if (!CollectionUtils.isEmpty(flowDataListVoList)) {
            for (ReportStatisFlowDataListVo vo : flowDataListVoList) {
                Long enterCount = vo.getEnterCount();
                if (entercountSumMap.containsKey(vo.getStatisTime())) {
                    enterCount += entercountSumMap.get(vo.getStatisTime());
                }
                entercountSumMap.put(vo.getStatisTime(), enterCount);
            }
        }
        //销售
        Map<LocalDate, BigDecimal> totalAmountSumMap = new HashMap<>();
        ReportMasterSaleDataListDto saleDataListDto = new ReportMasterSaleDataListDto();
        saleDataListDto.setTenantId(optUser.getTenantId());
        saleDataListDto.setSaleTimeList(statisTimeList);
        List<ReportMasterSaleDataListVo> saleDataListVoList = reportMasterSaleDataProvider.list(saleDataListDto);
        if (!CollectionUtils.isEmpty(saleDataListVoList)) {
            for (ReportMasterSaleDataListVo vo : saleDataListVoList) {
                BigDecimal totalAmount = vo.getTotalAmount();
                if (totalAmountSumMap.containsKey(vo.getSaleTime())) {
                    totalAmount = totalAmount.add(totalAmountSumMap.get(vo.getSaleTime()));
                }
                totalAmountSumMap.put(vo.getSaleTime(), totalAmount);
            }
        }
        for (int i = 7; i >= 1; i--) {
            Double pariPassu = 0.00;
            Double ringThan = 0.00;
            IotAnalyseResp resp = new IotAnalyseResp();
            resp.setDate(localDate.minusDays(i));
            if (req.getType() == 1) {
                //本期
                Long tenantFlow = entercountSumMap.containsKey(resp.getDate()) ? entercountSumMap.get(resp.getDate()) : 0L;
                resp.setTenantFlow(tenantFlow);
                //上一期，同比
                LocalDate lastDate = resp.getDate().minusDays(1);
                Long lastWeekFlow = entercountSumMap.containsKey(lastDate) ? entercountSumMap.get(lastDate) : 0L;
                //去年同期，环比
                LocalDate lastYearDate = resp.getDate().minusYears(1);
                Long lastYearFlow = entercountSumMap.containsKey(lastYearDate) ? entercountSumMap.get(lastYearDate) : 0L;
                if (lastYearFlow != 0)
                    pariPassu = Double.parseDouble(new Formatter().format("%.2f", (tenantFlow.doubleValue() - lastYearFlow.doubleValue()) / lastYearFlow.doubleValue() * 100).toString());
                if (lastWeekFlow != 0)
                    ringThan = Double.parseDouble(new Formatter().format("%.2f", (tenantFlow.doubleValue() - lastWeekFlow.doubleValue()) / lastWeekFlow.doubleValue() * 100).toString());
                resp.setPariPassu(pariPassu);
                resp.setRingThan(ringThan);
            } else if (req.getType() == 2) {
                BigDecimal totalAmountSum = totalAmountSumMap.containsKey(resp.getDate()) ? totalAmountSumMap.get(resp.getDate()) : BigDecimal.ZERO;
                resp.setSaleTotal(totalAmountSum.divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_DOWN));
                //前一周
                LocalDate lastDate = resp.getDate().minusDays(1);
                BigDecimal lastWeekTotalAmount = totalAmountSumMap.containsKey(lastDate) ? totalAmountSumMap.get(lastDate) : BigDecimal.ZERO;
                //前一个月
                LocalDate lastYearDate = resp.getDate().minusYears(1);
                BigDecimal lastYearTotalAmount = totalAmountSumMap.containsKey(lastYearDate) ? totalAmountSumMap.get(lastYearDate) : BigDecimal.ZERO;

                if (lastYearTotalAmount.compareTo(BigDecimal.ZERO) != 0)
                    pariPassu = (totalAmountSum.subtract(lastYearTotalAmount)).multiply(new BigDecimal(100)).divide(lastYearTotalAmount, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                if (lastWeekTotalAmount.compareTo(BigDecimal.ZERO) != 0)
                    ringThan = (totalAmountSum.subtract(lastWeekTotalAmount)).multiply(new BigDecimal(100)).divide(lastWeekTotalAmount, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                resp.setPariPassu(pariPassu);
                resp.setRingThan(ringThan);
            }
            result.add(resp);
        }
        return result;
    }

    @Override
    public List<IotStoreAnalyseResp> iotStoreAnalyse(IotStoreAnalyseReq req) {
        List<IotStoreAnalyseResp> result = new ArrayList<>();
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        LocalDate localDate = LocalDate.now();

        for (int i = 7; i >= 1; i--) {
            Formatter format = new Formatter();
            Double pariPassu = 0.00;
            Double ringThan = 0.00;
            IotStoreAnalyseResp resp = new IotStoreAnalyseResp();
            resp.setDate(localDate.minusDays(i));
            switch (req.getType()) {
                case 1: {
                    StoreFrontFlowResp flowResp = getStoreFrontFlow(optUser.getTenantId(), resp.getDate(), req.getRoomFcode(), req.getStoreId());

                    if (flowResp.getLastYearFrontFlow() > 0)
                        pariPassu = (BigDecimal.valueOf(flowResp.getFrontFlow()).subtract(BigDecimal.valueOf(flowResp.getLastYearFrontFlow()))).multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(flowResp.getLastYearFrontFlow()), 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                    if (flowResp.getLastWeekFrontFlow() > 0)
                        ringThan = (BigDecimal.valueOf(flowResp.getFrontFlow()).subtract(BigDecimal.valueOf(flowResp.getLastWeekFrontFlow()))).multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(flowResp.getLastWeekFrontFlow()), 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                    resp.setFrontFlow(flowResp.getFrontFlow());
                    resp.setPariPassu(pariPassu);
                    resp.setRingThan(ringThan);
                    break;
                }
                case 2: {
                    StoreTotalSaleResp saleResp = getStoreTotalSale(optUser.getTenantId(), resp.getDate(), req.getRoomFcode());
                    BigDecimal totalAmountSum = saleResp.getSale();
                    BigDecimal lastWeekTotalAmount = saleResp.getLastWeekSale();
                    BigDecimal lastYearTotalAmount = saleResp.getLastYearSale();

                    if (lastYearTotalAmount.compareTo(BigDecimal.ZERO) != 0)
                        pariPassu = (totalAmountSum.subtract(lastYearTotalAmount)).multiply(new BigDecimal(100)).divide(lastYearTotalAmount, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                    if (lastWeekTotalAmount.compareTo(BigDecimal.ZERO) != 0)
                        ringThan = (totalAmountSum.subtract(lastWeekTotalAmount)).multiply(new BigDecimal(100)).divide(lastWeekTotalAmount, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                    resp.setSaleTotal(totalAmountSum);
                    resp.setPariPassu(pariPassu);
                    resp.setRingThan(ringThan);
                    break;
                }
                case 3: {
                    StoreFrontFlowResp flowResp = getEnterStoreFlow(optUser.getTenantId(), resp.getDate(), req.getRoomFcode(), req.getStoreId());
                    StoreTotalOrderResp orderResp = getStoreTotalOrder(optUser.getTenantId(), resp.getDate(), req.getRoomFcode(), 0);

                    Double frontRate = 0.00;
                    if (flowResp.getFrontFlow().longValue() > 0)
                        frontRate = orderResp.getOrder().doubleValue() / flowResp.getFrontFlow().doubleValue();
                    Double lastWeekRate = 0.00;
                    if (flowResp.getLastWeekFrontFlow().longValue() > 0)
                        lastWeekRate = orderResp.getLastWeekOrder().doubleValue() / flowResp.getLastWeekFrontFlow();
                    Double lastYearRate = 0.00;
                    if (flowResp.getLastYearFrontFlow().longValue() > 0)
                        lastYearRate = orderResp.getLastYearOrder().doubleValue() / flowResp.getLastYearFrontFlow().doubleValue();

                    if (lastYearRate > 0)
                        pariPassu = BigDecimal.valueOf(lastYearRate).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                    if (lastWeekRate > 0)
                        ringThan = BigDecimal.valueOf(lastWeekRate).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                    if (frontRate > 0 ){
                        frontRate = BigDecimal.valueOf(frontRate).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                    }
                    resp.setFrontRate(frontRate);
                    resp.setPariPassu(pariPassu);
                    resp.setRingThan(ringThan);
                    break;
                }
                case 4: {
                    StoreTotalSaleResp saleResp = getStoreTotalSale(optUser.getTenantId(), resp.getDate(), req.getRoomFcode());
                    StoreTotalOrderResp orderResp = getStoreTotalOrder(optUser.getTenantId(), resp.getDate(), req.getRoomFcode(), 0);

                    BigDecimal unitValue = new BigDecimal(0.00);
                    if (orderResp.getOrder() > 0)
                        unitValue = BigDecimal.valueOf(saleResp.getSale().doubleValue() / orderResp.getOrder().doubleValue());
                    BigDecimal lastWeekValue = new BigDecimal(0.00);
                    if (orderResp.getLastWeekOrder() > 0)
                        lastWeekValue = BigDecimal.valueOf(saleResp.getLastWeekSale().doubleValue() / orderResp.getLastWeekOrder().doubleValue());
                    BigDecimal lastYearValue = new BigDecimal(0.00);
                    if (orderResp.getLastYearOrder() > 0)
                        lastYearValue = BigDecimal.valueOf(saleResp.getLastYearSale().doubleValue() / orderResp.getLastYearOrder().doubleValue());

                    if (lastYearValue.compareTo(BigDecimal.ZERO) != 0)
                        pariPassu = (unitValue.subtract(lastYearValue)).multiply(new BigDecimal(100)).divide(lastYearValue, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                    if (lastWeekValue.compareTo(BigDecimal.ZERO) != 0)
                        ringThan = (unitValue.subtract(lastWeekValue)).multiply(new BigDecimal(100)).divide(lastWeekValue, 2, BigDecimal.ROUND_HALF_DOWN).doubleValue();
                    resp.setUnitValue(unitValue.setScale(2, BigDecimal.ROUND_HALF_DOWN));
                    resp.setPariPassu(pariPassu);
                    resp.setRingThan(ringThan);
                    break;
                }
                default:
                    break;
            }
            result.add(resp);
            format.close();
        }
        return result;
    }

    public StoreFrontFlowResp getStoreFrontFlow(Long tenantId, LocalDate date, String fcode, String storeId) {
        StoreFrontFlowResp resp = new StoreFrontFlowResp();
        ReportStatisFlowDataListDto dto = new ReportStatisFlowDataListDto();
        dto.setTenantId(tenantId);
        dto.setStatisTime(date);
        dto.setStatisType(2);
        dto.setObjCode(fcode);
        dto.setStoreId(storeId);
        ReportStatisFlowDataGetVo reportStatisFlowDataGetVo = reportStatisFlowDataProvider.get(dto);
        Long frontFlow = Long.valueOf("0");

        if (reportStatisFlowDataGetVo != null)
            frontFlow = reportStatisFlowDataGetVo.getFrontCount();

        dto.setStatisTime(date.minusDays(1));
        ReportStatisFlowDataGetVo lastWeekGetVo = reportStatisFlowDataProvider.get(dto);
        Long lastWeekFlow = Long.valueOf("0");
        if (lastWeekGetVo != null)
            lastWeekFlow = lastWeekGetVo.getFrontCount();

        dto.setStatisTime(date.minusYears(1));
        ReportStatisFlowDataGetVo lastYearGetVo = reportStatisFlowDataProvider.get(dto);
        Long lastYearFlow = Long.valueOf("0");
        if (lastYearGetVo != null)
            lastYearFlow = lastYearGetVo.getFrontCount();

        resp.setFrontFlow(frontFlow);
        resp.setLastWeekFrontFlow(lastWeekFlow);
        resp.setLastYearFrontFlow(lastYearFlow);
        return resp;
    }

    public StoreFrontFlowResp getEnterStoreFlow(Long tenantId, LocalDate date, String fcode, String storeId) {
        StoreFrontFlowResp resp = new StoreFrontFlowResp();
        ReportStatisFlowDataListDto dto = new ReportStatisFlowDataListDto();
        dto.setTenantId(tenantId);
        dto.setStatisTime(date);
        dto.setStatisType(2);
        dto.setObjCode(fcode);
        dto.setStoreId(storeId);
        ReportStatisFlowDataGetVo reportStatisFlowDataGetVo = reportStatisFlowDataProvider.get(dto);
        Long frontFlow = Long.valueOf("0");

        if (reportStatisFlowDataGetVo != null)
            frontFlow = reportStatisFlowDataGetVo.getEnterCount();

        dto.setStatisTime(date.minusDays(1));
        ReportStatisFlowDataGetVo lastWeekGetVo = reportStatisFlowDataProvider.get(dto);
        Long lastWeekFlow = Long.valueOf("0");
        if (lastWeekGetVo != null)
            lastWeekFlow = lastWeekGetVo.getEnterCount();

        dto.setStatisTime(date.minusYears(1));
        ReportStatisFlowDataGetVo lastYearGetVo = reportStatisFlowDataProvider.get(dto);
        Long lastYearFlow = Long.valueOf("0");
        if (lastYearGetVo != null)
            lastYearFlow = lastYearGetVo.getEnterCount();

        resp.setFrontFlow(frontFlow);
        resp.setLastWeekFrontFlow(lastWeekFlow);
        resp.setLastYearFrontFlow(lastYearFlow);
        return resp;
    }

    public StoreTotalSaleResp getStoreTotalSale(Long tenantId, LocalDate date, String fcode) {
        StoreTotalSaleResp resp = new StoreTotalSaleResp();
        ReportMasterSaleDataListDto dto = new ReportMasterSaleDataListDto();
        dto.setTenantId(tenantId);
        dto.setSaleTime(date);
        dto.setObjCode(fcode);
        ReportMasterSaleDataGetVo getVo = reportMasterSaleDataProvider.get(dto);
        BigDecimal totalAmountSum = new BigDecimal(0.00);
        if (getVo != null)
            totalAmountSum = getVo.getTotalAmount();
        resp.setSale(totalAmountSum);

        dto.setSaleTime(date.minusDays(1));
        ReportMasterSaleDataGetVo lastWeekGetVo = reportMasterSaleDataProvider.get(dto);
        BigDecimal lastWeekTotalAmount = new BigDecimal(0.00);
        if (lastWeekGetVo != null)
            lastWeekTotalAmount = lastWeekGetVo.getTotalAmount();
        resp.setLastWeekSale(lastWeekTotalAmount);

        dto.setSaleTime(date.minusYears(1));
        ReportMasterSaleDataGetVo lastYearGetVo = reportMasterSaleDataProvider.get(dto);
        BigDecimal lastYearTotalAmount = new BigDecimal(0.00);
        if (lastYearGetVo != null)
            lastYearTotalAmount = lastYearGetVo.getTotalAmount();
        resp.setLastYearSale(lastYearTotalAmount);

        return resp;
    }

    public StoreTotalOrderResp getStoreTotalOrder(Long tenantId, LocalDate date, String fcode, Integer saleType) {
        StoreTotalOrderResp resp = new StoreTotalOrderResp();
        ReportMasterSaleDataListDto dto = new ReportMasterSaleDataListDto();
        dto.setTenantId(tenantId);
        dto.setSaleTime(date);
        dto.setObjCode(fcode);
        ReportMasterSaleDataGetVo getVo = reportMasterSaleDataProvider.get(dto);
        Integer order = 0;
        if (getVo != null){
            if (saleType==0){
                // 所有的销售总笔数
                order = getVo.getTotalOrder();
            }else if (saleType==1){
                // 门店销售笔数
                order = getVo.getStoreOrder();
            }
        }
        resp.setOrder(order);

        dto.setSaleTime(date.minusDays(1));
        ReportMasterSaleDataGetVo lastWeekGetVo = reportMasterSaleDataProvider.get(dto);
        Integer lastWeekOrder = 0;
        if (lastWeekGetVo != null){
            if (saleType==0){
                // 所有的销售总笔数
                lastWeekOrder = lastWeekGetVo.getTotalOrder();
            }else if (saleType==1){
                // 门店销售笔数
                lastWeekOrder = lastWeekGetVo.getStoreOrder();
            }
        }
        resp.setLastWeekOrder(lastWeekOrder);

        dto.setSaleTime(date.minusYears(1));
        ReportMasterSaleDataGetVo lastYearGetVo = reportMasterSaleDataProvider.get(dto);
        Integer lastYearOrder = 0;
        if (lastYearGetVo != null){
            if (saleType==0){
                // 所有的销售总笔数
                lastYearOrder = lastYearGetVo.getTotalOrder();
            }else if (saleType==1){
                // 门店销售笔数
                lastYearOrder = lastYearGetVo.getStoreOrder();
            }
        }
        resp.setLastYearOrder(lastYearOrder);

        return resp;
    }

    @Override
    public Long getRealFlow() {
        LocalDateTime endTime = LocalDateTime.now();
//        LocalDateTime startTime = endTime.minusHours(1);
        LocalDateTime startTime = endTime.truncatedTo(ChronoUnit.DAYS);
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        Long flow = iotFlowDataProvider.getTenantEnterCountByHour(optUser.getTenantId(), startTime, endTime);
        return flow;
    }
}

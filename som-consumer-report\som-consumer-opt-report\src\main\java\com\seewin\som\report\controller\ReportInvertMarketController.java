package com.seewin.som.report.controller;

import com.seewin.som.report.vo.req.*;
import com.seewin.som.report.vo.resp.ReportInvertMarketListItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.seewin.som.report.service.ReportInvertMarketService;
import com.seewin.consumer.data.ApiMethod;
import com.seewin.consumer.data.ApiResponse;
import com.seewin.consumer.vo.PageResp;

import com.seewin.som.report.vo.resp.ReportInvertMarketListItem;
import com.seewin.som.report.vo.resp.ReportInvertMarketGetResp;
import com.seewin.som.report.vo.resp.ReportInvertMarketAddResp;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <p>
 * 招商市场报告表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Tag(name = "招商市场报告表")
@RestController
@RequestMapping("reportInvertMarket")
public class ReportInvertMarketController {

	@Autowired
	private ReportInvertMarketService reportInvertMarketService;
	
	@Operation(summary = "招商市场报告表查询", description = "权限码：opt:report:reportinvertmarket:page")
	@PostMapping(ApiMethod.PAGE)
	public ApiResponse<PageResp<ReportInvertMarketListItem>> page(@RequestBody @Valid ReportInvertMarketListReq listReq) {
		ApiResponse<PageResp<ReportInvertMarketListItem>> result = new ApiResponse<>();

		PageResp<ReportInvertMarketListItem> pageResp = reportInvertMarketService.page(listReq);

		result.setData(pageResp);

		return result;
	}

	@Operation(summary = "招商市场报告表详情", description = "权限码：opt:report:reportinvertmarket:get")
	@GetMapping(ApiMethod.GET)
	public ApiResponse<ReportInvertMarketGetResp> get(@Valid ReportInvertMarketGetReq getReq) {
		ApiResponse<ReportInvertMarketGetResp> result = new ApiResponse<>();

		ReportInvertMarketGetResp getResp = reportInvertMarketService.get(getReq);

		result.setData(getResp);

		return result;
	}

	@Operation(summary = "招商市场报告表新增", description = "权限码：opt:report:reportinvertmarket:add")
	@PostMapping(ApiMethod.ADD)
	public ApiResponse<ReportInvertMarketAddResp> add(@RequestBody @Valid ReportInvertMarketAddReq addReq) {
		ApiResponse<ReportInvertMarketAddResp> result = new ApiResponse<>();

		ReportInvertMarketAddResp addResp = reportInvertMarketService.add(addReq);

		result.setData(addResp);

		return result;
	}

    @Operation(summary = "招商市场报告表编辑",description = "权限码：opt:report:reportinvertmarket:edit")
    @PostMapping(ApiMethod.EDIT)
    public ApiResponse edit(@RequestBody @Valid ReportInvertMarketEditReq editReq) {
        ApiResponse result = new ApiResponse<>();

        reportInvertMarketService.edit(editReq);

        return result;
    }
    
    @Operation(summary = "招商市场报告表删除",description = "权限码：opt:report:reportinvertmarket:del")
    @PostMapping(ApiMethod.DEL)
    public ApiResponse del(@RequestBody @Valid ReportInvertMarketDelReq delReq) {
        ApiResponse result = new ApiResponse<>();
        reportInvertMarketService.del(delReq);

        return result;
    }

	@Operation(summary = "招商市场报告表查询-不分页", description = "权限码：opt:report:reportinvertmarket:list")
	@PostMapping(ApiMethod.LIST)
	public ApiResponse<List<ReportInvertMarketListItem>> list(@RequestBody @Valid ReportInvertMarketListReq listReq) {
		ApiResponse<List<ReportInvertMarketListItem>> result = new ApiResponse<>();
		List<ReportInvertMarketListItem> pageResp = reportInvertMarketService.list(listReq);
		result.setData(pageResp);
		return result;
	}
}

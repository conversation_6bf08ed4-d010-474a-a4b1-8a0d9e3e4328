package com.seewin.som.report.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 盈亏报告-成本分析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Getter
@Setter
public class ReportProfitReq implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "租户Id")
    @NotBlank(message = "租户Id不能为空")
    private Long tenantId;
    /**
     * 门店id
     */
    @Schema(description = "门店id")
    @NotBlank(message = "门店id不能为空")
    private String storeId;
    /**
     * 年份
     */
    @Schema(description = "年份")
    private Integer year;
    /**
     * 月份
     */
    @Schema(description = "月份")
    private Integer month;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @NotBlank(message = "开始时间不能为空")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @NotBlank(message = "结束时间不能为空")
    private LocalDate endDate;
}

package com.seewin.som.report.controller;

import com.seewin.som.report.service.ReportInvertMarketService;
import com.seewin.som.report.service.ReportMobileFlowDataService;
import com.seewin.som.report.service.ReportProfitBusinessOverviewService;
import com.seewin.som.report.vo.req.MobileSaleDataAnalyseReq;
import com.seewin.som.report.vo.req.MobileFlowDataAnalyseReq;
import com.seewin.som.report.vo.req.ReportInvertMarketListReq;
import com.seewin.som.report.vo.req.ReportProfitReq;
import com.seewin.som.report.vo.resp.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.seewin.som.report.service.ReportMobileSaleDataService;
import com.seewin.consumer.data.ApiResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <p>
 * 主数据库销售数据 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@Tag(name = "主数据库销售数据")
@RestController
@RequestMapping("reportMobileSaleData")
public class ReportMobileSaleDataController {

	@Autowired
	private ReportMobileSaleDataService reportMobileSaleDataService;

	@Autowired
	private ReportMobileFlowDataService reportMobileFlowDataService;

	@Autowired
	private ReportProfitBusinessOverviewService overviewService;

	@Autowired
	private ReportInvertMarketService reportInvertMarketService;
	
	@Operation(summary = "销售分析", description = "权限码：cus:report:reportmobilesaledata:saleAnalyse")
	@PostMapping("saleAnalyse")
	public ApiResponse<MobileSaleDataAnalyseResp> saleAnalyse(@RequestBody @Valid MobileSaleDataAnalyseReq req) {
		ApiResponse<MobileSaleDataAnalyseResp> result = new ApiResponse<>();
		MobileSaleDataAnalyseResp resp = reportMobileSaleDataService.saleAnalyse(req);
		result.setData(resp);
		return result;
	}

	@Operation(summary = "客流分析", description = "权限码：cus:report:reportmobilesaledata:flowAnalyse")
	@PostMapping("flowAnalyse")
	public ApiResponse<MobileFlowDataAnalyseResp> flowAnalyse(@RequestBody @Valid MobileFlowDataAnalyseReq req) {
		ApiResponse<MobileFlowDataAnalyseResp> result = new ApiResponse<>();
		MobileFlowDataAnalyseResp resp = reportMobileFlowDataService.saleAnalyse(req);
		result.setData(resp);
		return result;
	}

	@Operation(summary = "店铺盈亏报告", description = "权限码：cus:report:reportmobilesaledata:getReportProfit")
	@PostMapping("getReportProfit")
	public ApiResponse<ReportGetProfitCostAnalysisResp> getReportProfit(@RequestBody ReportProfitReq req) {
		ApiResponse<ReportGetProfitCostAnalysisResp> result = new ApiResponse<>();
		ReportGetProfitCostAnalysisResp pageResp = overviewService.getReportProfit(req);
		result.setData(pageResp);
		return result;
	}

	@Operation(summary = "销售预测", description = "权限码：cus:report:reportmobilesaledata:salePredict")
	@PostMapping("salePredict")
	public ApiResponse<List<ReportPredictDataVo>> salePredict(@RequestBody @Valid MobileSaleDataAnalyseReq req) {
		ApiResponse<List<ReportPredictDataVo>> result = new ApiResponse<>();
		List<ReportPredictDataVo> resp = reportMobileSaleDataService.salePredict(req);
		result.setData(resp);
		return result;
	}

	@Operation(summary = "客流预测", description = "权限码：cus:report:reportmobilesaledata:flowPredict")
	@PostMapping("flowPredict")
	public ApiResponse<List<ReportPredictDataVo>> flowPredict(@RequestBody @Valid MobileSaleDataAnalyseReq req) {
		ApiResponse<List<ReportPredictDataVo>> result = new ApiResponse<>();
		List<ReportPredictDataVo> resp = reportMobileFlowDataService.flowPredict(req);
		result.setData(resp);
		return result;
	}
	@Operation(summary = "拓店市场报告", description = "权限码：cus:report:reportmobilesaledata:getInvertMarketReport")
	@PostMapping("getInvertMarketReport")
	public ApiResponse<List<ReportInvertMarketListItem>> getInvertMarketReport(@RequestBody @Valid ReportInvertMarketListReq listReq) {
		ApiResponse<List<ReportInvertMarketListItem>> result = new ApiResponse<>();
		List<ReportInvertMarketListItem> pageResp = reportInvertMarketService.list(listReq);
		result.setData(pageResp);
		return result;
	}
}

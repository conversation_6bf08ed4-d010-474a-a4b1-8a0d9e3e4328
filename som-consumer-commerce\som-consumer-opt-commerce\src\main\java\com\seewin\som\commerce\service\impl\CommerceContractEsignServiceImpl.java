package com.seewin.som.commerce.service.impl;


import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.alibaba.nacos.shaded.com.google.gson.JsonArray;
import com.alibaba.nacos.shaded.com.google.gson.JsonElement;
import com.alibaba.nacos.shaded.com.google.gson.JsonObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.som.commerce.provider.*;
import com.seewin.som.commerce.req.CommerceContractPrintEditDto;
import com.seewin.som.commerce.req.CommerceContractTemplateFieldListDto;
import com.seewin.som.commerce.resp.CommerceContractPrintGetVo;
import com.seewin.som.commerce.resp.CommerceContractPrintOaVo;
import com.seewin.som.commerce.resp.CommerceContractTemplateFieldListVo;
import com.seewin.som.commerce.resp.CommerceContractTemplateGetVo;
import com.seewin.som.commerce.service.CommerceContracEsignService;
import com.seewin.som.commerce.service.CommerceContractPrintService;
import com.seewin.som.commerce.utils.BigDecimalUtil;
import com.seewin.som.commerce.utils.Esign.comm.EsignFileBean;
import com.seewin.som.commerce.utils.Esign.comm.EsignHttpHelper;
import com.seewin.som.commerce.utils.Esign.comm.EsignHttpResponse;
import com.seewin.som.commerce.utils.Esign.constant.EsignDemoConfig;
import com.seewin.som.commerce.utils.Esign.enums.EsignHeaderConstant;
import com.seewin.som.commerce.utils.Esign.enums.EsignRequestType;
import com.seewin.som.commerce.utils.Esign.exception.EsignDemoException;
import com.seewin.som.commerce.utils.MinioFileUtils;
import com.seewin.som.commerce.utils.PdfPageCountUtils;
import com.seewin.som.commerce.utils.WordUtils;
import com.seewin.som.commerce.vo.req.*;
import com.seewin.som.commerce.vo.resp.CommerceContractPrintOaSealResp;
import com.seewin.som.commerce.vo.resp.CommerceContractPrintReceiptSealResp;
import com.seewin.som.commerce.vo.resp.CommerceContractPrintSealResp;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.rent.provider.RentVoucherTenantInfoProvider;
import com.seewin.som.storage.provider.SysFileProvider;
import com.seewin.som.storage.resp.SysFileGetVo;
import com.seewin.system.service.FileService;
import com.seewin.util.bean.BeanUtils;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <p>
 * 招商合同打印表 电子签章服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Service
@Slf4j
public class CommerceContractEsignServiceImpl implements CommerceContracEsignService {

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractPrintProvider commerceContractPrintProvider;

    @Autowired
    private FileService fileService;

    @Autowired
    private CommerceContractPrintService commerceContractPrintService;

    @Autowired
    private MinioFileUtils minioFileUtils;

    @Autowired
    private PdfPageCountUtils pdfPageCountUtils;

    @Value("${esign.app.host}")
    private  String eSignHost;
    @Value("${esign.app.id}")
    private  String eSignAppId;
    @Value("${esign.app.secret}")
    private  String eSignAppSecret;
    @Value("${esign.app.notifyUrl}")
    private String esignNotifyUrl;
    public  String fileId = null;
    public  String signFlowId = null;

    /**
     * <p>上传附件并签署<br>
     * 整体流程：
     * 1. 先拿到原先导出方法的输出流，转成临时文件存储后（方法结束时会将临时文件删除），再调用E签宝上传文件接口（实际上传需要调用两个接口）
     * 2. 上传完成后查看文件状态是否准备好 ，如果是，则调用签署接口，如果否，则循环查看状态，直到成功或失败
     * 3. 调用签署接口，如果成功，则保存signFlowId并更新合同签署状态为签署中
     * 4. 发送短信至签署人，等待签署（已签署和拒签都代表着签署完成，拒签没有重新发起机制）
     * 5. 签署完成之后E签宝调用回调接口，更新合同签署状态为已签署
     * @param response,uploadReq
     * @return
     */
    @Override
    public CommerceContractPrintSealResp uploadFile(HttpServletResponse response, CommerceContractPrintUploadFileReq uploadReq) throws InterruptedException, EsignDemoException, IOException {
        CommerceContractPrintSealResp sealResp = new CommerceContractPrintSealResp();
        File file = null;
        String filePath = null;

        List<File> files  = new ArrayList<>();
        List<Map<String,Object>> fileParamList  = new ArrayList<>();

        try {
            //根据ID拿到合同导出的数据信息
            CommerceContractPrintGetVo contractPrintGetVo = commerceContractPrintProvider.get(uploadReq.getId());
            // 做一些基础的必填校验
            checkTips(contractPrintGetVo);
            // 根据sameContractId拿到同一份合同下的所有文件
            List<CommerceContractPrintGetVo> contractPrintGetVos = commerceContractPrintProvider.getAllFileBySameContractId(contractPrintGetVo.getSameContractId());

            // 用于保存所有文件的 fileId
            Map<Long,String> fileIdMap = new HashMap<>();


            for (CommerceContractPrintGetVo vo : contractPrintGetVos) {
                CommerceContractPrintGetReq commerceContractPrintGetReq = new CommerceContractPrintGetReq();
                commerceContractPrintGetReq.setId(vo.getId());

                // 调用E签宝的上传文件接口
                file = commerceContractPrintService.expEsign(response, commerceContractPrintGetReq);
                files.add(file);
                response.setHeader("Content-Disposition", null);
                response.setHeader("Content-Type", "application/json");
                filePath = file.getAbsolutePath();
                log.info("上传文件的绝对路径: {}", filePath);
                String fileName = file.getName();
                String fileType = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
                log.info("上传文件的类型: {}", fileType);

                //获取文件id以及文件上传地址
                EsignHttpResponse getUploadUrl = getUploadUrl(filePath,fileType);
                Gson gson = new Gson();
                JsonObject getUploadUrlJsonObject = gson.fromJson(getUploadUrl.getBody(), JsonObject.class);
                // e签宝正常返回的code 是 0   其他则为有报错
                if (!"0".equals(getUploadUrlJsonObject.get("code").getAsString())) {
                    throw new ServiceException("E签宝返回错误: " + getUploadUrlJsonObject.get("message").getAsString());
                }
                JsonObject data = getUploadUrlJsonObject.getAsJsonObject("data");
                //文件id后续发起签署使用
                fileId = data.get("fileId").getAsString();
                String fileUploadUrl = data.get("fileUploadUrl").getAsString();

                log.info("获取文件id以及文件上传地址成功，文件id: {}", fileId);
                log.info("上传链接: {}", fileUploadUrl);

                //文件上传
                EsignHttpResponse uploadFileResponse = uploadFile(fileUploadUrl, filePath);
                JsonObject uploadFileResponseJsonObject = gson.fromJson(uploadFileResponse.getBody(), JsonObject.class);
                if (!"0".equals(uploadFileResponseJsonObject.get("errCode").getAsString())) {// 该接口返回体和别人不一样 ， 别的是code  data message  这个是 errCode msg
                    throw new ServiceException("E签宝返回错误: " + uploadFileResponseJsonObject.get("msg").getAsString());
                }
                String code = uploadFileResponseJsonObject.get("errCode").getAsString();
                log.info("文件上传成功，状态码: {}", code);

                //文件上传成功后文件会有一个异步处理过程，建议轮询文件状态，正常后发起签署
                //查询文件上传状态
                int i = 0;
                while (i < 3) {
                    EsignHttpResponse fileStatus = getFileStatus(fileId);
                    JsonObject fileStatusJsonObject = gson.fromJson(fileStatus.getBody(), JsonObject.class);
                    if (!"0".equals(fileStatusJsonObject.get("code").getAsString())) {
                        throw new ServiceException("E签宝返回错误: " + fileStatusJsonObject.get("message").getAsString());
                    }
                    String status = fileStatusJsonObject.getAsJsonObject("data").get("fileStatus").getAsString();
                    log.info("查询文件状态执行第{}次", i + 1);
                    if ("2".equalsIgnoreCase(status) || "5".equalsIgnoreCase(status)) {
                        log.info("文件上传成功，文件ID为：{}，文件状态为: {}", fileId,status);
                        fileIdMap.put(vo.getId(),fileId);
                        // 指定盖章位置的关键词
                        List<String> keywords = new ArrayList<>();
                        if(contractPrintGetVo.getLessorKeyword() != null){
                            keywords.add(contractPrintGetVo.getLessorKeyword());
                        }
                        if(contractPrintGetVo.getRenterKeyword() != null){
                            keywords.add(contractPrintGetVo.getRenterKeyword());
                        }

                        //请求盖章位置接口
                        EsignHttpResponse getKeyWordPositions = getKeyWordPositions(fileId, keywords);
                        JsonObject getKeyWordPositionsJsonObject = gson.fromJson(getKeyWordPositions.getBody(), JsonObject.class);
                        if (!"0".equals(getKeyWordPositionsJsonObject.get("code").getAsString())) {
                            throw new ServiceException("E签宝返回错误: " + getKeyWordPositionsJsonObject.get("message").getAsString());
                        }
                        JsonObject getKeyWordPositionsData = getKeyWordPositionsJsonObject.getAsJsonObject("data");
                        JsonArray keywordPositions = getKeyWordPositionsData.getAsJsonArray("keywordPositions");

                        // 创建签署需要的参数map
                        List<Map<String, Object>> reqMap = createSignRequestMap(fileId, contractPrintGetVo, keywordPositions);
                        Map<String, Object> fileParam = new HashMap<>();
                        fileParam.put("reqMap", reqMap);
                        fileParamList.add(fileParam);
                        log.info("文件准备完成！该文件组装后的参数：{}",fileParamList);

                        break;
                    }
                    log.info("文件未准备完成,等待两秒重新查询");
                    TimeUnit.SECONDS.sleep(2);
                    i++;
                }
            }

            log.info("所有签署文件准备完成！组装后的参数：{}",fileParamList);


            // 发起签署
            Gson gson = new Gson();
            EsignHttpResponse createByFile = createByFile(fileParamList);
            JsonObject createByFileJsonObject = gson.fromJson(createByFile.getBody(), JsonObject.class);
            if (!"0".equals(createByFileJsonObject.get("code").getAsString())) {
                throw new ServiceException("E签宝返回错误: " + createByFileJsonObject.get("message").getAsString());
            }
            JsonObject createByFileData = createByFileJsonObject.getAsJsonObject("data");
            signFlowId = createByFileData.get("signFlowId").getAsString();
            log.info("发起签署成功,签署流程id: {}", signFlowId);
            // 把生成的signFlowId 存入数据库中
            for (CommerceContractPrintGetVo printGetVo : contractPrintGetVos) {
                String fileIdMapFileId = fileIdMap.get(printGetVo.getId());
                printGetVo.setFileId(fileIdMapFileId);
                printGetVo.setSignFlowId(signFlowId);
                printGetVo.setSignStatus(1);//签署中
                CommerceContractPrintEditDto dto = BeanUtils.copyProperties(printGetVo, CommerceContractPrintEditDto.class);
                //设置修改人信息
                User curUser = ApiUtils.getUser(User.class);
                dto.setUpdateBy(curUser.getUserId());
                dto.setUpdateUser(curUser.getUserName());
                dto.setUpdateUserName(curUser.getRealName());
                commerceContractPrintProvider.edit(dto);
            }
            sealResp.setCode(200);
            sealResp.setMsg("成功发起电子签章！");
            sealResp.setData(null);

        } catch (ServiceException e){
            sealResp.setCode(9009);
            sealResp.setMsg(e.getMessage());
            sealResp.setData(null);
            return sealResp;
        } finally  {
            for (File file1 : files) {
                // 确保文件被删除
                if (file1 != null && file1.exists()) {
                    boolean isDeleted = file1.delete();
                    if (isDeleted) {
                        log.info("文件已成功删除: {}", filePath);
                    } else {
                        log.error("文件删除失败: {}", filePath);
                    }
                }
            }

        }
        return  sealResp;
    }
    /**
     * <p>回调接口<br>
     *
     * @param response,esignCallBackReq
     */
    @Override
    public void esignCallBack(HttpServletResponse response, CommerceContractPrintEsignCallBackReq esignCallBackReq) {
        // 获取签署结果
        String action = esignCallBackReq.getAction();
        Integer signOrder = esignCallBackReq.getSignOrder();
        Integer signResult = esignCallBackReq.getSignResult();

        log.info("E签宝回调 - action: {}, signOrder: {}, signResult: {}", action, signOrder, signResult);
        //根据流程ID拿到合同导出的数据信息
        CommerceContractPrintGetVo contractPrintGetVo = commerceContractPrintProvider.selectSignInfoBySignFlowId(esignCallBackReq.getSignFlowId());
        Integer signType = contractPrintGetVo.getSignType();

        // 单方签署   只要是SIGN_MISSON_COMPLETE状态则代表签署完成
        // signResult等于2表示签署成功，等于4表示拒签
        if(signType == 0 && "SIGN_MISSON_COMPLETE".equals(action) ){
            // 根据签署结果设置合同状态
            Integer signStatus = 1;
            if (signResult == 2 ) {
                signStatus = 2;
            } else if (signResult == 4) {
                signStatus = 3;
            }
            log.info("单方签署，更新电章状态 - signFlowId: {}, signStatus: {}", esignCallBackReq.getSignFlowId(), signStatus);
            // 更新合同状态到数据库
            commerceContractPrintProvider.updateSignStatusBySignFlowId(esignCallBackReq.getSignFlowId(),signStatus);
        }

        //  双方签署   遇到拒签直接修改了
        //  signOrder等于2表示是第二签署方进行签署动作时才做处理（因为签署整个流程都会发送请求，包括第一方为甲方时的自动签署的动作，所以需要用signOrder为2限制下）
        if( (signType == 1 || signType == 2) && "SIGN_MISSON_COMPLETE".equals(action) && signResult == 4  ){
            // 根据签署结果设置合同状态
            Integer signStatus = 3;
            log.info("双方签署，第一签署人拒签，更新电章状态为拒签 - signFlowId: {}, signStatus: {}", esignCallBackReq.getSignFlowId(), signStatus);
            // 更新合同状态到数据库
            commerceContractPrintProvider.updateSignStatusBySignFlowId(esignCallBackReq.getSignFlowId(),signStatus);
        }else if((signType == 1 || signType == 2) && "SIGN_MISSON_COMPLETE".equals(action) && signOrder == 2 ){
            // 根据签署结果设置合同状态
            Integer signStatus = 1;
            if (signResult == 2 ) {
                signStatus = 2;
            } else if (signResult == 4) {
                signStatus = 3;
            }
            log.info("双方签署，第二签署人签署完成或拒签，流程结束，更新电章状态 - signFlowId: {}, signStatus: {}", esignCallBackReq.getSignFlowId(), signStatus);
            // 更新合同状态到数据库
            commerceContractPrintProvider.updateSignStatusBySignFlowId(esignCallBackReq.getSignFlowId(),signStatus);
        }

    }


    @Override
    public CommerceContractPrintOaSealResp oaUploadFile(HttpServletResponse response, CommerceContractPrintOaUploadFileReq uploadReq) throws InterruptedException, EsignDemoException, IOException {

        CommerceContractPrintOaSealResp sealResp = new CommerceContractPrintOaSealResp();
        File file = null;
        String filePath = null;

        List<File> files  = new ArrayList<>();
        List<Map<String,Object>> fileParamList  = new ArrayList<>();

        // 拿到请求中的fileList   里面有文件映射ID 、 文件输入流
        List<CommerceContractPrintOaUploadFileReq.FileItem> fileList = uploadReq.getFileList();

        String oaSignFlowId  = null; // 存储E签宝返回的流程ID

        try {
            // TODO 做点校验

            // 用于保存所有文件的 fileId
            Map<String,String> fileIdMap = new HashMap<>();

            for (CommerceContractPrintOaUploadFileReq.FileItem fileItem : fileList) {
                String oaFileId = null;  // 存储E签宝返回的文件ID

                CommerceContractPrintOaVo commerceContractPrintOaVo = new CommerceContractPrintOaVo();
                byte[] fileByte = fileItem.getFileByte();
                // 创建临时文件   TODO 传输时需要有文件名 提取后缀
                String fileName = fileItem.getFileName();
                String fileType = "";
                int dotIndex = fileName.lastIndexOf('.');
                if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
                    fileType = fileName.substring(dotIndex + 1).toLowerCase();
                }

                File tempFile = File.createTempFile("temp_file_", "."+fileType);
                tempFile.deleteOnExit();

                // 使用 ByteArrayInputStream 包装 byte[]
                try (InputStream byteArrayInputStream = new ByteArrayInputStream(fileByte);
                     FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = byteArrayInputStream.read(buffer)) != -1) {
                        fileOutputStream.write(buffer, 0, bytesRead);
                    }

                    log.info("文件已成功写入临时文件: {}", tempFile.getAbsolutePath());

                } catch (IOException e) {
                    log.error("写入临时文件失败", e);
                    throw e;
                }

                // 调用E签宝的上传文件接口
                file = tempFile;
                files.add(file);
                filePath = file.getAbsolutePath();
                log.info("上传文件的绝对路径: {}", filePath);
                log.info("上传文件的类型: {}", fileType);

                // 拿到pdf文件的总页数  pdf格式的文件默认任意位置盖章 需要拿到pdf文件的总页数
                int pageCount = 0;
                if("pdf".equals(fileType)){
                    pageCount = pdfPageCountUtils.getPageCount(filePath);
                }


                //获取文件id以及文件上传地址
                EsignHttpResponse getUploadUrl = getUploadUrl(filePath,fileType);
                Gson gson = new Gson();
                JsonObject getUploadUrlJsonObject = gson.fromJson(getUploadUrl.getBody(), JsonObject.class);
                // e签宝正常返回的code 是 0   其他则为有报错
                if (!"0".equals(getUploadUrlJsonObject.get("code").getAsString())) {
                    throw new ServiceException("E签宝返回错误: " + getUploadUrlJsonObject.get("message").getAsString());
                }
                JsonObject data = getUploadUrlJsonObject.getAsJsonObject("data");
                //文件id后续发起签署使用
                oaFileId = data.get("fileId").getAsString();
                String fileUploadUrl = data.get("fileUploadUrl").getAsString();

                log.info("获取文件id以及文件上传地址成功，文件id: {}", oaFileId);
                log.info("上传链接: {}", fileUploadUrl);

                //文件上传
                EsignHttpResponse uploadFileResponse = uploadFile(fileUploadUrl, filePath);
                JsonObject uploadFileResponseJsonObject = gson.fromJson(uploadFileResponse.getBody(), JsonObject.class);
                if (!"0".equals(uploadFileResponseJsonObject.get("errCode").getAsString())) {// 该接口返回体和别人不一样 ， 别的是code  data message  这个是 errCode msg
                    throw new ServiceException("E签宝返回错误: " + uploadFileResponseJsonObject.get("msg").getAsString());
                }
                String code = uploadFileResponseJsonObject.get("errCode").getAsString();
                log.info("文件上传成功，状态码: {}", code);

                //文件上传成功后文件会有一个异步处理过程，建议轮询文件状态，正常后发起签署
                //查询文件上传状态
                int i = 0;
                while (i < 3) {
                    EsignHttpResponse fileStatus = getFileStatus(oaFileId);
                    JsonObject fileStatusJsonObject = gson.fromJson(fileStatus.getBody(), JsonObject.class);
                    if (!"0".equals(fileStatusJsonObject.get("code").getAsString())) {
                        throw new ServiceException("E签宝返回错误: " + fileStatusJsonObject.get("message").getAsString());
                    }
                    String status = fileStatusJsonObject.getAsJsonObject("data").get("fileStatus").getAsString();
                    log.info("查询文件状态执行第{}次", i + 1);
                    if ("2".equalsIgnoreCase(status) || "5".equalsIgnoreCase(status)) {
                        log.info("文件上传成功，文件ID为：{}，文件状态为: {}", oaFileId,status);
                        // 将文件映射id和文件id存入map中  前者是OA传来的ID  后者是E签宝返回的ID
                        fileIdMap.put(fileItem.getFileMapId(),oaFileId);

                        if("pdf".equals(fileType)){// pdf格式默认任意位置盖章、不显示日期
                            // 创建签署需要的参数map
                            List<Map<String, Object>> reqMap = createOaSignRequestMap(oaFileId, uploadReq,fileItem, null, null,fileType,pageCount);
                            Map<String, Object> fileParam = new HashMap<>();
                            fileParam.put("reqMap", reqMap);
                            fileParamList.add(fileParam);
                            log.info("文件准备完成！该文件组装后的参数：{}",fileParamList);
                        }else{
                            // 指定盖章位置的关键词
                            List<String> keywords = new ArrayList<>();
                            keywords.add("甲方（签章）");

                            // 关键词类型 不同关键词对应着不同的位置  0 甲方（签章） 1 （主体签章）
                            String keyWordType = "0";

                            //请求盖章位置接口
                            EsignHttpResponse getKeyWordPositions = getKeyWordPositions(oaFileId, keywords);
                            JsonObject getKeyWordPositionsJsonObject = gson.fromJson(getKeyWordPositions.getBody(), JsonObject.class);
                            if (!"0".equals(getKeyWordPositionsJsonObject.get("code").getAsString())) {
                                throw new ServiceException("E签宝返回错误: " + getKeyWordPositionsJsonObject.get("message").getAsString());
                            }
                            JsonObject getKeyWordPositionsData = getKeyWordPositionsJsonObject.getAsJsonObject("data");
                            JsonArray keywordPositions = getKeyWordPositionsData.getAsJsonArray("keywordPositions");
                            if(keywordPositions.get(0).getAsJsonObject().getAsJsonArray("positions").size() == 0){
                                // 如果根据甲方（签章）没有找到的话就用（主体签章）去找
                                keywords.clear();
                                keywords.add("（主体签章）");
                                keyWordType = "1";
                                //请求盖章位置接口
                                EsignHttpResponse getKeyWordPositions1 = getKeyWordPositions(oaFileId, keywords);
                                JsonObject getKeyWordPositionsJsonObject1 = gson.fromJson(getKeyWordPositions1.getBody(), JsonObject.class);
                                if (!"0".equals(getKeyWordPositionsJsonObject1.get("code").getAsString())) {
                                    throw new ServiceException("E签宝返回错误: " + getKeyWordPositionsJsonObject1.get("message").getAsString());
                                }
                                JsonObject getKeyWordPositionsData1 = getKeyWordPositionsJsonObject1.getAsJsonObject("data");
                                keywordPositions = getKeyWordPositionsData1.getAsJsonArray("keywordPositions");
                                if(keywordPositions.get(0).getAsJsonObject().getAsJsonArray("positions").size() == 0){
                                    throw new ServiceException("没有在"+fileItem.getFileName()+"中找到关键词！文件中必须含有甲方（签章）或（主体签章）");
                                }
                            }

                            // 创建签署需要的参数map
                            List<Map<String, Object>> reqMap = createOaSignRequestMap(oaFileId, uploadReq,fileItem, keywordPositions, keyWordType,null,0);
                            Map<String, Object> fileParam = new HashMap<>();
                            fileParam.put("reqMap", reqMap);
                            fileParamList.add(fileParam);
                            log.info("文件准备完成！该文件组装后的参数：{}",fileParamList);
                        }
                        break;
                    }
                    log.info("文件未准备完成,等待两秒重新查询");
                    TimeUnit.SECONDS.sleep(2);
                    i++;
                }
            }

            log.info("所有签署文件准备完成！组装后的参数：{}",fileParamList);



            // 发起签署
            Gson gson = new Gson();
            EsignHttpResponse createByFile = createByOaFile(fileParamList);
            JsonObject createByFileJsonObject = gson.fromJson(createByFile.getBody(), JsonObject.class);
            if (!"0".equals(createByFileJsonObject.get("code").getAsString())) {
                throw new ServiceException("E签宝返回错误: " + createByFileJsonObject.get("message").getAsString());
            }
            JsonObject createByFileData = createByFileJsonObject.getAsJsonObject("data");
            oaSignFlowId = createByFileData.get("signFlowId").getAsString();
            log.info("发起签署成功,签署流程id: {}", oaSignFlowId);

            // 创建返回的结构
            List<CommerceContractPrintOaVo> result = new ArrayList<>();
            //发起签署之后 响应需要时间  直到签署完成后  再返回结果
            int i = 0;
            while (i < 3) {

                //请求地址
                //下载已签署的文件及附属材料。流程结束后,获取签署完成的文件以及相关附属材料的下载链接。未签署完成的流程，无法下载相关文件，否则会报错："流程非签署完成状态，不允许下载文档"。
                String apiaddr = "/v3/sign-flow/"+ oaSignFlowId +"/file-download-url";
                //请求参数body体,json格式。get或者delete请求时jsonString传空json:"{}"或者null
                String jsonParm = null;
                //请求方法
                EsignRequestType requestType= EsignRequestType.GET;
                //生成签名鉴权方式的的header
                Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId,eSignAppSecret,jsonParm,requestType.name(),apiaddr,true);
                //发起接口请求
                EsignHttpResponse esignHttpResponse = EsignHttpHelper.doCommHttp(eSignHost, apiaddr, requestType, jsonParm, header, true);
                JsonObject fileDownloadUrlJsonObject = gson.fromJson(esignHttpResponse.getBody(), JsonObject.class);
                if (!"1437518".equals(fileDownloadUrlJsonObject.get("code").getAsString())) {
                    JsonObject fileDataDownloadUrlObject = fileDownloadUrlJsonObject.getAsJsonObject("data");//获取body体里的“data”字段
                    //获取已签署文件的下载链接
                    JsonArray eSignResponseFiles = fileDataDownloadUrlObject.get("files").getAsJsonArray();//获取fileDownloadUrlObject里的“files”字段
                    // 遍历 eSignResponseFiles 数组，查找匹配的 fileId
                    for (int k = 0; k < eSignResponseFiles.size(); k++) {
                        JsonObject fileJsonObject = eSignResponseFiles.get(k).getAsJsonObject();
                        String fileId = fileJsonObject.get("fileId").getAsString();
                        String downloadUrl = fileJsonObject.get("downloadUrl").getAsString();

                        // fileIdMap 中的结构是  fileMapId（OA传来的ID）: fileId（E签宝生成的ID） 所以需要比对的是value
                        for (Map.Entry<String, String> entry : fileIdMap.entrySet()) {
                            String fileMapId = entry.getKey();//OA传来的ID
                            String expectedFileId = entry.getValue();//E签宝生成的ID

                            if (fileId.equals(expectedFileId)) {
                                CommerceContractPrintOaVo vo = new CommerceContractPrintOaVo();
                                vo.setFileId(fileMapId); // 返回的是OA传来的ID
                                vo.setDownloadUrl(downloadUrl);
                                // 根据下载链接去拿到文件并转成字节数组返回
                                URL url = new URL(downloadUrl);
                                byte[] byteArray = null;
                                try (InputStream inputStream = url.openStream()) {
                                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                                    byte[] buffer = new byte[1024];
                                    int bytesRead;
                                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                                        byteArrayOutputStream.write(buffer, 0, bytesRead);
                                    }
                                    byteArray = byteArrayOutputStream.toByteArray();

                                    // 上传到minio中
                                    log.info("开始上传到Minio");
                                    String originalFileName = null;
                                    String fileNameWithoutExtension = null;
                                    String objectName = null;
                                    for (CommerceContractPrintOaUploadFileReq.FileItem item : fileList) {
                                        if (fileMapId.equals(item.getFileMapId())) {
                                            originalFileName = item.getFileName();
                                            break;
                                        }
                                    }
                                    if (originalFileName != null) {
                                        fileNameWithoutExtension = originalFileName.replaceFirst("\\.[^\\.]+$", "");
                                    }
                                    objectName = fileNameWithoutExtension + "_" +fileMapId+ ".pdf";
                                    String minioUrl = minioFileUtils.uploadToMinio(objectName, byteArray);
                                    vo.setDownloadUrl(minioUrl);
                                    log.info("上传到Minio成功，下载地址为：{}", minioUrl);
                                }
                                /*// 改成转成base64的格式返回
                                URL url = new URL(downloadUrl);
                                try (InputStream inputStream = url.openStream()) {
                                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                                    byte[] buffer = new byte[1024];
                                    int bytesRead;
                                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                                        byteArrayOutputStream.write(buffer, 0, bytesRead);
                                    }
                                    byte[] byteArray = byteArrayOutputStream.toByteArray();
                                    String base64String = Base64.getEncoder().encodeToString(byteArray);
                                    vo.setFileBase64(base64String);
                                }*/
                                result.add(vo);
                                break;
                            }
                        }
                    }
                    break;
                }
                log.info("文件未签署完成,等待两秒重新查询");
                TimeUnit.SECONDS.sleep(2);
                i++;
            }

            sealResp.setCode(200);
            sealResp.setMsg("成功发起电子签章！");
            sealResp.setData(result);

        } catch (ServiceException e){
            sealResp.setCode(9009);
            sealResp.setMsg(e.getMessage());
            sealResp.setData(null);
            System.out.println(e.getMessage());
            return sealResp;
        } finally  {
            for (File file1 : files) {
                // 确保文件被删除
                if (file1 != null && file1.exists()) {
                    boolean isDeleted = file1.delete();
                    if (isDeleted) {
                        log.info("文件已成功删除: {}", filePath);
                    } else {
                        log.error("文件删除失败: {}", filePath);
                    }
                }
            }

        }
        return  sealResp;
    }



    @Override
    public CommerceContractPrintReceiptSealResp receiptUploadFile(HttpServletResponse response, CommerceContractPrintReceiptUploadFileReq uploadReq,List<MultipartFile> fileList) throws InterruptedException, EsignDemoException, IOException {

        CommerceContractPrintReceiptSealResp sealResp = new CommerceContractPrintReceiptSealResp();
        File file = null;
        String filePath = null;

        List<File> files  = new ArrayList<>();
        List<Map<String,Object>> fileParamList  = new ArrayList<>();

        // 拿到请求中的fileList   里面有文件映射ID 、 文件输入流
//        List<MultipartFile> fileList = uploadReq.getFileList();

        String oaSignFlowId  = null; // 存储E签宝返回的流程ID

        try {
            for (MultipartFile fileItem : fileList) {
                String receiptFileId = null;  // 存储E签宝返回的文件ID

                // 获取文件字节流
                byte[] fileBytes;
                try {
                    fileBytes = fileItem.getBytes();
                } catch (IOException e) {
                    log.error("获取MultipartFile字节流失败", e);
                    throw new RuntimeException("获取文件内容失败", e);
                }

                String fileType = "pdf";
                File tempFile = File.createTempFile("temp_file_", "."+fileType);
                tempFile.deleteOnExit();

                // 使用 ByteArrayInputStream 包装 byte[]
                try (InputStream byteArrayInputStream = new ByteArrayInputStream(fileBytes);
                     FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = byteArrayInputStream.read(buffer)) != -1) {
                        fileOutputStream.write(buffer, 0, bytesRead);
                    }
                    log.info("文件已成功写入临时文件: {}", tempFile.getAbsolutePath());

                } catch (IOException e) {
                    log.error("写入临时文件失败", e);
                    throw e;
                }

                // 调用E签宝的上传文件接口
                file = tempFile;
                files.add(file);
                filePath = file.getAbsolutePath();
                log.info("上传文件的绝对路径: {}", filePath);
                log.info("上传文件的类型: {}", fileType);

                //获取文件id以及文件上传地址
                EsignHttpResponse getUploadUrl = getUploadUrl(filePath,fileType);
                Gson gson = new Gson();
                JsonObject getUploadUrlJsonObject = gson.fromJson(getUploadUrl.getBody(), JsonObject.class);
                // e签宝正常返回的code 是 0   其他则为有报错
                if (!"0".equals(getUploadUrlJsonObject.get("code").getAsString())) {
                    throw new ServiceException("E签宝返回错误: " + getUploadUrlJsonObject.get("message").getAsString());
                }
                JsonObject data = getUploadUrlJsonObject.getAsJsonObject("data");
                //文件id后续发起签署使用
                receiptFileId = data.get("fileId").getAsString();
                String fileUploadUrl = data.get("fileUploadUrl").getAsString();

                log.info("获取文件id以及文件上传地址成功，文件id: {}", receiptFileId);
                log.info("上传链接: {}", fileUploadUrl);

                //文件上传
                EsignHttpResponse uploadFileResponse = uploadFile(fileUploadUrl, filePath);
                JsonObject uploadFileResponseJsonObject = gson.fromJson(uploadFileResponse.getBody(), JsonObject.class);
                if (!"0".equals(uploadFileResponseJsonObject.get("errCode").getAsString())) {// 该接口返回体和别人不一样 ， 别的是code  data message  这个是 errCode msg
                    throw new ServiceException("E签宝返回错误: " + uploadFileResponseJsonObject.get("msg").getAsString());
                }
                String code = uploadFileResponseJsonObject.get("errCode").getAsString();
                log.info("文件上传成功，状态码: {}", code);

                //文件上传成功后文件会有一个异步处理过程，建议轮询文件状态，正常后发起签署
                //查询文件上传状态
                int i = 0;
                while (i < 3) {
                    EsignHttpResponse fileStatus = getFileStatus(receiptFileId);
                    JsonObject fileStatusJsonObject = gson.fromJson(fileStatus.getBody(), JsonObject.class);
                    if (!"0".equals(fileStatusJsonObject.get("code").getAsString())) {
                        throw new ServiceException("E签宝返回错误: " + fileStatusJsonObject.get("message").getAsString());
                    }
                    String status = fileStatusJsonObject.getAsJsonObject("data").get("fileStatus").getAsString();
                    log.info("查询文件状态执行第{}次", i + 1);
                    if ("2".equalsIgnoreCase(status) || "5".equalsIgnoreCase(status)) {
                        log.info("文件上传成功，文件ID为：{}，文件状态为: {}", receiptFileId,status);
                        // 将文件映射id和文件id存入map中  前者是OA传来的ID  后者是E签宝返回的ID
                        // fileIdMap.put(fileItem.getFileMapId(),oaFileId);
                        // 指定盖章位置的关键词
                        List<String> keywords = new ArrayList<>();
                        keywords.add("盖章");

                        //请求盖章位置接口
                        EsignHttpResponse getKeyWordPositions = getKeyWordPositions(receiptFileId, keywords);
                        JsonObject getKeyWordPositionsJsonObject = gson.fromJson(getKeyWordPositions.getBody(), JsonObject.class);
                        if (!"0".equals(getKeyWordPositionsJsonObject.get("code").getAsString())) {
                            throw new ServiceException("E签宝返回错误: " + getKeyWordPositionsJsonObject.get("message").getAsString());
                        }
                        JsonObject getKeyWordPositionsData = getKeyWordPositionsJsonObject.getAsJsonObject("data");
                        JsonArray keywordPositions = getKeyWordPositionsData.getAsJsonArray("keywordPositions");

                        // 创建签署需要的参数map
                        List<Map<String, Object>> reqMap = createReceiptSignRequestMap(receiptFileId, uploadReq, keywordPositions);
                        Map<String, Object> fileParam = new HashMap<>();
                        fileParam.put("reqMap", reqMap);
                        fileParamList.add(fileParam);
                        log.info("文件准备完成！该文件组装后的参数：{}",fileParamList);

                        break;
                    }
                    log.info("文件未准备完成,等待两秒重新查询");
                    TimeUnit.SECONDS.sleep(2);
                    i++;
                }
            }

            log.info("所有签署文件准备完成！组装后的参数：{}",fileParamList);



            // 发起签署
            Gson gson = new Gson();
            EsignHttpResponse createByFile = createByReceiptFile(fileParamList);
            JsonObject createByFileJsonObject = gson.fromJson(createByFile.getBody(), JsonObject.class);
            if (!"0".equals(createByFileJsonObject.get("code").getAsString())) {
                throw new ServiceException("E签宝返回错误: " + createByFileJsonObject.get("message").getAsString());
            }
            JsonObject createByFileData = createByFileJsonObject.getAsJsonObject("data");
            oaSignFlowId = createByFileData.get("signFlowId").getAsString();
            log.info("发起签署成功,签署流程id: {}", oaSignFlowId);

            // 创建返回的结构
            List<String> result = new ArrayList<>();
            //发起签署之后 响应需要时间  直到签署完成后  再返回结果
            int i = 0;
            while (i < 3) {

                //请求地址
                //下载已签署的文件及附属材料。流程结束后,获取签署完成的文件以及相关附属材料的下载链接。未签署完成的流程，无法下载相关文件，否则会报错："流程非签署完成状态，不允许下载文档"。
                String apiaddr = "/v3/sign-flow/"+ oaSignFlowId +"/file-download-url";
                //请求参数body体,json格式。get或者delete请求时jsonString传空json:"{}"或者null
                String jsonParm = null;
                //请求方法
                EsignRequestType requestType= EsignRequestType.GET;
                //生成签名鉴权方式的的header
                Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId,eSignAppSecret,jsonParm,requestType.name(),apiaddr,true);
                //发起接口请求
                EsignHttpResponse esignHttpResponse = EsignHttpHelper.doCommHttp(eSignHost, apiaddr, requestType, jsonParm, header, true);
                JsonObject fileDownloadUrlJsonObject = gson.fromJson(esignHttpResponse.getBody(), JsonObject.class);
                if (!"1437518".equals(fileDownloadUrlJsonObject.get("code").getAsString())) {
                    JsonObject fileDataDownloadUrlObject = fileDownloadUrlJsonObject.getAsJsonObject("data");//获取body体里的“data”字段
                    //获取已签署文件的下载链接
                    JsonArray eSignResponseFiles = fileDataDownloadUrlObject.get("files").getAsJsonArray();//获取fileDownloadUrlObject里的“files”字段
                    // 遍历 eSignResponseFiles 数组，查找匹配的 fileId
                    for (int k = 0; k < eSignResponseFiles.size(); k++) {
                        JsonObject fileJsonObject = eSignResponseFiles.get(k).getAsJsonObject();
                        String fileId = fileJsonObject.get("fileId").getAsString();
                        String downloadUrl = fileJsonObject.get("downloadUrl").getAsString();
                        result.add(downloadUrl);
                    }
                    break;
                }
                log.info("文件未签署完成,等待两秒重新查询");
                TimeUnit.SECONDS.sleep(2);
                i++;
            }

            sealResp.setCode(200);
            sealResp.setMsg("成功发起电子签章！");
            sealResp.setData(result);

        } catch (ServiceException e){
            sealResp.setCode(9009);
            sealResp.setMsg(e.getMessage());
            sealResp.setData(null);
            return sealResp;
        } finally  {
            for (File file1 : files) {
                // 确保文件被删除
                if (file1 != null && file1.exists()) {
                    boolean isDeleted = file1.delete();
                    if (isDeleted) {
                        log.info("文件已成功删除: {}", filePath);
                    } else {
                        log.error("文件删除失败: {}", filePath);
                    }
                }
            }

        }
        return  sealResp;
    }



    /**
     * 获取文件上传地址
     * @return
     */
    public  EsignHttpResponse getUploadUrl(String filePath,String fileType) throws EsignDemoException, IOException {
        //自定义的文件封装类，传入文件地址可以获取文件的名称大小,文件流等数据
        EsignFileBean esignFileBean = new EsignFileBean(filePath);
        String apiaddr="/v3/files/file-upload-url";
        // 默认为false pdf上传只能填false 不然会报错
        // 不为pdf的得上传到e签宝再转成PDF
        boolean isConvertToPDF = false;
        if(!fileType.equals("pdf")){
            isConvertToPDF = true;
        }
        // 使用 JsonObject 构建请求参数
        JsonObject json = new JsonObject();
        json.addProperty("contentMd5", esignFileBean.getFileContentMD5());
        json.addProperty("fileName", esignFileBean.getFileName());
        json.addProperty("fileSize", esignFileBean.getFileSize());
        json.addProperty("convertToPDF", isConvertToPDF);
        json.addProperty("contentType", EsignHeaderConstant.CONTENTTYPE_STREAM.VALUE());
        String jsonParm = json.toString();
        //请求方法
        EsignRequestType requestType= EsignRequestType.POST;
        //生成签名鉴权方式的的header
        Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId,eSignAppSecret,jsonParm,requestType.name(),apiaddr,true);
        //发起接口请求
        return EsignHttpHelper.doCommHttp(eSignHost, apiaddr,requestType , jsonParm, header,true);
    }


    /**
     * 文件流上传
     * @return
     */
    public  EsignHttpResponse uploadFile(String uploadUrl,String filePath) throws EsignDemoException, IOException {
        //根据文件地址获取文件contentMd5
        EsignFileBean esignFileBean = new EsignFileBean(filePath);
        //请求方法
        EsignRequestType requestType= EsignRequestType.PUT;
        return EsignHttpHelper.doUploadHttp(uploadUrl,requestType,esignFileBean.getFileBytes(),esignFileBean.getFileContentMD5(), EsignHeaderConstant.CONTENTTYPE_STREAM.VALUE(),true);
    }


    /**
     * 获取文件上传状态
     * @return
     */
    public  EsignHttpResponse getFileStatus(String fileId) throws EsignDemoException {
        String apiaddr="/v3/files/"+fileId;

        //请求参数body体,json格式。get或者delete请求时jsonString传空json:"{}"或者null
        String jsonParm=null;
        //请求方法
        EsignRequestType requestType= EsignRequestType.GET;
        //生成签名鉴权方式的的header
        Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId,eSignAppSecret,jsonParm,requestType.name(),apiaddr,true);
        //发起接口请求
        return EsignHttpHelper.doCommHttp(eSignHost, apiaddr,requestType , jsonParm, header,true);
    }


    /**
     * 根据关键词查找对应盖章的XY轴坐标
     * @return
     */
    public  EsignHttpResponse getKeyWordPositions(String fileId,List<String> keywords) throws EsignDemoException {
        String apiaddr= "/v3/files/" + fileId + "/keyword-positions";


        // 构建 keywords 的 JSON 数组字符串
        StringBuilder keywordsJson = new StringBuilder("[");
        for (int i = 0; i < keywords.size(); i++) {
            keywordsJson.append("\"").append(keywords.get(i)).append("\"");
            if (i < keywords.size() - 1) {
                keywordsJson.append(",");
            }
        }
        keywordsJson.append("]");

        // 构建完整的 JSON 请求体
        String jsonParm = "{\"keywords\": " + keywordsJson.toString() + "}";

        //请求方法
        EsignRequestType requestType= EsignRequestType.POST;
        //生成签名鉴权方式的的header
        Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId,eSignAppSecret,jsonParm,requestType.name(),apiaddr,true);
        //发起接口请求
        return EsignHttpHelper.doCommHttp(eSignHost, apiaddr,requestType , jsonParm, header,true);
    }


    /**
     * 发起签署
     * @return
     * @throws EsignDemoException
     */
    public  EsignHttpResponse createByFile(List<Map<String, Object>> reqMap) throws EsignDemoException {
        String apiaddr = "/v3/sign-flow/create-by-file";

        // 创建 docs 数组
        JsonArray docs = new JsonArray();
        for (Map<String, Object> objectMap : reqMap) {
            List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
            JsonObject doc = new JsonObject();
            doc.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());
            docs.add(doc);
        }
        List<Map<String, Object>> commonParam = (List<Map<String, Object>>) reqMap.get(0).get("reqMap");
        // 创建 signFlowConfig
        JsonObject signFlowConfig = new JsonObject();
        signFlowConfig.addProperty("signFlowTitle", commonParam.get(0).get("contractNo").toString()+"合同签署");
        signFlowConfig.addProperty("autoFinish", true);

        JsonObject noticeConfig = new JsonObject();
        noticeConfig.addProperty("noticeTypes", "1,2");
        signFlowConfig.add("noticeConfig", noticeConfig);
        // TODO 这个是开发环境回调地址  上生产得改成生产地址
        signFlowConfig.addProperty("notifyUrl", esignNotifyUrl);
        // 生产环境地址
        // signFlowConfig.addProperty("notifyUrl", "https://erqi.leyingiot.com/api/opt-commerce/commerceContractPrint/esignCallBack");
        // 开发环境地址
        // signFlowConfig.addProperty("notifyUrl", "http://47.112.30.182:22398/api/opt-commerce/commerceContractPrint/esignCallBack");
        // 测试环境地址
        // signFlowConfig.addProperty("notifyUrl", "http://47.112.30.182:22402/api/opt-commerce/commerceContractPrint/esignCallBack");
        // 本地测试地址
        //signFlowConfig.addProperty("notifyUrl", "http://chase3613.free.idcfengye.com/opt-commerce/commerceContractPrint/esignCallBack");


        // 创建 signers 数组
        JsonArray signers = new JsonArray();
        // 单方签署
        if(Integer.valueOf(commonParam.get(0).get("signType").toString()) == 0){
            signers = getUnilateralSignatureSignerJson(reqMap,commonParam);
        }else if(Integer.valueOf(commonParam.get(0).get("signType").toString()) == 1){// 平台自身与个人用户签署
            signers = getSystemSignedWithPersonSignerJson(reqMap,commonParam);
        }else if(Integer.valueOf(commonParam.get(0).get("signType").toString()) == 2){// 平台自身与企业用户签署
            signers = getSystemSignedWithCompanySignerJson(reqMap,commonParam);
        }

        // 将所有内容组合成一个 JSON 对象
        JsonObject json = new JsonObject();
        json.add("docs", docs);
        json.add("signFlowConfig", signFlowConfig);
        json.add("signers", signers);

        // 发起机构名称改成用章公司对应的名称
        // 创建 signFlowConfig
        if(commonParam.get(0).get("otherOrgId") != null){
            JsonObject signFlowInitiator = new JsonObject();
            JsonObject orgInitiator = new JsonObject();
            orgInitiator.addProperty("orgId", commonParam.get(0).get("otherOrgId").toString());
            JsonObject transactor = new JsonObject();
            transactor.addProperty("psnId", commonParam.get(0).get("handlerId").toString());
            orgInitiator.add("transactor",transactor);
            signFlowInitiator.add("orgInitiator", orgInitiator);
            json.add("signFlowInitiator", signFlowInitiator);
        }

        String jsonParm  = json.toString();
        log.info("最终的请求参数：{}" ,jsonParm);
        //请求方法
        EsignRequestType requestType = EsignRequestType.POST;
        //生成请求签名鉴权方式的Header
        Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId, eSignAppSecret, jsonParm, requestType.name(), apiaddr, true);
        //发起接口请求
        return EsignHttpHelper.doCommHttp(eSignHost, apiaddr, requestType, jsonParm, header, true);
    }



    /**
     * 发起签署——OA
     * @return
     * @throws EsignDemoException
     */
    public  EsignHttpResponse createByOaFile(List<Map<String, Object>> reqMap) throws EsignDemoException {
        String apiaddr = "/v3/sign-flow/create-by-file";

        // 创建 docs 数组
        JsonArray docs = new JsonArray();
        for (Map<String, Object> objectMap : reqMap) {
            List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
            JsonObject doc = new JsonObject();
            doc.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());
            docs.add(doc);
        }
        List<Map<String, Object>> commonParam = (List<Map<String, Object>>) reqMap.get(0).get("reqMap");
        // 创建 signFlowConfig
        JsonObject signFlowConfig = new JsonObject();
        signFlowConfig.addProperty("signFlowTitle", "OA文件签署");
        signFlowConfig.addProperty("autoFinish", true);

        JsonObject noticeConfig = new JsonObject();
        noticeConfig.addProperty("noticeTypes", "1,2");
        signFlowConfig.add("noticeConfig", noticeConfig);

        // 单方签署 -甲方签署
        JsonArray signers = new JsonArray();
        JsonObject signer = new JsonObject();
        // 0 甲方签署
        JsonObject signConfig = new JsonObject();
        signConfig.addProperty("signOrder", 1);
        signer.add("signConfig", signConfig);
        signer.addProperty("signerType", 1);

        // 签署区
        JsonArray signFields = new JsonArray();
        for (Map<String, Object> objectMap : reqMap) {
            List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
            //签署的位置信息
            // 适配多个签署区的场景
            for (int i = 1; i < everyParamMapList.size(); i++) {
                JsonObject signField = new JsonObject();
                signField.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

                // 签署的形式信息：自动签署、指定印章签署
                JsonObject normalSignFieldConfig = new JsonObject();
                normalSignFieldConfig.addProperty("autoSign", true);
                normalSignFieldConfig.addProperty("signFieldStyle", 1);
                normalSignFieldConfig.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

                JsonObject signFieldPosition = new JsonObject();
                signFieldPosition.addProperty("positionPage", everyParamMapList.get(i).get("positionPage").toString());
                signFieldPosition.addProperty("positionX", everyParamMapList.get(i).get("positionX").toString());
                signFieldPosition.addProperty("positionY", everyParamMapList.get(i).get("positionY").toString());
                normalSignFieldConfig.add("signFieldPosition", signFieldPosition);
                signField.add("normalSignFieldConfig", normalSignFieldConfig);

                //签署的日期信息
                JsonObject signDateConfig = new JsonObject();
                signDateConfig.addProperty("fontSize", 13);
                signDateConfig.addProperty("showSignDate", 1);
                // E签宝有点bug 必须得指定签署日期的坐标fontSize才能生效  下面是根据XY轴确定日期位置，只能固定在印章下面的大概位置
                Double positionX = Double.parseDouble(everyParamMapList.get(i).get("positionX").toString());
                Double adjustedPositionX = positionX - 40;
                signDateConfig.addProperty("signDatePositionX", adjustedPositionX.toString());
                Double positionY = Double.parseDouble(everyParamMapList.get(i).get("positionY").toString());
                Double adjustedPositionY = positionY - 90;
                signDateConfig.addProperty("signDatePositionY", adjustedPositionY.toString());
                // pdf格式默认任意位置盖章、不显示日期
                if(!"pdf".equals(String.valueOf(everyParamMapList.get(0).get("fileType")))){
                    signField.add("signDateConfig", signDateConfig);
                }
                signFields.add(signField);
            }

            // 骑缝章的判断
            if (everyParamMapList.get(0).get("isCrossPageSeal") != null && "0".equals(everyParamMapList.get(0).get("isCrossPageSeal").toString())) {
                JsonObject signField3 = new JsonObject();
                signField3.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

                // 签署的形式信息：自动签署、指定印章签署
                JsonObject normalSignFieldConfig3 = new JsonObject();
                normalSignFieldConfig3.addProperty("autoSign", true);
                normalSignFieldConfig3.addProperty("signFieldStyle", 2);
                normalSignFieldConfig3.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

                //签署的位置信息
                JsonObject signFieldPosition3 = new JsonObject();
                signFieldPosition3.addProperty("acrossPageMode", "ALL");//默认所有页码都盖上
                signFieldPosition3.addProperty("positionY", 300);
                normalSignFieldConfig3.add("signFieldPosition", signFieldPosition3);
                signField3.add("normalSignFieldConfig", normalSignFieldConfig3);
                signFields.add(signField3);
            }
        }
        signer.add("signFields", signFields);
        signers.add(signer);

        // 将所有内容组合成一个 JSON 对象
        JsonObject json = new JsonObject();
        json.add("docs", docs);
        json.add("signFlowConfig", signFlowConfig);
        json.add("signers", signers);

        // 发起机构名称改成用章公司对应的名称
        // 创建 signFlowConfig
        if(commonParam.get(0).get("otherOrgId") != null){
            JsonObject signFlowInitiator = new JsonObject();
            JsonObject orgInitiator = new JsonObject();
            orgInitiator.addProperty("orgId", commonParam.get(0).get("otherOrgId").toString());
            JsonObject transactor = new JsonObject();
            transactor.addProperty("psnId", commonParam.get(0).get("handlerId").toString());
            orgInitiator.add("transactor",transactor);
            signFlowInitiator.add("orgInitiator", orgInitiator);
            json.add("signFlowInitiator", signFlowInitiator);
        }

        String jsonParm  = json.toString();
        log.info("最终的请求参数：{}" ,jsonParm);
        //请求方法
        EsignRequestType requestType = EsignRequestType.POST;
        //生成请求签名鉴权方式的Header
        Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId, eSignAppSecret, jsonParm, requestType.name(), apiaddr, true);
        //发起接口请求
        return EsignHttpHelper.doCommHttp(eSignHost, apiaddr, requestType, jsonParm, header, true);
    }

    /**
     * 发起签署——收据
     * @return
     * @throws EsignDemoException
     */
    public  EsignHttpResponse createByReceiptFile(List<Map<String, Object>> reqMap) throws EsignDemoException {
        String apiaddr = "/v3/sign-flow/create-by-file";

        // 创建 docs 数组
        JsonArray docs = new JsonArray();
        for (Map<String, Object> objectMap : reqMap) {
            List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
            JsonObject doc = new JsonObject();
            doc.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());
            docs.add(doc);
        }
        List<Map<String, Object>> commonParam = (List<Map<String, Object>>) reqMap.get(0).get("reqMap");
        // 创建 signFlowConfig
        JsonObject signFlowConfig = new JsonObject();
        signFlowConfig.addProperty("signFlowTitle", "收据文件签署");
        signFlowConfig.addProperty("autoFinish", true);

        JsonObject noticeConfig = new JsonObject();
        noticeConfig.addProperty("noticeTypes", "1,2");
        signFlowConfig.add("noticeConfig", noticeConfig);

        // 单方签署 -甲方签署
        JsonArray signers = new JsonArray();
        JsonObject signer = new JsonObject();
        // 0 甲方签署
        JsonObject signConfig = new JsonObject();
        signConfig.addProperty("signOrder", 1);
        signer.add("signConfig", signConfig);
        signer.addProperty("signerType", 1);

        // 签署区
        JsonArray signFields = new JsonArray();
        for (Map<String, Object> objectMap : reqMap) {
            List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
            int size = everyParamMapList.size();
            for(int i = 1; i < size; i++){
                JsonObject signField = new JsonObject();
                signField.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());
                // 签署的形式信息：自动签署、指定印章签署
                JsonObject normalSignFieldConfig = new JsonObject();
                normalSignFieldConfig.addProperty("autoSign", true);
                normalSignFieldConfig.addProperty("signFieldStyle", 1);
                normalSignFieldConfig.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

                //签署的位置信息
                JsonObject signFieldPosition = new JsonObject();
                signFieldPosition.addProperty("positionPage", everyParamMapList.get(i).get("positionPage").toString());
                signFieldPosition.addProperty("positionX", everyParamMapList.get(i).get("positionX").toString());
                signFieldPosition.addProperty("positionY", everyParamMapList.get(i).get("positionY").toString());
                normalSignFieldConfig.add("signFieldPosition", signFieldPosition);
                signField.add("normalSignFieldConfig", normalSignFieldConfig);

                signFields.add(signField);
            }


        }
        signer.add("signFields", signFields);
        signers.add(signer);

        // 将所有内容组合成一个 JSON 对象
        JsonObject json = new JsonObject();
        json.add("docs", docs);
        json.add("signFlowConfig", signFlowConfig);
        json.add("signers", signers);

        // 发起机构名称改成用章公司对应的名称
        // 创建 signFlowConfig
        if(commonParam.get(0).get("otherOrgId") != null){
            JsonObject signFlowInitiator = new JsonObject();
            JsonObject orgInitiator = new JsonObject();
            orgInitiator.addProperty("orgId", commonParam.get(0).get("otherOrgId").toString());
            JsonObject transactor = new JsonObject();
            transactor.addProperty("psnId", commonParam.get(0).get("handlerId").toString());
            orgInitiator.add("transactor",transactor);
            signFlowInitiator.add("orgInitiator", orgInitiator);
            json.add("signFlowInitiator", signFlowInitiator);
        }

        String jsonParm  = json.toString();
        log.info("最终的请求参数：{}" ,jsonParm);
        //请求方法
        EsignRequestType requestType = EsignRequestType.POST;
        //生成请求签名鉴权方式的Header
        Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId, eSignAppSecret, jsonParm, requestType.name(), apiaddr, true);
        //发起接口请求
        return EsignHttpHelper.doCommHttp(eSignHost, apiaddr, requestType, jsonParm, header, true);
    }


    /**
     * 根据企业名称查找机构ID
     * @return
     */
    public  EsignHttpResponse getOrgId(String orgName) throws EsignDemoException {
        String apiaddr="/v3/organizations/identity-info?orgName="+orgName;

        //请求参数body体,json格式。get或者delete请求时jsonString传空json:"{}"或者null
        String jsonParm=null;
        //请求方法
        EsignRequestType requestType= EsignRequestType.GET;
        //生成签名鉴权方式的的header
        Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId,eSignAppSecret,jsonParm,requestType.name(),apiaddr,true);
        //发起接口请求
        return EsignHttpHelper.doCommHttp(eSignHost, apiaddr,requestType , jsonParm, header,true);
    }

    /**
     * 根据经办人手机号拿到经办人ID
     * @return
     */
    public  EsignHttpResponse getHandlerId(String handlerAccount) throws EsignDemoException {
        String apiaddr="/v3/persons/identity-info?psnAccount="+handlerAccount;

        //请求参数body体,json格式。get或者delete请求时jsonString传空json:"{}"或者null
        String jsonParm=null;
        //请求方法
        EsignRequestType requestType= EsignRequestType.GET;
        //生成签名鉴权方式的的header
        Map<String, String> header = EsignHttpHelper.signAndBuildSignAndJsonHeader(eSignAppId,eSignAppSecret,jsonParm,requestType.name(),apiaddr,true);
        //发起接口请求
        return EsignHttpHelper.doCommHttp(eSignHost, apiaddr,requestType , jsonParm, header,true);
    }



    /**
     * 创建签署需要的参数map
     * @param fileId 文件ID
     * @param contractPrintGetVo 合同打印获取VO
     * @param keywordPositions 关键词位置
     * @return 签署请求参数列表
     */
    private List<Map<String, Object>> createSignRequestMap(String fileId, CommerceContractPrintGetVo contractPrintGetVo, JsonArray keywordPositions) throws ServiceException, EsignDemoException {
        List<Map<String, Object>> reqMap = new ArrayList<>();
        Map<String, Object> signMap = new HashMap<>();
        signMap.put("fileId", fileId);
        signMap.put("signType",contractPrintGetVo.getSignType());
        signMap.put("contractNo", contractPrintGetVo.getContractNo());
        signMap.put("templateName", contractPrintGetVo.getTemplateName());
        signMap.put("lessorContactName", contractPrintGetVo.getLessorContactName());
        signMap.put("lessorContactPhone", contractPrintGetVo.getLessorContactPhone());
        signMap.put("renterNotifyContactName", contractPrintGetVo.getRenterNotifyContactName());
        signMap.put("renterNotifyContactPhone", contractPrintGetVo.getRenterNotifyContactPhone());
        signMap.put("isRenterSeal",contractPrintGetVo.getIsRenterSeal());
        signMap.put("isRenterSealFirst",contractPrintGetVo.getIsRenterSealFirst());
        signMap.put("isCrossPageSeal",contractPrintGetVo.getIsCrossPageSeal());
        signMap.put("renterCompanyName",contractPrintGetVo.getRenterCompanyName());
        signMap.put("renterCompanyCode",contractPrintGetVo.getRenterCompanyCode());


        // 根据用章公司和印章类别来拿到印章ID  双方签署时getIsRenterSeal为null  单方签署-甲方签署才做查询印章操作
        if(contractPrintGetVo.getIsRenterSeal() == null || contractPrintGetVo.getIsRenterSeal() == 0){
            log.info("走了查询印章~");
            Integer signCompanyCode = contractPrintGetVo.getSignCompanyCode();
            Integer sealTypeCode = contractPrintGetVo.getSealTypeCode();
            String sealId = commerceContractPrintProvider.selectSealId(signCompanyCode, sealTypeCode);
            signMap.put("sealId", sealId);
            log.info("查询到的印章为：signCompanyCode ：{}  sealTypeCode：{}   sealId：{}",signCompanyCode,sealTypeCode,sealId);
            if(StringUtils.isEmpty(sealId)){
                throw new ServiceException("没有找到对应的印章，请确定用章公司与印章类别是否正确！");
            }
            // 根据CompanyCode来拿到公司名称  发起时需要指定发起的公司是哪个
            if(signCompanyCode != null && signCompanyCode != 0){// 默认是主公司， 不是的话需要进行处理
                Map<String, Object> signInfo =  commerceContractPrintProvider.getSignInfoBySignCompanyCode(signCompanyCode);
                String signCompanyName = String.valueOf(signInfo.get("signCompanyName"));
                String handlerAccount = String.valueOf(signInfo.get("handlerAccount"));

                EsignHttpResponse getOrgId = getOrgId(signCompanyName);
                Gson gson = new Gson();
                JsonObject getOrgIdObject = gson.fromJson(getOrgId.getBody(), JsonObject.class);
                // e签宝正常返回的code 是 0   其他则为有报错
                if (!"0".equals(getOrgIdObject.get("code").getAsString())) {
                    throw new ServiceException("E签宝返回错误: " + getOrgIdObject.get("message").getAsString());
                }
                JsonObject data = getOrgIdObject.getAsJsonObject("data");
                //文件id后续发起签署使用
                String otherOrgId = data.get("orgId").getAsString();
                signMap.put("otherOrgId",otherOrgId);
                log.info("获取公司orgId成功，otherOrgId: {}", otherOrgId);


                EsignHttpResponse getHandlerId = getHandlerId(handlerAccount);
                Gson gson1 = new Gson();
                JsonObject getHandlerIdObject = gson1.fromJson(getHandlerId.getBody(), JsonObject.class);
                // e签宝正常返回的code 是 0   其他则为有报错
                if (!"0".equals(getHandlerIdObject.get("code").getAsString())) {
                    throw new ServiceException("E签宝返回错误: " + getHandlerIdObject.get("message").getAsString());
                }
                JsonObject data1 = getHandlerIdObject.getAsJsonObject("data");
                //文件id后续发起签署使用
                String handlerId = data1.get("psnId").getAsString();
                signMap.put("handlerId",handlerId);
                log.info("获取经办人成功，handlerId: {}", handlerId);
            }
        }

        reqMap.add(signMap);

        // 拿到盖章位置接口返回体中的盖章位置数据
        for (JsonElement keywordPosition : keywordPositions) {
            JsonArray positions = keywordPosition.getAsJsonObject().getAsJsonArray("positions");
            // 判断一下  如果返回的盖章位置数据为空，说明甲方、乙方关键词都没有匹配上，则抛出异常
            for (JsonElement positionElement : positions) {
                JsonObject position = positionElement.getAsJsonObject();
                // 拿到返回体中的关键字XY坐标值coordinates
                JsonArray coordinates = position.getAsJsonArray("coordinates");
                for (JsonElement coordinateElement : coordinates) {
                    JsonObject coordinate = coordinateElement.getAsJsonObject();
                    double positionX = coordinate.get("positionX").getAsDouble() + 100;
                    double positionY = coordinate.get("positionY").getAsDouble() ;
                    System.out.println("positionX: " + positionX + ", positionY: " + positionY);

                    // 创建一个Map来存储坐标信息
                    Map<String, Object> coordinateMap = new HashMap<>();
                    coordinateMap.put("positionPage", position.get("pageNum").getAsString());
                    coordinateMap.put("positionX", positionX);
                    coordinateMap.put("positionY", positionY);
                    reqMap.add(coordinateMap);
                }
            }
        }
        // 单方签署时
        if(contractPrintGetVo.getSignType() != null && contractPrintGetVo.getSignType() == 0 && reqMap.size() < 2){
            throw new ServiceException("没有在文件中找到甲/乙方关键词！");
        }
        // 双方签署时  如果reqMap的长度小于3，说明一定有关键词没有匹配上 ，需要抛出异常
        if(contractPrintGetVo.getSignType() != null && (contractPrintGetVo.getSignType() == 1 || contractPrintGetVo.getSignType() == 2)  && reqMap.size() < 3){
            throw new ServiceException("没有在文件中找到甲/乙方关键词！");
        }
        return reqMap;
    }




    /**
     * 创建OA签署需要的参数map
     * @param fileId 文件ID
     * @param keywordPositions 关键词位置
     * @return 签署请求参数列表
     */
    private List<Map<String, Object>> createOaSignRequestMap(String fileId, CommerceContractPrintOaUploadFileReq uploadReq,CommerceContractPrintOaUploadFileReq.FileItem fileItem, JsonArray keywordPositions,String keyWordType,String fileType,int pageCount) throws ServiceException, EsignDemoException {
        List<Map<String, Object>> reqMap = new ArrayList<>();
        Map<String, Object> signMap = new HashMap<>();
        signMap.put("fileId", fileId);
        signMap.put("signType",0);//OA默认都是单方签署
        signMap.put("isRenterSeal",0);//OA默认都是甲方签署
        signMap.put("isCrossPageSeal",fileItem.getCrossPageSealType());//OA默认都盖骑缝章
        signMap.put("fileType",fileType);

        // 根据用章公司和印章类别来拿到印章ID
        String signCompanyName = uploadReq.getSignCompanyName();
        String sealTypeName = uploadReq.getSealTypeName();

        Map<String, Object> sealObjectMap = commerceContractPrintProvider.selectSealIdByOaUploadReq(signCompanyName, sealTypeName);
        Integer signCompanyCode = (Integer) sealObjectMap.get("signCompanyCode");
        Integer sealTypeCode = (Integer) sealObjectMap.get("sealTypeCode");
        String sealId = String.valueOf(sealObjectMap.get("sealId"));
        String handlerAccount = String.valueOf(sealObjectMap.get("handlerAccount"));

        signMap.put("sealId", sealId);
        log.info("查询到的印章为：signCompanyCode ：{}  sealTypeCode：{}   sealId：{}",signCompanyCode,sealTypeCode,sealId);
        if(StringUtils.isEmpty(sealId)){
            throw new ServiceException("没有找到对应的印章，请确定用章公司与印章类别是否正确！");
        }
        // 根据CompanyCode来拿到公司名称  发起时需要指定发起的公司是哪个
        if(signCompanyCode != null && signCompanyCode != 0){// 默认是主公司， 不是的话需要进行处理
            EsignHttpResponse getOrgId = getOrgId(signCompanyName);
            Gson gson = new Gson();
            JsonObject getOrgIdObject = gson.fromJson(getOrgId.getBody(), JsonObject.class);
            // e签宝正常返回的code 是 0   其他则为有报错
            if (!"0".equals(getOrgIdObject.get("code").getAsString())) {
                throw new ServiceException("E签宝返回错误: " + getOrgIdObject.get("message").getAsString());
            }
            JsonObject data = getOrgIdObject.getAsJsonObject("data");
            //文件id后续发起签署使用
            String otherOrgId = data.get("orgId").getAsString();
            signMap.put("otherOrgId",otherOrgId);
            log.info("获取公司orgId成功，otherOrgId: {}", otherOrgId);


            EsignHttpResponse getHandlerId = getHandlerId(handlerAccount);
            Gson gson1 = new Gson();
            JsonObject getHandlerIdObject = gson1.fromJson(getHandlerId.getBody(), JsonObject.class);
            // e签宝正常返回的code 是 0   其他则为有报错
            if (!"0".equals(getHandlerIdObject.get("code").getAsString())) {
                throw new ServiceException("E签宝返回错误: " + getHandlerIdObject.get("message").getAsString());
            }
            JsonObject data1 = getHandlerIdObject.getAsJsonObject("data");
            //文件id后续发起签署使用
            String handlerId = data1.get("psnId").getAsString();
            signMap.put("handlerId",handlerId);
            log.info("获取经办人成功，handlerId: {}", handlerId);
        }

        reqMap.add(signMap);

        // 拿到盖章位置接口返回体中的盖章位置数据
        // pdf格式默认任意位置盖章  XY轴坐标写死   大概在文件中上一点的位置
        if("pdf".equals(fileType)){
            for(int i = 1; i <= pageCount; i++){
                // 创建一个Map来存储坐标信息
                Map<String, Object> coordinateMap = new HashMap<>();
                coordinateMap.put("positionPage", String.valueOf(i));
                coordinateMap.put("positionX", 300);
                coordinateMap.put("positionY", 500);
                reqMap.add(coordinateMap);
            }
        }else{
            for (JsonElement keywordPosition : keywordPositions) {
                JsonArray positions = keywordPosition.getAsJsonObject().getAsJsonArray("positions");
                // 判断一下  如果返回的盖章位置数据为空，说明甲方、乙方关键词都没有匹配上，则抛出异常
                for (JsonElement positionElement : positions) {
                    JsonObject position = positionElement.getAsJsonObject();
                    // 拿到返回体中的关键字XY坐标值coordinates
                    JsonArray coordinates = position.getAsJsonArray("coordinates");
                    for (JsonElement coordinateElement : coordinates) {
                        JsonObject coordinate = coordinateElement.getAsJsonObject();
                        // 0 甲方（签章）关键词找到的坐标值  需右偏一些
                        // 1 (主体签章) 关键词找到的坐标值  需左偏一些
                        int xTranslation = 0;
                        int yTranslation = 0;
                        if("0".equals(keyWordType)){
                            xTranslation = 100;
                        }else if("1".equals(keyWordType)){
                            xTranslation = 40;
                            yTranslation = 40;
                        }

                        double positionX = coordinate.get("positionX").getAsDouble() + xTranslation;
                        double positionY = coordinate.get("positionY").getAsDouble() + yTranslation;
                        System.out.println("positionX: " + positionX + ", positionY: " + positionY);

                        // 创建一个Map来存储坐标信息
                        Map<String, Object> coordinateMap = new HashMap<>();
                        coordinateMap.put("positionPage", position.get("pageNum").getAsString());
                        coordinateMap.put("positionX", positionX);
                        coordinateMap.put("positionY", positionY);
                        reqMap.add(coordinateMap);
                    }
                }
            }
            // 单方签署时
            if( reqMap.size() < 2){
                throw new ServiceException("没有在文件中找到甲方关键词！");
            }
        }

        return reqMap;
    }


    /**
     * 创建OA签署需要的参数map
     * @param fileId 文件ID
     * @param keywordPositions 关键词位置
     * @return 签署请求参数列表
     */
    private List<Map<String, Object>> createReceiptSignRequestMap(String fileId, CommerceContractPrintReceiptUploadFileReq uploadReq, JsonArray keywordPositions) throws ServiceException, EsignDemoException {
        List<Map<String, Object>> reqMap = new ArrayList<>();
        Map<String, Object> signMap = new HashMap<>();
        signMap.put("fileId", fileId);
        signMap.put("signType",0);//收据默认都是单方签署
        signMap.put("isRenterSeal",0);//收据默认都是甲方签署
        // signMap.put("isCrossPageSeal",0);

        // 根据用章公司和印章类别来拿到印章ID
        String signCompanyName = uploadReq.getAccountingOrgName();
        String sealTypeName = "财务章";

        Map<String, Object> sealObjectMap = commerceContractPrintProvider.selectSealIdByOaUploadReq(signCompanyName, sealTypeName);
        if(sealObjectMap == null){
            throw new ServiceException("没有找到对应的印章，请确定用章公司与印章类别是否正确！");
        }
        Integer signCompanyCode = (Integer) sealObjectMap.get("signCompanyCode");
        Integer sealTypeCode = (Integer) sealObjectMap.get("sealTypeCode");
        String sealId = String.valueOf(sealObjectMap.get("sealId"));
        String handlerAccount = String.valueOf(sealObjectMap.get("handlerAccount"));

        signMap.put("sealId", sealId);
        log.info("查询到的印章为：signCompanyCode ：{}  sealTypeCode：{}   sealId：{}",signCompanyCode,sealTypeCode,sealId);
        if(StringUtils.isEmpty(sealId)){
            throw new ServiceException("没有找到对应的印章，请确定用章公司与印章类别是否正确！");
        }
        // 根据CompanyCode来拿到公司名称  发起时需要指定发起的公司是哪个
        if(signCompanyCode != null && signCompanyCode != 0){// 默认是主公司， 不是的话需要进行处理
            EsignHttpResponse getOrgId = getOrgId(signCompanyName);
            Gson gson = new Gson();
            JsonObject getOrgIdObject = gson.fromJson(getOrgId.getBody(), JsonObject.class);
            // e签宝正常返回的code 是 0   其他则为有报错
            if (!"0".equals(getOrgIdObject.get("code").getAsString())) {
                throw new ServiceException("E签宝返回错误: " + getOrgIdObject.get("message").getAsString());
            }
            JsonObject data = getOrgIdObject.getAsJsonObject("data");
            //文件id后续发起签署使用
            String otherOrgId = data.get("orgId").getAsString();
            signMap.put("otherOrgId",otherOrgId);
            log.info("获取公司orgId成功，otherOrgId: {}", otherOrgId);


            EsignHttpResponse getHandlerId = getHandlerId(handlerAccount);
            Gson gson1 = new Gson();
            JsonObject getHandlerIdObject = gson1.fromJson(getHandlerId.getBody(), JsonObject.class);
            // e签宝正常返回的code 是 0   其他则为有报错
            if (!"0".equals(getHandlerIdObject.get("code").getAsString())) {
                throw new ServiceException("E签宝返回错误: " + getHandlerIdObject.get("message").getAsString());
            }
            JsonObject data1 = getHandlerIdObject.getAsJsonObject("data");
            //文件id后续发起签署使用
            String handlerId = data1.get("psnId").getAsString();
            signMap.put("handlerId",handlerId);
            log.info("获取经办人成功，handlerId: {}", handlerId);
        }

        reqMap.add(signMap);

        // 拿到盖章位置接口返回体中的盖章位置数据
        double positionX = 400.959 + 100;// 数值变大就向右移   变小就向左移动  大于边界就会卡在最边缘  大概50就是2-3个汉字的距离
        double positionY = 627.72 ;// 数值变大就向上移   变小就向下移动
        System.out.println("positionX: " + positionX + ", positionY: " + positionY);

        // 创建一个Map来存储坐标信息
        for(int i = 0 ; i < uploadReq.getPageNum() ; i++){
            Map<String, Object> coordinateMap = new HashMap<>();
            coordinateMap.put("positionPage", i+1);
            coordinateMap.put("positionX", positionX);
            coordinateMap.put("positionY", positionY);
            reqMap.add(coordinateMap);
        }
        return reqMap;
    }


    private  void checkTips(CommerceContractPrintGetVo contractPrintGetVo) throws ServiceException{
        // 以下是签署状态的校验
        //检查一下签署状态是否是签署中（存在signFlowId且状态为1即代表签署中）
        if (StringUtils.isNotEmpty(contractPrintGetVo.getSignFlowId()) &&  contractPrintGetVo.getSignStatus() == 1 ) {
            throw new ServiceException("该合同正在签署中！");
        }
        // 检查一下签署状态是否是已签署或拒签
        if (contractPrintGetVo.getSignStatus() == 2 || contractPrintGetVo.getSignStatus() == 3) {
            throw new ServiceException("该合同已签署或拒签，请勿重复发起！");
        }
        if(contractPrintGetVo.getSignType() == null){
            throw new ServiceException("请指定合同签署方式！");
        }

        //以下是单方签署场景的校验
        if ( contractPrintGetVo.getSignType() == 0 ) {
            if(contractPrintGetVo.getIsRenterSeal() == null ){
                throw new ServiceException("合同签署方式为单方签署时，单方签署形式必填！");
            }
            // 单方签署形式为甲方签署时
            if(contractPrintGetVo.getIsRenterSeal() != null && contractPrintGetVo.getIsRenterSeal() == 0 && (StringUtils.isEmpty(contractPrintGetVo.getLessorKeyword()) || contractPrintGetVo.getSignCompanyCode() == null || contractPrintGetVo.getSealTypeCode() == null )){
                throw new ServiceException("单方签署形式为甲方签署时，甲方签章关键词、用章公司、印章类别必填！");
            }
            // 单方签署形式为乙方签署（个人）时
            if(contractPrintGetVo.getIsRenterSeal() != null && contractPrintGetVo.getIsRenterSeal() == 1 && (StringUtils.isEmpty(contractPrintGetVo.getRenterKeyword()) || StringUtils.isEmpty(contractPrintGetVo.getRenterNotifyContactName()) || StringUtils.isEmpty(contractPrintGetVo.getRenterNotifyContactPhone())  )){
                throw new ServiceException("单方签署形式为乙方签署（个人）时，乙方签章关键词、乙方联系人、乙方联系方式必填！");
            }
            // 单方签署形式为乙方签署（企业）时
            if(contractPrintGetVo.getIsRenterSeal() != null && contractPrintGetVo.getIsRenterSeal() == 2 && (StringUtils.isEmpty(contractPrintGetVo.getRenterCompanyName()) || StringUtils.isEmpty(contractPrintGetVo.getRenterCompanyCode())
                    || StringUtils.isEmpty(contractPrintGetVo.getRenterNotifyContactName()) || StringUtils.isEmpty(contractPrintGetVo.getRenterNotifyContactPhone()))  ){
                throw new ServiceException("单方签署形式为乙方签署（企业）时，企业名称、企业统一社会信用代码、乙方联系人、乙方联系方式必填！");
            }

        }

        // 合同签署方式为平台自身与个人用户签署时
        if ( contractPrintGetVo.getSignType() == 1 ) {
            if(contractPrintGetVo.getIsRenterSealFirst() == null || StringUtils.isEmpty(contractPrintGetVo.getLessorKeyword()) || StringUtils.isEmpty(contractPrintGetVo.getRenterKeyword())
                    || StringUtils.isEmpty(contractPrintGetVo.getRenterNotifyContactName()) || StringUtils.isEmpty(contractPrintGetVo.getRenterNotifyContactPhone())
                    ||contractPrintGetVo.getSignCompanyCode() == null || contractPrintGetVo.getSealTypeCode() == null ){
                throw new ServiceException("合同签署方式为平台自身与个人用户签署时，是否乙方先盖章、甲方乙方签章关键词、用章公司、印章类别、乙方联系人、乙方联系方式必填！");
            }
        }

        //合同签署方式为平台自身与企业用户签署时
        if(contractPrintGetVo.getSignType() == 2 ){
            if(contractPrintGetVo.getIsRenterSealFirst() == null || StringUtils.isEmpty(contractPrintGetVo.getLessorKeyword()) || StringUtils.isEmpty(contractPrintGetVo.getRenterKeyword())
                    || StringUtils.isEmpty(contractPrintGetVo.getRenterCompanyName()) || StringUtils.isEmpty(contractPrintGetVo.getRenterCompanyCode())
                    ||contractPrintGetVo.getSignCompanyCode() == null || contractPrintGetVo.getSealTypeCode() == null
                    || StringUtils.isEmpty(contractPrintGetVo.getRenterNotifyContactName()) || StringUtils.isEmpty(contractPrintGetVo.getRenterNotifyContactPhone())){
                throw new ServiceException("合同签署方式为平台自身与企业用户签署时，是否乙方先盖章、甲方乙方签章关键词、用章公司、印章类别、乙方联系人、乙方联系方式、乙方企业名称、乙方企业统一社会信用代码必填！");
            }
        }
    }





    /**
     * 生成单方签署人请求参数
     */
    private  JsonArray getUnilateralSignatureSignerJson(List<Map<String, Object>> reqMap,List<Map<String, Object>> commonParam) {
        JsonArray signers = new JsonArray();
        JsonObject signer = new JsonObject();
        // 0 甲方签署   1 乙方签署（个人） 2 乙方签署（企业）
        if(commonParam.get(0).get("isRenterSeal") != null && "0".equals(commonParam.get(0).get("isRenterSeal").toString())){
            JsonObject signConfig = new JsonObject();
            signConfig.addProperty("signOrder", 1);
            signer.add("signConfig", signConfig);
            signer.addProperty("signerType", 1);

            // 签署区
            JsonArray signFields = new JsonArray();
            for (Map<String, Object> objectMap : reqMap) {
                List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
                JsonObject signField = new JsonObject();
                signField.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

                // 签署的形式信息：自动签署、指定印章签署
                JsonObject normalSignFieldConfig = new JsonObject();
                normalSignFieldConfig.addProperty("autoSign", true);
                normalSignFieldConfig.addProperty("signFieldStyle", 1);
                normalSignFieldConfig.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

                //签署的位置信息
                JsonObject signFieldPosition = new JsonObject();
                signFieldPosition.addProperty("positionPage", everyParamMapList.get(1).get("positionPage").toString());
                signFieldPosition.addProperty("positionX", everyParamMapList.get(1).get("positionX").toString());
                signFieldPosition.addProperty("positionY", everyParamMapList.get(1).get("positionY").toString());
                normalSignFieldConfig.add("signFieldPosition", signFieldPosition);
                signField.add("normalSignFieldConfig", normalSignFieldConfig);

                //签署的日期信息
                JsonObject signDateConfig = new JsonObject();
                signDateConfig.addProperty("fontSize", 13);
                signDateConfig.addProperty("showSignDate", 1);
                // E签宝有点bug 必须得指定签署日期的坐标fontSize才能生效  下面是根据XY轴确定日期位置，只能固定在印章下面的大概位置
                Double positionX = Double.parseDouble(everyParamMapList.get(1).get("positionX").toString());
                Double adjustedPositionX = positionX - 40;
                signDateConfig.addProperty("signDatePositionX", adjustedPositionX.toString());
                Double positionY = Double.parseDouble(everyParamMapList.get(1).get("positionY").toString());
                Double adjustedPositionY = positionY - 90;
                signDateConfig.addProperty("signDatePositionY", adjustedPositionY.toString());
                signField.add("signDateConfig", signDateConfig);
                signFields.add(signField);

                // 骑缝章的判断
                if(everyParamMapList.get(0).get("isCrossPageSeal") != null && "0".equals(everyParamMapList.get(0).get("isCrossPageSeal").toString())){
                    JsonObject signField3 = new JsonObject();
                    signField3.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

                    // 签署的形式信息：自动签署、指定印章签署
                    JsonObject normalSignFieldConfig3 = new JsonObject();
                    normalSignFieldConfig3.addProperty("autoSign", true);
                    normalSignFieldConfig3.addProperty("signFieldStyle", 2);
                    normalSignFieldConfig3.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

                    //签署的位置信息
                    JsonObject signFieldPosition3 = new JsonObject();
                    signFieldPosition3.addProperty("acrossPageMode", "ALL");//默认所有页码都盖上
                    signFieldPosition3.addProperty("positionY", 300);
                    normalSignFieldConfig3.add("signFieldPosition", signFieldPosition3);
                    signField3.add("normalSignFieldConfig", normalSignFieldConfig3);
                    signFields.add(signField3);
                }
            }
            signer.add("signFields", signFields);
        }else if(commonParam.get(0).get("isRenterSeal") != null && "1".equals(commonParam.get(0).get("isRenterSeal").toString())){
            // 乙方个人
            JsonObject psnSignerInfo = new JsonObject();
            psnSignerInfo.addProperty("psnAccount", commonParam.get(0).get("renterNotifyContactPhone").toString());

            JsonObject psnInfo = new JsonObject();
            psnInfo.addProperty("psnName", commonParam.get(0).get("renterNotifyContactName").toString());
            psnSignerInfo.add("psnInfo", psnInfo);

            signer.add("psnSignerInfo", psnSignerInfo);
            JsonObject signConfig = new JsonObject();
            signConfig.addProperty("forcedReadingTime", 10);
            signConfig.addProperty("signOrder", 1);
            signer.add("signConfig", signConfig);
            signer.addProperty("signerType", 0);

            JsonArray signFields = new JsonArray();

            for (Map<String, Object> objectMap : reqMap) {
                List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
                JsonObject signField = new JsonObject();
                signField.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

                JsonObject normalSignFieldConfig = new JsonObject();
                normalSignFieldConfig.addProperty("signFieldStyle", 1);
                normalSignFieldConfig.addProperty("psnSealStyles", 0);

                JsonObject signFieldPosition = new JsonObject();
                signFieldPosition.addProperty("positionPage", everyParamMapList.get(1).get("positionPage").toString());
                signFieldPosition.addProperty("positionX", everyParamMapList.get(1).get("positionX").toString());
                signFieldPosition.addProperty("positionY", everyParamMapList.get(1).get("positionY").toString());
                normalSignFieldConfig.add("signFieldPosition", signFieldPosition);
                signField.add("normalSignFieldConfig", normalSignFieldConfig);

                //签署的日期信息
                JsonObject signDateConfig = new JsonObject();
                signDateConfig.addProperty("fontSize", 13);
                signDateConfig.addProperty("showSignDate", 1);
                signField.add("signDateConfig", signDateConfig);
                signFields.add(signField);

                //盖骑缝章
                if(everyParamMapList.get(0).get("isCrossPageSeal") != null && "1".equals(everyParamMapList.get(0).get("isCrossPageSeal").toString())){
                    JsonObject signField3 = new JsonObject();
                    signField3.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

                    // 签署的形式信息：自动签署、指定印章签署
                    JsonObject normalSignFieldConfig3 = new JsonObject();
                    normalSignFieldConfig3.addProperty("autoSign", false);
                    normalSignFieldConfig3.addProperty("signFieldStyle", 2);
                    // normalSignFieldConfig3.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

                    //签署的位置信息
                    JsonObject signFieldPosition3 = new JsonObject();
                    signFieldPosition3.addProperty("acrossPageMode", "ALL");//默认所有页码都盖上
                    signFieldPosition3.addProperty("positionY", 300);
                    normalSignFieldConfig3.add("signFieldPosition", signFieldPosition3);
                    signField3.add("normalSignFieldConfig", normalSignFieldConfig3);
                    signFields.add(signField3);
                }
            }
            signer.add("signFields", signFields);
        }else if(commonParam.get(0).get("isRenterSeal") != null && "2".equals(commonParam.get(0).get("isRenterSeal").toString())){
            // 单方签署  乙方企业
            JsonObject orgSignerInfo = new JsonObject();
            orgSignerInfo.addProperty("orgName", commonParam.get(0).get("renterCompanyName").toString());

            JsonObject orgInfo = new JsonObject();
            orgInfo.addProperty("orgIDCardNum", commonParam.get(0).get("renterCompanyCode").toString());
            orgInfo.addProperty("orgIDCardType", "CRED_ORG_USCC");
            orgSignerInfo.add("orgInfo", orgInfo);

            JsonObject transactorInfo = new JsonObject();
            transactorInfo.addProperty("psnAccount", commonParam.get(0).get("renterNotifyContactPhone").toString());

            JsonObject psnInfo = new JsonObject();
            psnInfo.addProperty("psnName", commonParam.get(0).get("renterNotifyContactName").toString());
            transactorInfo.add("psnInfo", psnInfo);

            orgSignerInfo.add("transactorInfo", transactorInfo);
            signer.add("orgSignerInfo", orgSignerInfo);

            // 构建 signConfig
            JsonObject signConfig = new JsonObject();
            signConfig.addProperty("forcedReadingTime", 10);
            signConfig.addProperty("signOrder", 1);
            signer.add("signConfig", signConfig);

            // 构建 signerType
            signer.addProperty("signerType", 1);

            // 构建 signFields
            JsonArray signFields = new JsonArray();

            for (Map<String, Object> objectMap : reqMap) {
                List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
                JsonObject signField = new JsonObject();
                signField.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

                JsonObject normalSignFieldConfig = new JsonObject();
                normalSignFieldConfig.addProperty("signFieldStyle", 1);

                JsonObject signFieldPosition = new JsonObject();
                signFieldPosition.addProperty("positionPage", everyParamMapList.get(1).get("positionPage").toString());
                signFieldPosition.addProperty("positionX", everyParamMapList.get(1).get("positionX").toString());
                signFieldPosition.addProperty("positionY", everyParamMapList.get(1).get("positionY").toString());
                normalSignFieldConfig.add("signFieldPosition", signFieldPosition);
                signField.add("normalSignFieldConfig", normalSignFieldConfig);

                //签署的日期信息
                JsonObject signDateConfig = new JsonObject();
                signDateConfig.addProperty("fontSize", 13);
                signDateConfig.addProperty("showSignDate", 1);
                signField.add("signDateConfig", signDateConfig);
                signFields.add(signField);
                if(everyParamMapList.get(0).get("isCrossPageSeal") != null && "1".equals(everyParamMapList.get(0).get("isCrossPageSeal").toString())){
                    JsonObject signField3 = new JsonObject();
                    signField3.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

                    // 签署的形式信息：自动签署、指定印章签署
                    JsonObject normalSignFieldConfig3 = new JsonObject();
                    normalSignFieldConfig3.addProperty("autoSign", false);
                    normalSignFieldConfig3.addProperty("signFieldStyle", 2);

                    //签署的位置信息
                    JsonObject signFieldPosition3 = new JsonObject();
                    signFieldPosition3.addProperty("acrossPageMode", "ALL");//默认所有页码都盖上
                    signFieldPosition3.addProperty("positionY", 300);
                    normalSignFieldConfig3.add("signFieldPosition", signFieldPosition3);
                    signField3.add("normalSignFieldConfig", normalSignFieldConfig3);
                    signFields.add(signField3);
                }

            }
            signer.add("signFields", signFields);
        }

        signers.add(signer);
        return signers;
    }

    /**
     * 平台对个人签署
     */
    private  JsonArray getSystemSignedWithPersonSignerJson(List<Map<String, Object>> reqMap,List<Map<String, Object>> commonParam) {
        JsonArray signers = new JsonArray();
        // 处理签署顺序 是否乙方先签署   0是 1否
        Integer singerOrder1 = 1;
        Integer singerOrder2 = 2;
        if( commonParam.get(0).get("isRenterSealFirst") != null && "0".equals(commonParam.get(0).get("isRenterSealFirst").toString())){
            singerOrder1 = 2 ;
            singerOrder2 = 1 ;
        }

        // 第一个签署者  本公司 即系统
        JsonObject signer1 = new JsonObject();
        JsonObject signConfig1 = new JsonObject();
        signConfig1.addProperty("signOrder", singerOrder1);
        signer1.add("signConfig", signConfig1);
        signer1.addProperty("signerType", 1);

        JsonArray signFields1 = new JsonArray();

        for (Map<String, Object> objectMap : reqMap) {
            List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
            JsonObject signField1 = new JsonObject();
            signField1.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

            // 签署的形式信息：自动签署、指定印章签署
            JsonObject normalSignFieldConfig1 = new JsonObject();
            normalSignFieldConfig1.addProperty("autoSign", true);
            normalSignFieldConfig1.addProperty("signFieldStyle", 1);
            normalSignFieldConfig1.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

            //签署的位置信息
            JsonObject signFieldPosition1 = new JsonObject();
            signFieldPosition1.addProperty("positionPage", everyParamMapList.get(1).get("positionPage").toString());
            signFieldPosition1.addProperty("positionX", everyParamMapList.get(1).get("positionX").toString());
            signFieldPosition1.addProperty("positionY", everyParamMapList.get(1).get("positionY").toString());
            normalSignFieldConfig1.add("signFieldPosition", signFieldPosition1);
            signField1.add("normalSignFieldConfig", normalSignFieldConfig1);

            //签署的日期信息
            JsonObject signDateConfig1 = new JsonObject();
            signDateConfig1.addProperty("fontSize", 13);
            signDateConfig1.addProperty("showSignDate", 1);
            Double positionX = Double.parseDouble(everyParamMapList.get(1).get("positionX").toString());
            Double adjustedPositionX = positionX - 40;
            signDateConfig1.addProperty("signDatePositionX", adjustedPositionX.toString());
            Double positionY = Double.parseDouble(everyParamMapList.get(1).get("positionY").toString());
            Double adjustedPositionY = positionY - 90;
            signDateConfig1.addProperty("signDatePositionY", adjustedPositionY.toString());
            signField1.add("signDateConfig", signDateConfig1);

            signFields1.add(signField1);

            //不再判断是否甲方乙方盖骑缝章   只要是双方盖章默认都盖骑缝章
            //if(reqMap.get(0).get("isCrossPageSeal") != null && "0".equals(reqMap.get(0).get("isCrossPageSeal").toString())){
            JsonObject signField3 = new JsonObject();
            signField3.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

            // 签署的形式信息：自动签署、指定印章签署
            JsonObject normalSignFieldConfig3 = new JsonObject();
            normalSignFieldConfig3.addProperty("autoSign", true);
            normalSignFieldConfig3.addProperty("signFieldStyle", 2);
            normalSignFieldConfig3.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

            //签署的位置信息
            JsonObject signFieldPosition3 = new JsonObject();
            signFieldPosition3.addProperty("acrossPageMode", "ALL");//默认所有页码都盖上
            signFieldPosition3.addProperty("positionY", 500);
            normalSignFieldConfig3.add("signFieldPosition", signFieldPosition3);
            signField3.add("normalSignFieldConfig", normalSignFieldConfig3);
            signFields1.add(signField3);
            //}
            signer1.add("signFields", signFields1);
        }

        // 第二个签署者   乙方个人
        JsonObject signer2 = new JsonObject();
        // 乙方基本信息
        JsonObject psnSignerInfo = new JsonObject();
        psnSignerInfo.addProperty("psnAccount", commonParam.get(0).get("renterNotifyContactPhone").toString());

        JsonObject psnInfo = new JsonObject();
        psnInfo.addProperty("psnName", commonParam.get(0).get("renterNotifyContactName").toString());
        psnSignerInfo.add("psnInfo", psnInfo);

        signer2.add("psnSignerInfo", psnSignerInfo);
        JsonObject signConfig2 = new JsonObject();
        signConfig2.addProperty("forcedReadingTime", 10);// 强制浏览10秒
        signConfig2.addProperty("signOrder", singerOrder2);// 签署顺序
        signer2.add("signConfig", signConfig2);
        signer2.addProperty("signerType", 0);

        JsonArray signFields2 = new JsonArray();

        for (Map<String, Object> objectMap : reqMap) {
            List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
            JsonObject signField2 = new JsonObject();
            signField2.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

            JsonObject normalSignFieldConfig2 = new JsonObject();
            normalSignFieldConfig2.addProperty("signFieldStyle", 1);
            normalSignFieldConfig2.addProperty("psnSealStyles", 0);
            // 签署位置参数
            JsonObject signFieldPosition2 = new JsonObject();
            signFieldPosition2.addProperty("positionPage", everyParamMapList.get(2).get("positionPage").toString());
            signFieldPosition2.addProperty("positionX", everyParamMapList.get(2).get("positionX").toString());
            signFieldPosition2.addProperty("positionY", everyParamMapList.get(2).get("positionY").toString());
            normalSignFieldConfig2.add("signFieldPosition", signFieldPosition2);
            signField2.add("normalSignFieldConfig", normalSignFieldConfig2);

            //签署的日期信息
            JsonObject signDateConfig2 = new JsonObject();
            signDateConfig2.addProperty("fontSize", 13);
            signDateConfig2.addProperty("showSignDate", 1);
            signField2.add("signDateConfig", signDateConfig2);

            signFields2.add(signField2);
            signer2.add("signFields", signFields2);

            //不再判断是否甲方乙方盖骑缝章   只要是双方盖章默认都盖骑缝章
            // if(reqMap.get(0).get("isCrossPageSeal") != null && "1".equals(reqMap.get(0).get("isCrossPageSeal").toString())){
            JsonObject signField4 = new JsonObject();
            signField4.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

            // 签署的形式信息：自动签署、指定印章签署
            JsonObject normalSignFieldConfig4 = new JsonObject();
            normalSignFieldConfig4.addProperty("autoSign", false);
            normalSignFieldConfig4.addProperty("signFieldStyle", 2);

            //签署的位置信息
            JsonObject signFieldPosition4 = new JsonObject();
            signFieldPosition4.addProperty("acrossPageMode", "ALL");//默认所有页码都盖上
            signFieldPosition4.addProperty("positionY", 300);
            normalSignFieldConfig4.add("signFieldPosition", signFieldPosition4);
            signField4.add("normalSignFieldConfig", normalSignFieldConfig4);
            signFields2.add(signField4);
            // }

        }

        signers.add(signer1);
        signers.add(signer2);
        return signers;
    }


    /**
     * 平台对企业
     */
    private  JsonArray getSystemSignedWithCompanySignerJson(List<Map<String, Object>> reqMap,List<Map<String, Object>> commonParam) {
        JsonArray signers = new JsonArray();

        // 处理签署顺序 是否乙方先签署   0是 1否
        Integer singerOrder1 = 1;
        Integer singerOrder2 = 2;
        if(commonParam.get(0).get("isRenterSealFirst") != null && "0".equals(commonParam.get(0).get("isRenterSealFirst").toString())){
            singerOrder1 = 2 ;
            singerOrder2 = 1 ;
        }
        // 第一个签署者  本公司 即系统
        JsonObject signer1 = new JsonObject();
        JsonObject signConfig1 = new JsonObject();
        signConfig1.addProperty("signOrder", singerOrder1);
        signer1.add("signConfig", signConfig1);
        signer1.addProperty("signerType", 1);

        JsonArray signFields1 = new JsonArray();

        for (Map<String, Object> objectMap : reqMap) {
            List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
            JsonObject signField1 = new JsonObject();
            signField1.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

            // 签署的形式信息：自动签署、指定印章签署
            JsonObject normalSignFieldConfig1 = new JsonObject();
            normalSignFieldConfig1.addProperty("autoSign", true);
            normalSignFieldConfig1.addProperty("signFieldStyle", 1);
            normalSignFieldConfig1.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

            //签署的位置信息
            JsonObject signFieldPosition1 = new JsonObject();
            signFieldPosition1.addProperty("positionPage", everyParamMapList.get(1).get("positionPage").toString());
            signFieldPosition1.addProperty("positionX", everyParamMapList.get(1).get("positionX").toString());
            signFieldPosition1.addProperty("positionY", everyParamMapList.get(1).get("positionY").toString());
            normalSignFieldConfig1.add("signFieldPosition", signFieldPosition1);
            signField1.add("normalSignFieldConfig", normalSignFieldConfig1);

            //签署的日期信息
            JsonObject signDateConfig1 = new JsonObject();
            signDateConfig1.addProperty("fontSize", 13);
            signDateConfig1.addProperty("showSignDate", 1);
            Double positionX = Double.parseDouble(everyParamMapList.get(1).get("positionX").toString());
            Double adjustedPositionX = positionX - 40;
            signDateConfig1.addProperty("signDatePositionX", adjustedPositionX.toString());
            Double positionY = Double.parseDouble(everyParamMapList.get(1).get("positionY").toString());
            Double adjustedPositionY = positionY - 90;
            signDateConfig1.addProperty("signDatePositionY", adjustedPositionY.toString());
            signField1.add("signDateConfig", signDateConfig1);

            signFields1.add(signField1);
            signer1.add("signFields", signFields1);

            //不再判断是否甲方乙方盖骑缝章   只要是双方盖章默认都盖骑缝章
            //if(reqMap.get(0).get("isCrossPageSeal") != null && "0".equals(reqMap.get(0).get("isCrossPageSeal").toString())){
            JsonObject signField3 = new JsonObject();
            signField3.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

            // 签署的形式信息：自动签署、指定印章签署
            JsonObject normalSignFieldConfig3 = new JsonObject();
            normalSignFieldConfig3.addProperty("autoSign", true);
            normalSignFieldConfig3.addProperty("signFieldStyle", 2);
            normalSignFieldConfig3.addProperty("assignedSealId", everyParamMapList.get(0).get("sealId").toString());

            //签署的位置信息
            JsonObject signFieldPosition3 = new JsonObject();
            signFieldPosition3.addProperty("acrossPageMode", "ALL");//默认所有页码都盖上
            signFieldPosition3.addProperty("positionY", 500);
            normalSignFieldConfig3.add("signFieldPosition", signFieldPosition3);
            signField3.add("normalSignFieldConfig", normalSignFieldConfig3);
            signFields1.add(signField3);
            //}

        }



        // 第二个签署者   乙方企业
        JsonObject signer2 = new JsonObject();

        // 构建 orgSignerInfo  企业对应的参数
        JsonObject orgSignerInfo = new JsonObject();
        orgSignerInfo.addProperty("orgName", commonParam.get(0).get("renterCompanyName").toString());

        JsonObject orgInfo = new JsonObject();
        orgInfo.addProperty("orgIDCardNum", commonParam.get(0).get("renterCompanyCode").toString());
        orgInfo.addProperty("orgIDCardType", "CRED_ORG_USCC");
        orgSignerInfo.add("orgInfo", orgInfo);

        JsonObject transactorInfo = new JsonObject();
        transactorInfo.addProperty("psnAccount", commonParam.get(0).get("renterNotifyContactPhone").toString());

        JsonObject psnInfo = new JsonObject();
        psnInfo.addProperty("psnName", commonParam.get(0).get("renterNotifyContactName").toString());
        transactorInfo.add("psnInfo", psnInfo);

        orgSignerInfo.add("transactorInfo", transactorInfo);
        signer2.add("orgSignerInfo", orgSignerInfo);

        // 构建 signConfig   签署配置的参数
        JsonObject signConfig2 = new JsonObject();
        signConfig2.addProperty("forcedReadingTime", 10);
        signConfig2.addProperty("signOrder", singerOrder2);
        signer2.add("signConfig", signConfig2);

        // 构建 signerType  签署方类型0 - 个人，1 -企业/机构，2 - 法定代表人，3 - 经办人
        signer2.addProperty("signerType", 1);

        // 构建 signFields   签署区   在哪个位置签署
        JsonArray signFields2 = new JsonArray();

        for (Map<String, Object> objectMap : reqMap) {
            List<Map<String, Object>> everyParamMapList = (List<Map<String, Object>>) objectMap.get("reqMap");
            JsonObject signField2 = new JsonObject();
            signField2.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

            JsonObject normalSignFieldConfig2 = new JsonObject();
            normalSignFieldConfig2.addProperty("signFieldStyle", 1);

            // 签署位置参数
            JsonObject signFieldPosition2 = new JsonObject();
            signFieldPosition2.addProperty("positionPage", everyParamMapList.get(2).get("positionPage").toString());
            signFieldPosition2.addProperty("positionX", everyParamMapList.get(2).get("positionX").toString());
            signFieldPosition2.addProperty("positionY", everyParamMapList.get(2).get("positionY").toString());
            normalSignFieldConfig2.add("signFieldPosition", signFieldPosition2);
            signField2.add("normalSignFieldConfig", normalSignFieldConfig2);

            // 签署日期参数
            JsonObject signDateConfig2 = new JsonObject();
            signDateConfig2.addProperty("fontSize", 13);
            signDateConfig2.addProperty("showSignDate", 1);
            signField2.add("signDateConfig", signDateConfig2);

            signFields2.add(signField2);
            signer2.add("signFields", signFields2);

            //不再判断是否甲方乙方盖骑缝章   只要是双方盖章默认都盖骑缝章
            //if(reqMap.get(0).get("isCrossPageSeal") != null && "1".equals(reqMap.get(0).get("isCrossPageSeal").toString())){
            JsonObject signField4 = new JsonObject();
            signField4.addProperty("fileId", everyParamMapList.get(0).get("fileId").toString());

            // 签署的形式信息：自动签署、指定印章签署
            JsonObject normalSignFieldConfig4 = new JsonObject();
            normalSignFieldConfig4.addProperty("autoSign", false);
            normalSignFieldConfig4.addProperty("signFieldStyle", 2);
            // normalSignFieldConfig3.addProperty("assignedSealId", reqMap.get(0).get("sealId").toString());

            //签署的位置信息
            JsonObject signFieldPosition4 = new JsonObject();
            signFieldPosition4.addProperty("acrossPageMode", "ALL");//默认所有页码都盖上
            signFieldPosition4.addProperty("positionY", 300);
            normalSignFieldConfig4.add("signFieldPosition", signFieldPosition4);
            signField4.add("normalSignFieldConfig", normalSignFieldConfig4);
            signFields2.add(signField4);
            //}

        }
        signers.add(signer1);
        signers.add(signer2);

        return signers;
    }


}

#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32784 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=10896, tid=14300
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.7******.130-jcef (21.0.7+9) (build 21.0.7+9-b895.130)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.7******.130-jcef (21.0.7+9-b895.130, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitlab.seewintech.com': 

Host: Intel(R) Core(TM) i5-9400 CPU @ 2.90GHz, 6 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Mon Jul  7 10:09:58 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4202) elapsed time: 1.571500 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x0000022f7fedda30):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=14300, stack(0x000000739d200000,0x000000739d300000) (1024K)]


Current CompileTask:
C2:1571 1111       4       sun.security.ec.ECOperations$PointMultiplier::lookup (84 bytes)

Stack: [0x000000739d200000,0x000000739d300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e6049]
V  [jvm.dll+0x8c4343]
V  [jvm.dll+0x8c689e]
V  [jvm.dll+0x8c6f83]
V  [jvm.dll+0x289266]
V  [jvm.dll+0xc675d]
V  [jvm.dll+0xc6c93]
V  [jvm.dll+0xc6885]
V  [jvm.dll+0x6c082c]
V  [jvm.dll+0x61afbe]
V  [jvm.dll+0x609a8b]
V  [jvm.dll+0x6096b5]
V  [jvm.dll+0x828d1a]
V  [jvm.dll+0x82013c]
V  [jvm.dll+0x82be9a]
V  [jvm.dll+0x60d512]
V  [jvm.dll+0x259a22]
V  [jvm.dll+0x259ddf]
V  [jvm.dll+0x252585]
V  [jvm.dll+0x24fdde]
V  [jvm.dll+0x1cd734]
V  [jvm.dll+0x25f76c]
V  [jvm.dll+0x25dcb6]
V  [jvm.dll+0x3ffa86]
V  [jvm.dll+0x86bea8]
V  [jvm.dll+0x6e480d]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000022f4a1b8410, length=15, elements={
0x0000022f695797f0, 0x0000022f7feca780, 0x0000022f7fece000, 0x0000022f7fecf580,
0x0000022f7fed4d10, 0x0000022f7fed5760, 0x0000022f7fed8960, 0x0000022f7fedda30,
0x0000022f451518d0, 0x0000022f4523a2f0, 0x0000022f4523cb30, 0x0000022f4a120090,
0x0000022f4a2bb0d0, 0x0000022f4a163120, 0x0000022f4a020040
}

Java Threads: ( => current thread )
  0x0000022f695797f0 JavaThread "main"                              [_thread_blocked, id=52680, stack(0x000000739c400000,0x000000739c500000) (1024K)]
  0x0000022f7feca780 JavaThread "Reference Handler"          daemon [_thread_blocked, id=5968, stack(0x000000739cc00000,0x000000739cd00000) (1024K)]
  0x0000022f7fece000 JavaThread "Finalizer"                  daemon [_thread_blocked, id=4668, stack(0x000000739cd00000,0x000000739ce00000) (1024K)]
  0x0000022f7fecf580 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=40052, stack(0x000000739ce00000,0x000000739cf00000) (1024K)]
  0x0000022f7fed4d10 JavaThread "Attach Listener"            daemon [_thread_blocked, id=20684, stack(0x000000739cf00000,0x000000739d000000) (1024K)]
  0x0000022f7fed5760 JavaThread "Service Thread"             daemon [_thread_blocked, id=37020, stack(0x000000739d000000,0x000000739d100000) (1024K)]
  0x0000022f7fed8960 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=38312, stack(0x000000739d100000,0x000000739d200000) (1024K)]
=>0x0000022f7fedda30 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=14300, stack(0x000000739d200000,0x000000739d300000) (1024K)]
  0x0000022f451518d0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=38676, stack(0x000000739d300000,0x000000739d400000) (1024K)]
  0x0000022f4523a2f0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=47972, stack(0x000000739d400000,0x000000739d500000) (1024K)]
  0x0000022f4523cb30 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=30740, stack(0x000000739d500000,0x000000739d600000) (1024K)]
  0x0000022f4a120090 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=32096, stack(0x000000739d700000,0x000000739d800000) (1024K)]
  0x0000022f4a2bb0d0 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_native, id=51608, stack(0x000000739d900000,0x000000739da00000) (1024K)]
  0x0000022f4a163120 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=13704, stack(0x000000739da00000,0x000000739db00000) (1024K)]
  0x0000022f4a020040 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=5260, stack(0x000000739d600000,0x000000739d700000) (1024K)]
Total: 15

Other Threads:
  0x0000022f7feac5c0 VMThread "VM Thread"                           [id=50304, stack(0x000000739cb00000,0x000000739cc00000) (1024K)]
  0x0000022f7fe9a1e0 WatcherThread "VM Periodic Task Thread"        [id=46736, stack(0x000000739ca00000,0x000000739cb00000) (1024K)]
  0x0000022f695e1a50 WorkerThread "GC Thread#0"                     [id=14120, stack(0x000000739c500000,0x000000739c600000) (1024K)]
  0x0000022f695f27b0 ConcurrentGCThread "G1 Main Marker"            [id=48932, stack(0x000000739c600000,0x000000739c700000) (1024K)]
  0x0000022f695f32b0 WorkerThread "G1 Conc#0"                       [id=11280, stack(0x000000739c700000,0x000000739c800000) (1024K)]
  0x0000022f6963e740 ConcurrentGCThread "G1 Refine#0"               [id=28820, stack(0x000000739c800000,0x000000739c900000) (1024K)]
  0x0000022f7fd68500 ConcurrentGCThread "G1 Service"                [id=8572, stack(0x000000739c900000,0x000000739ca00000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0  2062 1111       4       sun.security.ec.ECOperations$PointMultiplier::lookup (84 bytes)
C2 CompilerThread1  2062 1113       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000702800000, size: 4056 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000022f04000000-0x0000022f04d10000-0x0000022f04d10000), size 13697024, SharedBaseAddress: 0x0000022f04000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000022f05000000-0x0000022f45000000, reserved size: 1073741824
Narrow klass base: 0x0000022f04000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 6 total, 6 available
 Memory: 16220M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4056M
 Pre-touch: Disabled
 Parallel Workers: 6
 Concurrent Workers: 2
 Concurrent Refinement Workers: 6
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 260096K, used 14336K [0x0000000702800000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 0 survivors (0K)
 Metaspace       used 8032K, committed 8320K, reserved 1114112K
  class space    used 913K, committed 1024K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000| PB 0x0000000702800000| Untracked 
|   1|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000| PB 0x0000000702a00000| Untracked 
|   2|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000| PB 0x0000000702c00000| Untracked 
|   3|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000| PB 0x0000000702e00000| Untracked 
|   4|0x0000000703000000, 0x0000000703000000, 0x0000000703200000|  0%| F|  |TAMS 0x0000000703000000| PB 0x0000000703000000| Untracked 
|   5|0x0000000703200000, 0x0000000703200000, 0x0000000703400000|  0%| F|  |TAMS 0x0000000703200000| PB 0x0000000703200000| Untracked 
|   6|0x0000000703400000, 0x0000000703400000, 0x0000000703600000|  0%| F|  |TAMS 0x0000000703400000| PB 0x0000000703400000| Untracked 
|   7|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000| PB 0x0000000703600000| Untracked 
|   8|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000| PB 0x0000000703800000| Untracked 
|   9|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000| PB 0x0000000703a00000| Untracked 
|  10|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000| PB 0x0000000703c00000| Untracked 
|  11|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000| PB 0x0000000703e00000| Untracked 
|  12|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000| PB 0x0000000704000000| Untracked 
|  13|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000| PB 0x0000000704200000| Untracked 
|  14|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000| PB 0x0000000704400000| Untracked 
|  15|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000| PB 0x0000000704600000| Untracked 
|  16|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000| PB 0x0000000704800000| Untracked 
|  17|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000| PB 0x0000000704a00000| Untracked 
|  18|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000| PB 0x0000000704c00000| Untracked 
|  19|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked 
|  20|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked 
|  21|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked 
|  22|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked 
|  23|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked 
|  24|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked 
|  25|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked 
|  26|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked 
|  27|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked 
|  28|0x0000000706000000, 0x0000000706000000, 0x0000000706200000|  0%| F|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked 
|  29|0x0000000706200000, 0x0000000706200000, 0x0000000706400000|  0%| F|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked 
|  30|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked 
|  31|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked 
|  32|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked 
|  33|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked 
|  34|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked 
|  35|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked 
|  36|0x0000000707000000, 0x0000000707000000, 0x0000000707200000|  0%| F|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked 
|  37|0x0000000707200000, 0x0000000707200000, 0x0000000707400000|  0%| F|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked 
|  38|0x0000000707400000, 0x0000000707400000, 0x0000000707600000|  0%| F|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked 
|  39|0x0000000707600000, 0x0000000707600000, 0x0000000707800000|  0%| F|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked 
|  40|0x0000000707800000, 0x0000000707800000, 0x0000000707a00000|  0%| F|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked 
|  41|0x0000000707a00000, 0x0000000707a00000, 0x0000000707c00000|  0%| F|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked 
|  42|0x0000000707c00000, 0x0000000707c00000, 0x0000000707e00000|  0%| F|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked 
|  43|0x0000000707e00000, 0x0000000707e00000, 0x0000000708000000|  0%| F|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked 
|  44|0x0000000708000000, 0x0000000708000000, 0x0000000708200000|  0%| F|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked 
|  45|0x0000000708200000, 0x0000000708200000, 0x0000000708400000|  0%| F|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked 
|  46|0x0000000708400000, 0x0000000708400000, 0x0000000708600000|  0%| F|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked 
|  47|0x0000000708600000, 0x0000000708600000, 0x0000000708800000|  0%| F|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked 
|  48|0x0000000708800000, 0x0000000708800000, 0x0000000708a00000|  0%| F|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked 
|  49|0x0000000708a00000, 0x0000000708a00000, 0x0000000708c00000|  0%| F|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked 
|  50|0x0000000708c00000, 0x0000000708c00000, 0x0000000708e00000|  0%| F|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked 
|  51|0x0000000708e00000, 0x0000000708e00000, 0x0000000709000000|  0%| F|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked 
|  52|0x0000000709000000, 0x0000000709000000, 0x0000000709200000|  0%| F|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked 
|  53|0x0000000709200000, 0x0000000709200000, 0x0000000709400000|  0%| F|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked 
|  54|0x0000000709400000, 0x0000000709400000, 0x0000000709600000|  0%| F|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked 
|  55|0x0000000709600000, 0x0000000709600000, 0x0000000709800000|  0%| F|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked 
|  56|0x0000000709800000, 0x0000000709800000, 0x0000000709a00000|  0%| F|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked 
|  57|0x0000000709a00000, 0x0000000709a00000, 0x0000000709c00000|  0%| F|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked 
|  58|0x0000000709c00000, 0x0000000709c00000, 0x0000000709e00000|  0%| F|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked 
|  59|0x0000000709e00000, 0x0000000709e00000, 0x000000070a000000|  0%| F|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked 
|  60|0x000000070a000000, 0x000000070a000000, 0x000000070a200000|  0%| F|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked 
|  61|0x000000070a200000, 0x000000070a200000, 0x000000070a400000|  0%| F|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked 
|  62|0x000000070a400000, 0x000000070a400000, 0x000000070a600000|  0%| F|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked 
|  63|0x000000070a600000, 0x000000070a600000, 0x000000070a800000|  0%| F|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked 
|  64|0x000000070a800000, 0x000000070a800000, 0x000000070aa00000|  0%| F|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked 
|  65|0x000000070aa00000, 0x000000070aa00000, 0x000000070ac00000|  0%| F|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked 
|  66|0x000000070ac00000, 0x000000070ac00000, 0x000000070ae00000|  0%| F|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked 
|  67|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|  68|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|  69|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|  70|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|  71|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|  72|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|  73|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|  74|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|  75|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|  76|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  77|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  78|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  79|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
|  80|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
|  81|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
|  82|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
|  83|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
|  84|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
|  85|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
|  86|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
|  87|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
|  88|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
|  89|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
|  90|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
|  91|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
|  92|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
|  93|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
|  94|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
|  95|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
|  96|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked 
|  97|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
|  98|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
|  99|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
| 100|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
| 101|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
| 102|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
| 103|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
| 104|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked 
| 105|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked 
| 106|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Untracked 
| 107|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000| PB 0x000000070fe00000| Untracked 
| 108|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000| PB 0x0000000710000000| Untracked 
| 109|0x0000000710200000, 0x0000000710200000, 0x0000000710400000|  0%| F|  |TAMS 0x0000000710200000| PB 0x0000000710200000| Untracked 
| 110|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000| PB 0x0000000710400000| Untracked 
| 111|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000| PB 0x0000000710600000| Untracked 
| 112|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000| PB 0x0000000710800000| Untracked 
| 113|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000| PB 0x0000000710a00000| Untracked 
| 114|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000| PB 0x0000000710c00000| Untracked 
| 115|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000| PB 0x0000000710e00000| Untracked 
| 116|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000| PB 0x0000000711000000| Untracked 
| 117|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000| PB 0x0000000711200000| Untracked 
| 118|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000| PB 0x0000000711400000| Untracked 
| 119|0x0000000711600000, 0x000000071179a060, 0x0000000711800000| 80%| E|  |TAMS 0x0000000711600000| PB 0x0000000711600000| Complete 
| 120|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%| E|CS|TAMS 0x0000000711800000| PB 0x0000000711800000| Complete 
| 121|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| E|CS|TAMS 0x0000000711a00000| PB 0x0000000711a00000| Complete 
| 122|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| E|CS|TAMS 0x0000000711c00000| PB 0x0000000711c00000| Complete 
| 123|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| E|CS|TAMS 0x0000000711e00000| PB 0x0000000711e00000| Complete 
| 124|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| E|CS|TAMS 0x0000000712000000| PB 0x0000000712000000| Complete 
| 125|0x0000000712200000, 0x0000000712400000, 0x0000000712400000|100%| E|CS|TAMS 0x0000000712200000| PB 0x0000000712200000| Complete 
| 126|0x0000000712400000, 0x0000000712600000, 0x0000000712600000|100%| E|CS|TAMS 0x0000000712400000| PB 0x0000000712400000| Complete 

Card table byte_map: [0x0000022f7cec0000,0x0000022f7d6b0000] _byte_map_base: 0x0000022f796ac000

Marking Bits: (CMBitMap*) 0x0000022f695e2150
 Bits: [0x0000022f00000000, 0x0000022f03f60000)

Polling page: 0x0000022f67400000

Metaspace:

Usage:
  Non-class:      6.95 MB used.
      Class:    913.57 KB used.
       Both:      7.84 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.12 MB ( 11%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.00 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.12 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.42 MB
       Class:  15.00 MB
        Both:  23.42 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 224.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 130.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 372.
num_chunk_merges: 0.
num_chunk_splits: 215.
num_chunks_enlarged: 98.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=400Kb max_used=400Kb free=119599Kb
 bounds [0x0000022f75090000, 0x0000022f75300000, 0x0000022f7c5c0000]
CodeHeap 'profiled nmethods': size=120000Kb used=1767Kb max_used=1767Kb free=118232Kb
 bounds [0x0000022f6d5c0000, 0x0000022f6d830000, 0x0000022f74af0000]
CodeHeap 'non-nmethods': size=5760Kb used=1430Kb max_used=1463Kb free=4329Kb
 bounds [0x0000022f74af0000, 0x0000022f74d60000, 0x0000022f75090000]
 total_blobs=1630 nmethods=1115 adapters=420
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.331 Thread 0x0000022f451518d0 1109       3       sun.security.util.math.intpoly.IntegerPolynomial$Element::<init> (40 bytes)
Event: 1.331 Thread 0x0000022f451518d0 nmethod 1109 0x0000022f6d778110 code [0x0000022f6d7782c0, 0x0000022f6d778610]
Event: 1.332 Thread 0x0000022f4a020040 nmethod 1106 0x0000022f750f1c90 code [0x0000022f750f1e40, 0x0000022f750f2158]
Event: 1.332 Thread 0x0000022f4a020040 1102       4       sun.security.ec.point.ProjectivePoint$Mutable::conditionalSet (53 bytes)
Event: 1.332 Thread 0x0000022f451518d0 1110       3       sun.security.util.ArrayUtil::swap (15 bytes)
Event: 1.333 Thread 0x0000022f451518d0 nmethod 1110 0x0000022f6d778710 code [0x0000022f6d7788c0, 0x0000022f6d778a08]
Event: 1.336 Thread 0x0000022f7fedda30 nmethod 1100 0x0000022f750f2310 code [0x0000022f750f24c0, 0x0000022f750f2a18]
Event: 1.336 Thread 0x0000022f7fedda30 1111       4       sun.security.ec.ECOperations$PointMultiplier::lookup (84 bytes)
Event: 1.336 Thread 0x0000022f451518d0 1112       3       sun.security.ec.point.ProjectivePoint::<init> (20 bytes)
Event: 1.337 Thread 0x0000022f451518d0 nmethod 1112 0x0000022f6d778b10 code [0x0000022f6d778cc0, 0x0000022f6d778f58]
Event: 1.337 Thread 0x0000022f451518d0 1114       3       sun.security.util.math.intpoly.IntegerPolynomial::get0 (10 bytes)
Event: 1.338 Thread 0x0000022f451518d0 nmethod 1114 0x0000022f6d779010 code [0x0000022f6d7791c0, 0x0000022f6d7793c8]
Event: 1.338 Thread 0x0000022f451518d0 1115       3       sun.security.util.math.intpoly.IntegerPolynomial::get0 (5 bytes)
Event: 1.338 Thread 0x0000022f451518d0 nmethod 1115 0x0000022f6d779510 code [0x0000022f6d7796c0, 0x0000022f6d7799b0]
Event: 1.352 Thread 0x0000022f451518d0 1117       3       sun.security.ec.ECOperations$PointMultiplier$Secp256R1GeneratorMultiplier::bit (13 bytes)
Event: 1.563 Thread 0x0000022f451518d0 nmethod 1117 0x0000022f6d779b10 code [0x0000022f6d779ca0, 0x0000022f6d779dc8]
Event: 1.564 Thread 0x0000022f4a020040 nmethod 1102 0x0000022f750f2b90 code [0x0000022f750f2d80, 0x0000022f750f3740]
Event: 1.564 Thread 0x0000022f4a020040 1116       4       sun.security.util.math.intpoly.IntegerPolynomial$MutableElement::setAdditiveInverse (32 bytes)
Event: 1.566 Thread 0x0000022f4a020040 nmethod 1116 0x0000022f750f3c10 code [0x0000022f750f3da0, 0x0000022f750f3f90]
Event: 1.566 Thread 0x0000022f4a020040 1113       4       sun.security.ec.ECOperations::setDouble (463 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.012 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.dll
Event: 0.046 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.229 Thread 0x0000022f695797f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000022f7509cd70 relative=0x00000000000000d0
Event: 0.229 Thread 0x0000022f695797f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000022f7509cd70 method=java.lang.String.charAt(I)C @ 4 c2
Event: 0.229 Thread 0x0000022f695797f0 DEOPT PACKING pc=0x0000022f7509cd70 sp=0x000000739c4fdf40
Event: 0.229 Thread 0x0000022f695797f0 DEOPT UNPACKING pc=0x0000022f74b446a2 sp=0x000000739c4fdee0 mode 2
Event: 0.245 Thread 0x0000022f695797f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000022f750b0530 relative=0x0000000000000090
Event: 0.245 Thread 0x0000022f695797f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000022f750b0530 method=jdk.internal.misc.Unsafe.convEndian(ZI)I @ 4 c2
Event: 0.245 Thread 0x0000022f695797f0 DEOPT PACKING pc=0x0000022f750b0530 sp=0x000000739c4fe160
Event: 0.245 Thread 0x0000022f695797f0 DEOPT UNPACKING pc=0x0000022f74b446a2 sp=0x000000739c4fe090 mode 2
Event: 0.417 Thread 0x0000022f695797f0 DEOPT PACKING pc=0x0000022f6d5d904c sp=0x000000739c4fc9a0
Event: 0.417 Thread 0x0000022f695797f0 DEOPT UNPACKING pc=0x0000022f74b44e42 sp=0x000000739c4fbdc0 mode 0
Event: 0.480 Thread 0x0000022f695797f0 DEOPT PACKING pc=0x0000022f6d5d70cc sp=0x000000739c4fba50
Event: 0.480 Thread 0x0000022f695797f0 DEOPT UNPACKING pc=0x0000022f74b44e42 sp=0x000000739c4faeb8 mode 0
Event: 0.599 Thread 0x0000022f4a120090 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000022f750b09a8 relative=0x00000000000001c8
Event: 0.599 Thread 0x0000022f4a120090 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000022f750b09a8 method=java.lang.invoke.VarHandle.checkAccessModeThenIsDirect(Ljava/lang/invoke/VarHandle$AccessDescriptor;)Z @ 4 c2
Event: 0.599 Thread 0x0000022f4a120090 DEOPT PACKING pc=0x0000022f750b09a8 sp=0x000000739d7ff270
Event: 0.599 Thread 0x0000022f4a120090 DEOPT UNPACKING pc=0x0000022f74b446a2 sp=0x000000739d7ff1a8 mode 2
Event: 0.687 Thread 0x0000022f4a120090 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000022f750df1c4 relative=0x0000000000000f44
Event: 0.687 Thread 0x0000022f4a120090 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000022f750df1c4 method=java.util.concurrent.ConcurrentHashMap.putVal(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object; @ 159 c2
Event: 0.687 Thread 0x0000022f4a120090 DEOPT PACKING pc=0x0000022f750df1c4 sp=0x000000739d7fdae0
Event: 0.687 Thread 0x0000022f4a120090 DEOPT UNPACKING pc=0x0000022f74b446a2 sp=0x000000739d7fda60 mode 2

Classes loaded (20 events):
Event: 0.769 Loading class sun/security/ssl/XDHKeyExchange$1
Event: 0.769 Loading class sun/security/ssl/XDHKeyExchange$1 done
Event: 0.769 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry
Event: 0.769 Loading class sun/security/ssl/KeyShareExtension$KeyShareEntry done
Event: 0.769 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession
Event: 0.769 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossession done
Event: 0.786 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384
Event: 0.786 Loading class sun/security/util/math/intpoly/IntegerPolynomialP384 done
Event: 0.786 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521
Event: 0.786 Loading class sun/security/util/math/intpoly/IntegerPolynomialP521 done
Event: 0.786 Loading class sun/security/util/math/intpoly/P384OrderField
Event: 0.787 Loading class sun/security/util/math/intpoly/P384OrderField done
Event: 0.803 Loading class sun/security/util/math/intpoly/P521OrderField
Event: 0.803 Loading class sun/security/util/math/intpoly/P521OrderField done
Event: 0.804 Loading class java/security/interfaces/ECPrivateKey
Event: 0.804 Loading class java/security/interfaces/ECPrivateKey done
Event: 0.805 Loading class sun/security/util/ArrayUtil
Event: 0.805 Loading class sun/security/util/ArrayUtil done
Event: 1.000 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1
Event: 1.000 Loading class sun/security/util/math/IntegerModuloP$MultiplicativeInverser$Secp256R1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.402 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f8a3b0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x0000000711f8a3b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.403 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f919c0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000711f919c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.403 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f98458}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x0000000711f98458) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.404 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711f9eee8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000711f9eee8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.404 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711fa35d8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000711fa35d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.412 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711fcd3b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000711fcd3b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.412 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711fd0d48}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000711fd0d48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.417 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711fe0068}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711fe0068) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.421 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711c124b8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x0000000711c124b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.423 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711c18d88}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711c18d88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.423 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711c1c1a8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711c1c1a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.549 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711db76b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711db76b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.583 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711ba6df8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000711ba6df8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.583 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711baa760}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000711baa760) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.589 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711bd8808}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000711bd8808) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.600 Thread 0x0000022f4a120090 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711a8a608}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000711a8a608) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.601 Thread 0x0000022f695797f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007118684f8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007118684f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.625 Thread 0x0000022f4a120090 Exception <a 'java/lang/NoSuchMethodError'{0x0000000711ac46c0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x0000000711ac46c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.651 Thread 0x0000022f4a2bb0d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007118f1260}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007118f1260) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.681 Thread 0x0000022f4a2bb0d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007119415b8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000007119415b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 0.078 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.081 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.086 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.086 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.225 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.225 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.602 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.621 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.055 Thread 0x0000022f695797f0 Thread added: 0x0000022f7fecf580
Event: 0.055 Thread 0x0000022f695797f0 Thread added: 0x0000022f7fed4d10
Event: 0.055 Thread 0x0000022f695797f0 Thread added: 0x0000022f7fed5760
Event: 0.055 Thread 0x0000022f695797f0 Thread added: 0x0000022f7fed8960
Event: 0.055 Thread 0x0000022f695797f0 Thread added: 0x0000022f7fedda30
Event: 0.057 Thread 0x0000022f695797f0 Thread added: 0x0000022f451518d0
Event: 0.068 Thread 0x0000022f695797f0 Thread added: 0x0000022f4523a2f0
Event: 0.070 Thread 0x0000022f695797f0 Thread added: 0x0000022f4523cb30
Event: 0.072 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\net.dll
Event: 0.073 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\nio.dll
Event: 0.076 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\zip.dll
Event: 0.121 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\jimage.dll
Event: 0.130 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\sunmscapi.dll
Event: 0.235 Thread 0x0000022f451518d0 Thread added: 0x0000022f4a020040
Event: 0.532 Loaded shared library D:\IntelliJ IDEA 2025.1.3\jbr\bin\extnet.dll
Event: 0.544 Thread 0x0000022f695797f0 Thread added: 0x0000022f4a120090
Event: 0.625 Thread 0x0000022f4a120090 Thread added: 0x0000022f4a2bb0d0
Event: 0.687 Thread 0x0000022f4a120090 Thread added: 0x0000022f4a163120
Event: 0.938 Thread 0x0000022f4a020040 Thread exited: 0x0000022f4a020040
Event: 1.323 Thread 0x0000022f451518d0 Thread added: 0x0000022f4a020040


Dynamic libraries:
0x00007ff7ae320000 - 0x00007ff7ae32a000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.exe
0x00007ffa153e0000 - 0x00007ffa15645000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa143f0000 - 0x00007ffa144b9000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa12910000 - 0x00007ffa12cf8000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa13030000 - 0x00007ffa1317b000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa093e0000 - 0x00007ffa093f8000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jli.dll
0x00007ffa13d70000 - 0x00007ffa13f3a000 	C:\Windows\System32\USER32.dll
0x00007ffa12e80000 - 0x00007ffa12ea7000 	C:\Windows\System32\win32u.dll
0x00007ffa144c0000 - 0x00007ffa144eb000 	C:\Windows\System32\GDI32.dll
0x00007ffa12530000 - 0x00007ffa12667000 	C:\Windows\System32\gdi32full.dll
0x00007ffa12860000 - 0x00007ffa12903000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa0d8a0000 - 0x00007ffa0d8bb000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\VCRUNTIME140.dll
0x00007ff9e2090000 - 0x00007ff9e232a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffa146b0000 - 0x00007ffa14759000 	C:\Windows\System32\msvcrt.dll
0x00007ffa148f0000 - 0x00007ffa14920000 	C:\Windows\System32\IMM32.DLL
0x00007ffa0dfd0000 - 0x00007ffa0dfdc000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\vcruntime140_1.dll
0x00007ff9f0a80000 - 0x00007ff9f0b0d000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\msvcp140.dll
0x00007ff9642a0000 - 0x00007ff965061000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\server\jvm.dll
0x00007ffa14760000 - 0x00007ffa14813000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa13330000 - 0x00007ffa133d6000 	C:\Windows\System32\sechost.dll
0x00007ffa14ab0000 - 0x00007ffa14bc5000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa13560000 - 0x00007ffa135d4000 	C:\Windows\System32\WS2_32.dll
0x00007ffa122d0000 - 0x00007ffa1232e000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa04c10000 - 0x00007ffa04c45000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa0e190000 - 0x00007ffa0e19b000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa122b0000 - 0x00007ffa122c4000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa112f0000 - 0x00007ffa1130b000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa0d970000 - 0x00007ffa0d97a000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\jimage.dll
0x00007ffa06230000 - 0x00007ffa06471000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa13980000 - 0x00007ffa13d05000 	C:\Windows\System32\combase.dll
0x00007ffa14930000 - 0x00007ffa14a11000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa03bf0000 - 0x00007ffa03c29000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa12670000 - 0x00007ffa12709000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa09e20000 - 0x00007ffa09e40000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\java.dll
0x00007ffa14c40000 - 0x00007ffa15382000 	C:\Windows\System32\SHELL32.dll
0x00007ffa12d00000 - 0x00007ffa12e74000 	C:\Windows\System32\wintypes.dll
0x00007ffa101d0000 - 0x00007ffa10a28000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa136a0000 - 0x00007ffa13791000 	C:\Windows\System32\SHCORE.dll
0x00007ffa14bd0000 - 0x00007ffa14c3a000 	C:\Windows\System32\shlwapi.dll
0x00007ffa12440000 - 0x00007ffa1246f000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa093c0000 - 0x00007ffa093d8000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\zip.dll
0x00007ffa09180000 - 0x00007ffa09190000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\net.dll
0x00007ffa0db10000 - 0x00007ffa0dc2e000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffa11860000 - 0x00007ffa118ca000 	C:\Windows\system32\mswsock.dll
0x00007ffa045d0000 - 0x00007ffa045e6000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\nio.dll
0x00007ffa02d80000 - 0x00007ffa02d8e000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\sunmscapi.dll
0x00007ffa12eb0000 - 0x00007ffa13027000 	C:\Windows\System32\CRYPT32.dll
0x00007ffa11d30000 - 0x00007ffa11d60000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffa11ce0000 - 0x00007ffa11d1f000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffa11b10000 - 0x00007ffa11b2b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffa11250000 - 0x00007ffa1128a000 	C:\Windows\system32\rsaenh.dll
0x00007ffa11900000 - 0x00007ffa1192b000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffa12410000 - 0x00007ffa12436000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffa11b30000 - 0x00007ffa11b3c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffa10d20000 - 0x00007ffa10d53000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffa143d0000 - 0x00007ffa143da000 	C:\Windows\System32\NSI.dll
0x00007ff9e0070000 - 0x00007ff9e0078000 	C:\Windows\system32\wshunix.dll
0x00007ff9fdae0000 - 0x00007ff9fdae9000 	D:\IntelliJ IDEA 2025.1.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\IntelliJ IDEA 2025.1.3\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;D:\IntelliJ IDEA 2025.1.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitlab.seewintech.com': 
java_class_path (initial): D:/IntelliJ IDEA 2025.1.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/IntelliJ IDEA 2025.1.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 6                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4253024256                                {product} {ergonomic}
   size_t MaxNewSize                               = 2550136832                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4253024256                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\jdk-17.0.10
CLASSPATH=.;D:\jdk-17.0.10\lib;D:\jdk-17.0.10\lib\tools.jar
PATH=D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\libexec\git-core;D:\Git\mingw64\bin;D:\Git\usr\bin;C:\Users\<USER>\bin;D:\ShadowBot;D:\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;D:\jdk-17.0.10\bin;E:\JavaSpace\peizhi\apache-maven-3.5.4\apache-maven-3.5.4\bin;D:\Git\cmd;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;D:\cursor\resources\app\bin;E:\node;C:\Program Files\dotnet;D:\ShadowBot;C:\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\JetBrains\PyCharm 2024.3.4\bin;D:\JetBrains\IntelliJ IDEA 2024.1.7\bin;D:\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Microsoft VS Code\bin;D:\IntelliJ IDEA 2025.1.3\bin
USERNAME=Administrator
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 81388K (0% of 16609336K total physical memory with 2805868K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 6437K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1578K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16400B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 25 days 14:38 hours

CPU: total 6 (initial active 6) (6 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xde, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 6 processors :
  Max Mhz: 2904, Current Mhz: 2904, Mhz Limit: 2904

Memory: 4k page, system-wide physical 16220M (2740M free)
TotalPageFile size 32693M (AvailPageFile size 60M)
current process WorkingSet (physical memory assigned to process): 80M, peak: 80M
current process commit charge ("private bytes"): 353M, peak: 354M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+9-b895.130) for windows-amd64 JRE (21.0.7+9-b895.130), built on 2025-05-13 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.

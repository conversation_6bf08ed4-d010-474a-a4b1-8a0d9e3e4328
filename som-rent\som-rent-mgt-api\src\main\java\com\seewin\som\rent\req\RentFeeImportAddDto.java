package com.seewin.som.rent.req;

import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 费用收缴导入
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-24
 */
@Getter
@Setter
public class RentFeeImportAddDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 导入状态 1：已完成
     */
    private Integer status;

    /**
     * 总数
     */
    private Integer totalCount;

    /**
     * 失败数
     */
    private Integer failCount;

    /**
     * 失败明细文件
     */
    private Long failFileId;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 业务类型 '1应收管理 2实收管理 3费用管理'
     */
    private Integer bizType;
}

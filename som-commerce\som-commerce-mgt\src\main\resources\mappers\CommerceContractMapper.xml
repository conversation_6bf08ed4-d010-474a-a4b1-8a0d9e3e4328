<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.commerce.mapper.CommerceContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.commerce.entity.CommerceContract">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="order_code" property="orderCode" />
        <result column="contract_code" property="contractCode" />
        <result column="approve_status" property="approveStatus" />
        <result column="process_instance_id" property="processInstanceId" />
        <result column="apply_date" property="applyDate" />
        <result column="approve_date" property="approveDate" />
        <result column="archive_date" property="archiveDate" />
        <result column="approve_remark" property="approveRemark" />
        <result column="execute_user" property="executeUser" />
        <result column="execute_user_name" property="executeUserName" />
        <result column="execute_user_fid" property="executeUserFid" />
        <result column="execute_user_fname" property="executeUserFname" />
        <result column="fcode" property="fcode" />
        <result column="fname" property="fname" />
        <result column="room_id" property="roomId" />
        <result column="name" property="name" />
        <result column="room_shop_id" property="roomShopId" />
        <result column="actual_area" property="actualArea" />
        <result column="rent_area" property="rentArea" />
        <result column="room_status" property="roomStatus" />
        <result column="empty_start_date" property="emptyStartDate" />
        <result column="last_contract_date" property="lastContractDate" />
        <result column="brand_id" property="brandId" />
        <result column="brand_name" property="brandName" />
        <result column="commercial_type_code" property="commercialTypeCode" />
        <result column="commercial_type_name" property="commercialTypeName" />
        <result column="category_id" property="categoryId" />
        <result column="category_name" property="categoryName" />
        <result column="electricity" property="electricity" />
        <result column="water_supply" property="waterSupply" />
        <result column="drainage" property="drainage" />
        <result column="exhaust_fumes" property="exhaustFumes" />
        <result column="data_type" property="dataType" />
        <result column="user_input" property="userInput"/>
        <result column="f_address_detail" property="faddressdetail" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
    </resultMap>
    <select id="getLastContract" resultMap="BaseResultMap">
        select *
        from som_commerce_contract
        where del_status = 0
        and room_id = #{roomId}
        and approve_status = 3
        order by approve_date desc
        limit 1
    </select>
    <select id="historyList" resultMap="BaseResultMap">
        SELECT
        cc.*
        FROM
        som_commerce_contract cc
        LEFT JOIN som_commerce_contract_info cci ON cc.contract_code = cci.contract_code
        WHERE
        cc.approve_status = 3
        AND act_retrive_date <![CDATA[ < ]]> NOW()
        <if test="null != listDto.roomId">
            and room_id = #{listDto.roomId}
        </if>
        <if test="null != listDto.roomShopId and '' != listDto.roomShopId">
            and room_shop_id = #{listDto.roomShopId}
        </if>
        ORDER BY cc.create_time DESC
    </select>
    <select id="getMaxShopId" resultType="java.lang.String">
        select max(room_shop_id)
        from som_commerce_contract
        where room_id = #{roomId}
        and room_shop_id is not null
    </select>
    <select id="getShopIdCount" resultType="java.lang.Long">
        select count(distinct room_shop_id)
        from som_commerce_contract
        where room_id = #{roomId}
        and room_shop_id is not null
    </select>

    <select id="selectByRoomIdAndApproveStatus" resultMap="BaseResultMap">
        SELECT
        *
        FROM `som_commerce_contract`
        WHERE
        room_id = #{roomId}
        <if test="approveStatus != null">
            AND approve_status = #{approveStatus}
        </if>
        <if test="approveStatus == null">
            AND approve_status is NULL
        </if>
        LIMIT 1
    </select>
    <select id="selectRentStartContract" resultType="com.seewin.som.commerce.resp.CommerceContractArchiveVo">
        SELECT aa.*,e.id as entexitId FROM
        ( select c.*, ROW_NUMBER() OVER ( PARTITION BY c.room_id ORDER BY create_time DESC ) AS rn
        from som_commerce_contract c,som_commerce_contract_info i
        where c.tenant_id  = i.tenant_id
        and c.contract_code  = i.contract_code
        and 	GREATEST(
        COALESCE(c.approve_date, '1000-01-01'),
        COALESCE(i.rent_start_date, '1000-01-01')
        ) = #{date}
        and c.approve_status = 3
        and c.del_status = 0
        and c.approve_date  is not null
        and i.rent_start_date is not null
<!--        and not EXISTS (select * from som_commerce_entexit e where e.tenant_id = c.tenant_id and e.room_id = c.room_shop_id)-->
        ) as aa left join som_commerce_entexit e on aa.tenant_id = e.tenant_id  and aa.room_id = e.shop_id and aa.room_shop_id = e.room_id  and e.del_status =0
        WHERE rn = 1
    </select>

    <!-- 获取本月所需要生成账单的有效期内合同 -->
    <select id="selectContractByBillMonth" resultType="com.seewin.som.commerce.resp.CommerceContractBillMonthVo" parameterType="string">
        SELECT
        cc.room_id, -- 房间ID
        cc.contract_code, -- 合同编号
        cc.brand_id, -- 品牌ID
        cc.brand_name, -- 签约品牌名称

        cci.rent_type, -- 租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
        cci.rent_start_date, -- 租赁起始时间
        cci.rent_end_date, -- 租赁结束时间

        ccr.month_manage_fee_contract,-- 月物业管理费总额（元）
        ccr.month_operate_fee_contract, -- 月运营管理费总额（元）
        ccr.other_fee_contract, -- 其他费用总额（元）
        ccr.month_fee_contract, -- 月租金总额（元）
        ccr.month_fee_percent -- 扣点百分率

        FROM
        som_commerce_contract cc
        LEFT JOIN som_commerce_contract_info cci ON cc.contract_code = cci.contract_code
        LEFT JOIN som_commerce_contract_rent ccr ON cc.contract_code = ccr.contract_code
        WHERE
        cc.del_status = 0
        AND cc.approve_status = 3
        AND DATE_FORMAT(cci.rent_start_date, '%Y-%m')  <![CDATA[ <= ]]> #{billMonth}
        AND DATE_FORMAT(cci.rent_end_date, '%Y-%m')  <![CDATA[ >= ]]> #{billMonth}
        -- 此条件为了排除提前撤场的合同
        AND (act_retrive_date is NULL OR DATE_FORMAT(cci.act_retrive_date, '%Y-%m') <![CDATA[ >= ]]> #{billMonth})
    </select>
    
    <select id="selectContractApproveOvertime" resultType="com.seewin.som.commerce.entity.CommerceContract">
        select c.*
        from som_commerce_contract c,som_commerce_contract_info i
        where c.tenant_id  = i.tenant_id
        and c.contract_code  = i.contract_code
        and i.rent_start_date = #{date}
        and c.approve_status = 2
    </select>

    <select id="selectContractMonthFee" resultType="com.seewin.som.commerce.resp.CommerceContractMonthFeeVo" parameterType="string">
        SELECT
            cc.tenant_id,-- 项目id
            cc.tenant_name,-- 项目名称
            cc.contract_code,-- 合同编号
            cc.room_id,-- 房间ID
            cc.brand_id,-- 品牌ID
            cc.brand_name,-- 签约品牌名称
            cc.room_shop_id,-- 门店编码

            cci.contract_type,-- 签约类型：1 新签，2 续签
            cci.rent_type,-- 租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
            cci.rent_start_date,-- 租赁起始时间
            cci.rent_end_date,-- 租赁结束时间
            cci.rent_fee_date,-- 商户计租日期
            cci.act_retrive_date,-- 计费截止日期
            cci.points_mode,-- 扣点方式
            cci.rent_increase_mode,-- 租金方式
            cci.operate_increase_mode,   -- 月运营管理费递增方式
            cci.manage_increase_mode,    -- 月物业管理费递增方式
            cci.settlement_date,-- 结算日期

            ccr.month_fee_contract,-- 月租金总额（元）
            ccr.incremental_rate_list,-- 租金递增集合
            ccr.month_fee_percent_list,-- 扣点百分率集合
            ccr.operate_incremental_rate_list, -- 月运营管理费递增集合
            ccr.manage_incremental_rate_list,  -- 月物业管理费递增集合
            ccr.month_manage_fee_contract, -- 月物业管理费总额（元）
            ccr.month_operate_fee_contract, -- 月运营管理费总额（元）
            ccr.other_fee_contract -- 其他费用总额（元）
        FROM
            `som_commerce_contract` cc
                LEFT JOIN som_commerce_contract_info cci ON cc.contract_code = cci.contract_code AND cc.tenant_id =cci.tenant_id
                LEFT JOIN som_commerce_contract_rent ccr ON cc.contract_code = ccr.contract_code AND cc.tenant_id =ccr.tenant_id
        WHERE
            cc.del_status = 0
          AND cc.approve_status = 3
        <if test="type == 'rentEndDate'">
            AND cci.rent_end_date > DATE_SUB( NOW(), INTERVAL 1 MONTH )
        </if>
        <if test="type == 'rentFeeDate'">
            AND cci.rent_fee_date = CURDATE() + INTERVAL 1 DAY
        </if>


    </select>
    <select id="getLastOneContract" resultMap="BaseResultMap">
        select *
        from som_commerce_contract
        where del_status = 0
        and id &lt; #{id}
        and approve_status = 3
        and room_id = (select room_id from som_commerce_contract where id = #{id})
        order by approve_date desc
        limit 1
    </select>

    <select id="selectEffectiveContract" resultType="com.seewin.som.commerce.resp.CommerceEffectiveContractVo">
        SELECT cc.tenant_id,                      -- 项目ID
               cc.room_shop_id store_id,         -- 门店ID
               cc.room_id,                       -- 房间ID
               cc.contract_code,                 -- 合同编号
               cc.rent_area,                     -- 计租面积
               cc.brand_id,                      -- 品牌ID
               cc.brand_name,                    -- 签约品牌名称
               cci.rent_type,                    -- 租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
               cci.rent_start_date,              -- 租赁起始时间
               cci.rent_end_date,                -- 租赁结束时间
               cci.rent_fee_date,                -- 计租日期
               cci.rent_increase_mode,           -- 租金递增方式
               cci.points_mode,                  -- 扣点比例
               cci.operate_increase_mode,        -- 月运营管理费递增方式
               cci.manage_increase_mode,         -- 月物业管理费递增方式
               ccr.month_fee_contract,           -- 月租金总额
               ccr.guarant_sale_contract,        -- 月保底销售额
               ccr.month_manage_price_contract,  -- 物业管理费单价
               ccr.month_operate_price_contract, -- 运营管理费单价
               ccr.month_manage_fee_contract,    -- 月物业管理费总额
               ccr.month_operate_fee_contract,   -- 月运营管理费总额
               ccr.month_fee_percent,            -- 扣点百分率
               ccr.incremental_rate_list,        -- 租金递增集合
               ccr.month_fee_percent_list,        -- 扣点百分率集合
               ccr.operate_incremental_rate_list, -- 月运营管理费递增集合
               ccr.manage_incremental_rate_list,  -- 月物业管理费递增集合
               cci.act_retrive_date,
               cc.approve_date,
               ccr.decoration_property_fee,
               ccr.decoration_operate_fee,
               ccr.other_fee_contract,
               ccr.composite_manage_fee_contract,
               ccr.decoration_bail_fee_contract,
               ccr.rent_bail_fee_contract,
               ccr.operate_manage_bail_fee_contract,
               ccr.property_manage_bail_fee_contract,
               ccr.common_bail_fee_contract,
               cci.shop_type
        FROM som_commerce_contract cc
                 LEFT JOIN som_commerce_contract_info cci ON cc.contract_code = cci.contract_code
                 LEFT JOIN som_commerce_contract_rent ccr ON cc.contract_code = ccr.contract_code
        WHERE cc.del_status = 0
          and cci.del_status=0
          and ccr.del_status=0
          AND cc.approve_status = 3
          and ((#{currentDate} BETWEEN cci.rent_start_date AND cci.rent_end_date) or #{currentDate}=cc.approve_date)
          -- 此条件为了排除提前撤场的合同
          AND (act_retrive_date is NULL OR cci.act_retrive_date <![CDATA[ >= ]]> #{currentDate})
    </select>
    <select id="selectEffectiveContractByContractCode" resultType="com.seewin.som.commerce.resp.CommerceEffectiveContractVo">
        SELECT cc.tenant_id,                      -- 项目ID
               cc.room_shop_id store_id,         -- 门店ID
               cc.room_id,                       -- 房间ID
               cc.contract_code,                 -- 合同编号
               cc.rent_area,                     -- 计租面积
               cc.brand_id,                      -- 品牌ID
               cc.brand_name,                    -- 签约品牌名称
               cci.rent_type,                    -- 租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
               cci.rent_start_date,              -- 租赁起始时间
               cci.rent_end_date,                -- 租赁结束时间
               cci.rent_fee_date,                -- 计租日期
               cci.rent_increase_mode,           -- 租金递增方式
               cci.points_mode,                  -- 扣点比例
               cci.operate_increase_mode,        -- 月运营管理费递增方式
               cci.manage_increase_mode,         -- 月物业管理费递增方式
               ccr.month_fee_contract,           -- 月租金总额
               ccr.guarant_sale_contract,        -- 月保底销售额
               ccr.month_manage_price_contract,  -- 物业管理费单价
               ccr.month_operate_price_contract, -- 运营管理费单价
               ccr.month_manage_fee_contract,    -- 月物业管理费总额
               ccr.month_operate_fee_contract,   -- 月运营管理费总额
               ccr.month_fee_percent,            -- 扣点百分率
               ccr.incremental_rate_list,        -- 租金递增集合
               ccr.month_fee_percent_list,        -- 扣点百分率集合
               ccr.operate_incremental_rate_list, -- 月运营管理费递增集合
               ccr.manage_incremental_rate_list,  -- 月物业管理费递增集合
               cci.act_retrive_date,
               ccr.decoration_property_fee,
               ccr.decoration_operate_fee,
               ccr.other_fee_contract,
               ccr.composite_manage_fee_contract,
               ccr.decoration_bail_fee_contract,
               ccr.rent_bail_fee_contract,
               ccr.operate_manage_bail_fee_contract,
               ccr.property_manage_bail_fee_contract,
               ccr.common_bail_fee_contract,
               cci.shop_type
        FROM som_commerce_contract cc
                 LEFT JOIN som_commerce_contract_info cci ON cc.contract_code = cci.contract_code
                 LEFT JOIN som_commerce_contract_rent ccr ON cc.contract_code = ccr.contract_code
        WHERE cc.del_status = 0
          and cci.del_status=0
          and ccr.del_status=0
          AND cc.approve_status = 3
          and
        cc.contract_code in
        <foreach collection="contractCodeList" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="getRentFeeCalculateInfo" resultType="com.seewin.som.commerce.resp.CommerceEffectiveContractVo">
        SELECT cc.tenant_id,                      -- 项目ID
               cc.room_shop_id store_id,         -- 门店ID
               cc.room_id,                       -- 房间ID
               cc.contract_code,                 -- 合同编号
               cc.rent_area,                     -- 计租面积
               cc.brand_id,                      -- 品牌ID
               cc.brand_name,                    -- 签约品牌名称
               cci.supplier_id,                  -- 签约供应商名称ID
               cci.supplier_name,                -- 签约供应商名称
               cci.rent_type,                    -- 租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
               cci.rent_start_date,              -- 租赁起始时间
               cci.rent_end_date,                -- 租赁结束时间
               cci.rent_fee_date,                -- 计租日期
               cci.rent_increase_mode,           -- 租金递增方式
               cci.points_mode,                  -- 扣点方式
               cci.operate_increase_mode,        -- 月运营管理费递增方式
               cci.manage_increase_mode,         -- 月物业管理费递增方式
               ccr.month_fee_contract,           -- 月租金总额
               ccr.guarant_sale_contract,        -- 月保底销售额
               ccr.month_manage_price_contract,  -- 物业管理费单价
               ccr.month_operate_price_contract, -- 运营管理费单价
               ccr.month_manage_fee_contract,    -- 月物业管理费总额
               ccr.month_operate_fee_contract,   -- 月运营管理费总额
               ccr.month_fee_percent,            -- 扣点百分率
               ccr.incremental_rate_list,        -- 租金递增集合
               ccr.month_fee_percent_list,        -- 扣点百分率集合
               ccr.operate_incremental_rate_list, -- 月运营管理费递增集合
               ccr.manage_incremental_rate_list,  -- 月物业管理费递增集合
               ccr.decoration_property_fee,
               ccr.decoration_operate_fee,
               ccr.other_fee_contract,
               ccr.composite_manage_fee_contract,
               ccr.decoration_bail_fee_contract,
               ccr.rent_bail_fee_contract,
               ccr.operate_manage_bail_fee_contract,
               ccr.property_manage_bail_fee_contract,
               ccr.common_bail_fee_contract,
               cci.shop_type
        FROM som_commerce_contract cc
                 LEFT JOIN som_commerce_contract_info cci ON cc.contract_code = cci.contract_code
                 LEFT JOIN som_commerce_contract_rent ccr ON cc.contract_code = ccr.contract_code
        WHERE cc.del_status =0
          and cci.del_status=0
          and ccr.del_status=0
          AND cc.contract_code = #{contractCode}
    </select>

    <select id="getContractFeeList" resultType="com.seewin.som.commerce.resp.CommerceContractRentFeeExportVo">
        SELECT cc.tenant_id,                 -- 项目ID
           cc.room_shop_id store_id,         -- 门店ID
           cc.room_id,                       -- 房间ID
           cc.name,                          -- 房间名称
           cc.contract_code,                 -- 合同编号
           cc.rent_area,                     -- 计租面积
           cc.brand_id,                      -- 品牌ID
           cc.brand_name,                    -- 签约品牌名称
           cc.approve_status,                -- 审批状态
           cci.supplier_id,                  -- 签约供应商名称ID
           cci.supplier_name,                -- 签约供应商名称
           cci.rent_type,                    -- 租赁类型：1 纯租，2 纯扣，3 保底扣（租金），4 保底扣（租金+运营管理费+物业管理费）
           cci.rent_start_date,              -- 租赁起始时间
           cci.rent_end_date,                -- 租赁结束时间
           cci.rent_fee_date,                -- 计租日期
           cci.act_retrive_date,             -- 计费截止日期
           cci.rent_increase_mode,           -- 租金递增方式
           cci.points_mode,                  -- 扣点方式
           cci.operate_increase_mode,        -- 月运营管理费递增方式
           cci.manage_increase_mode,         -- 月物业管理费递增方式
           ccr.month_fee_contract,           -- 月租金总额
           ccr.guarant_sale_contract,        -- 月保底销售额
           ccr.month_manage_price_contract,  -- 物业管理费单价
           ccr.month_operate_price_contract, -- 运营管理费单价
           ccr.month_manage_fee_contract,    -- 月物业管理费总额
           ccr.month_operate_fee_contract,   -- 月运营管理费总额
           ccr.other_price_contract,         -- 其他费用单价
           ccr.other_fee_contract,           -- 其他费用总额
           ccr.month_fee_percent,            -- 扣点百分率
           ccr.incremental_rate_list,        -- 租金递增集合
           ccr.month_fee_percent_list,       -- 扣点百分率集合
           ccr.operate_incremental_rate_list, -- 月运营管理费递增集合
           ccr.manage_incremental_rate_list,  -- 月物业管理费递增集合
           ccr.fee_free,                      -- 装修免租期（天）
        cci.shop_type
       FROM som_commerce_contract cc
           LEFT JOIN som_commerce_contract_info cci ON cc.contract_code = cci.contract_code
           LEFT JOIN som_commerce_contract_rent ccr ON cc.contract_code = ccr.contract_code
       WHERE cc.del_status =0
       and cci.del_status=0
       and ccr.del_status=0
       and cc.tenant_id = #{dto.tenantId}
       and cci.rent_type != 2
       and (
        (#{dto.periodYearEnd} >= cci.rent_start_date and cci.rent_end_date >= #{dto.periodYearStart})
        or cci.rent_start_date between #{dto.periodYearStart} and #{dto.periodYearEnd}
        or cci.rent_end_date between #{dto.periodYearStart} and #{dto.periodYearEnd}
        )
       <if test="dto.name != null and dto.name != ''">
           and cc.name = #{dto.name}
       </if>
       ORDER BY cc.id desc
    </select>

    <select id="findSignContract" resultType="com.seewin.som.commerce.resp.CommerceContractArchiveVo">
        SELECT aa.*,e.id as entexitId FROM
        ( select c.*, ROW_NUMBER() OVER ( PARTITION BY c.room_id ORDER BY create_time DESC ) AS rn
        from som_commerce_contract c,som_commerce_contract_info i
        where c.tenant_id  = i.tenant_id
        and c.contract_code  = i.contract_code
        and c.room_id = #{roomId}
        and c.approve_status = 3
        and c.del_status = 0
        ) as aa left join som_commerce_entexit e on aa.tenant_id = e.tenant_id  and aa.room_id = e.shop_id and aa.room_shop_id = e.room_id  and e.del_status =0
        WHERE rn = 1
        LIMIT 1
    </select>
    <select id="listByReceivable" resultType="com.seewin.som.commerce.resp.CommerceChargeContractListVo">
        SELECT
        cc.id,
        cc.contract_code,
        cci.shop_type,
        cc.room_id,
        cc.room_shop_id,
        cc.name as room_name,
        cci.brand_name,
        cci.supplier_id,
        cci.supplier_name,
        cci.rent_start_date
        FROM
        som_commerce_contract cc
        LEFT JOIN som_commerce_contract_info cci ON cc.contract_code = cci.contract_code
        LEFT JOIN som_commerce_contract_rent ccr ON cc.contract_code = ccr.contract_code
        WHERE cc.del_status =0
          and cci.del_status=0
          and ccr.del_status=0 and cc.approve_status = 3
          and cc.tenant_id = #{dto.tenantId}
        <if test="dto.roomName != null and dto.roomName != ''">
            and cc.name like CONCAT('%',#{dto.roomName}, '%')
        </if>
        <if test="dto.brandName != null and dto.brandName != ''">
            and cci.brand_name like CONCAT('%',#{dto.brandName}, '%')
        </if>
        <if test="dto.brandId != null ">
            and cci.brand_id = #{dto.brandId}
        </if>
        <if test="dto.shopType != null ">
            and cci.shop_type =#{dto.shopType}
        </if>

        order by cc.approve_date desc
    </select>
    <select id="getAllInvertContract" resultType="com.seewin.som.commerce.resp.CommerceInvertContractListVo">
        SELECT t1.tenant_id,
               t1.fcode,
               t1.room_shop_id                             store_id,
               t2.rent_start_date,
               t3.visit_date,
               DATEDIFF(t2.rent_start_date, t3.visit_date) invest_cycle,
               t2.business_type
        FROM som_commerce_contract t1
                 JOIN som_commerce_contract_info t2 ON t1.contract_code = t2.contract_code
                 left JOIN (SELECT *
                       FROM (SELECT *, ROW_NUMBER() OVER ( PARTITION BY order_code ORDER BY visit_date ASC ) AS rn
                             FROM som_commerce_sitevisit
                             WHERE del_status = 0) t
                       WHERE rn = 1) t3 ON t1.order_code = t3.order_code
        WHERE t1.del_status = 0
          AND t1.approve_status = 3
    </select>

</mapper>

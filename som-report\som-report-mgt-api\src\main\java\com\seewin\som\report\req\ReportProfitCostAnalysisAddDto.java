package com.seewin.som.report.req;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 盈亏报告-成本分析
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Getter
@Setter
public class ReportProfitCostAnalysisAddDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 房间id
     */
    private String storeId;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 月度总成本（元/月）-本期数据
     */
    private BigDecimal monthTotal = BigDecimal.ZERO;

    /**
     * 月度总成本（元/月）-同比率
     */
    private BigDecimal monthTotalSameRatio = BigDecimal.ZERO;

    /**
     * 月度总成本（元/月）-环比率
     */
    private BigDecimal monthTotalRingRatio = BigDecimal.ZERO;

    /**
     * 人工成本（元/月）-本期数据
     */
    private BigDecimal laborCost = BigDecimal.ZERO;

    /**
     * 人工成本（元/月）-同比率
     */
    private BigDecimal laborCostSameRatio = BigDecimal.ZERO;

    /**
     * 人工成本（元/月）-环比率
     */
    private BigDecimal laborCostRingRatio = BigDecimal.ZERO;

    /**
     * 租管费（元/月）-本期数据
     */
    private BigDecimal rentPipe = BigDecimal.ZERO;

    /**
     * 租管费（元/月）-同比率
     */
    private BigDecimal rentPipeSameRatio = BigDecimal.ZERO;

    /**
     * 租管费（元/月）-环比率
     */
    private BigDecimal rentPipeRingRatio = BigDecimal.ZERO;

    /**
     * 水电费（元/月）-本期数据
     */
    private BigDecimal hydropowerFee = BigDecimal.ZERO;

    /**
     * 水电费（元/月）-同比率
     */
    private BigDecimal hydropowerFeeSameRatio = BigDecimal.ZERO;

    /**
     * 水电费（元/月）-环比率
     */
    private BigDecimal hydropowerFeeRingRatio = BigDecimal.ZERO;

    /**
     * 原材料费用（元/月）-本期数据
     */
    private BigDecimal materialCost = BigDecimal.ZERO;

    /**
     * 原材料费用（元/月）-同比率
     */
    private BigDecimal materialCostSameRatio = BigDecimal.ZERO;

    /**
     * 原材料费用（元/月）-环比率
     */
    private BigDecimal materialCostRingRatio = BigDecimal.ZERO;

    /**
     * 营销费用（元/月）-本期数据
     */
    private BigDecimal marketingExpenses = BigDecimal.ZERO;

    /**
     * 营销费用（元/月）-同比率
     */
    private BigDecimal marketingExpensesSameRatio = BigDecimal.ZERO;

    /**
     * 营销费用（元/月）-环比率
     */
    private BigDecimal marketingExpensesRingRatio = BigDecimal.ZERO;

    /**
     * 外卖平台费用（元/月）-本期数据
     */
    private BigDecimal takeoutFee = BigDecimal.ZERO;

    /**
     * 外卖平台费用（元/月）-同比率
     */
    private BigDecimal takeoutFeeSameRatio = BigDecimal.ZERO;

    /**
     * 外卖平台费用（元/月）-环比率
     */
    private BigDecimal takeoutFeeRingRatio = BigDecimal.ZERO;

    /**
     * 加盟管理费（元/月）-本期数据
     */
    private BigDecimal franchiseFee = BigDecimal.ZERO;

    /**
     * 加盟管理费（元/月）-同比率
     */
    private BigDecimal franchiseFeeSameRatio = BigDecimal.ZERO;

    /**
     * 加盟管理费（元/月）-环比率
     */
    private BigDecimal franchiseFeeRingRatio = BigDecimal.ZERO;

    /**
     * 税费（元/月）-本期数据
     */
    private BigDecimal tax = BigDecimal.ZERO;

    /**
     * 税费（元/月）-同比率
     */
    private BigDecimal taxSameRatio = BigDecimal.ZERO;

    /**
     * 税费（元/月）-环比率
     */
    private BigDecimal taxRingRatio = BigDecimal.ZERO;

    /**
     * 装修摊销费用（元/月）-本期数据
     */
    private BigDecimal decorationFee = BigDecimal.ZERO;

    /**
     * 装修摊销费用（元/月）-同比率
     */
    private BigDecimal decorationFeeSameRatio = BigDecimal.ZERO;

    /**
     * 装修摊销费用（元/月）-环比率
     */
    private BigDecimal decorationFeeRingRatio = BigDecimal.ZERO;

    /**
     * 固定资产折旧（元/月）-本期数据
     */
    private BigDecimal fixedDepreciation = BigDecimal.ZERO;

    /**
     * 固定资产折旧（元/月）-同比率
     */
    private BigDecimal fixedDepreciationSameRatio = BigDecimal.ZERO;

    /**
     * 固定资产折旧（元/月）-环比率
     */
    private BigDecimal fixedDepreciationRingRatio = BigDecimal.ZERO;

    /**
     * 品牌加盟费（元/月）-本期数据
     */
    private BigDecimal brandFee = BigDecimal.ZERO;

    /**
     * 品牌加盟费（元/月）-同比率
     */
    private BigDecimal brandFeeSameRatio = BigDecimal.ZERO;

    /**
     * 品牌加盟费（元/月）-环比率
     */
    private BigDecimal brandFeeRingRatio = BigDecimal.ZERO;

    /**
     * 其他成本-本期数据
     */
    private BigDecimal otherCosts = BigDecimal.ZERO;

    /**
     * 其他成本-同比率
     */
    private BigDecimal otherCostsSameRatio = BigDecimal.ZERO;

    /**
     * 其他成本-环比率
     */
    private BigDecimal otherCostsRingRatio = BigDecimal.ZERO;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 开始时间
     */
    private LocalDate startDate;
    /**
     * 结束时间
     */
    private LocalDate endDate;
}

package com.seewin.som.report.service;

import com.seewin.som.report.vo.req.MultiAnalyseReq;
import com.seewin.som.report.vo.resp.MultiAnalyseFlowResp;

public interface MultiAnalyseFlowService {
    /**
     * 图表
     */
    MultiAnalyseFlowResp flowAnalysis(MultiAnalyseReq req);
    /**
     * 图表
     */
    MultiAnalyseFlowResp flowDetail(MultiAnalyseReq req);
    /**
     * 明细导出
     */
    void expFlowDetail(MultiAnalyseReq req);

}
